{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\ApiConfigPanel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n_c = PanelContainer;\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c2 = PanelHeader;\nconst ApiIcon = styled.div`\n  width: 20px;\n  height: 20px;\n  background: #4bffb5;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #000;\n  font-weight: bold;\n`;\n_c3 = ApiIcon;\nconst ConfigForm = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n`;\n_c4 = ConfigForm;\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n`;\n_c5 = FormGroup;\nconst Label = styled.label`\n  color: #888;\n  font-size: 12px;\n  font-weight: 500;\n`;\n_c6 = Label;\nconst Input = styled.input`\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n    box-shadow: 0 0 0 2px rgba(75, 255, 181, 0.2);\n  }\n  \n  &::placeholder {\n    color: #666;\n  }\n`;\n_c7 = Input;\nconst Select = styled.select`\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n  }\n  \n  option {\n    background: #2a2a2a;\n    color: #fff;\n  }\n`;\n_c8 = Select;\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-top: 8px;\n`;\n_c9 = ButtonGroup;\nconst Button = styled.button`\n  flex: 1;\n  padding: 10px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.primary {\n    background: #4bffb5;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #3de89f;\n      transform: translateY(-1px);\n    }\n  }\n  \n  &.secondary {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n      border-color: #555;\n    }\n  }\n`;\n_c0 = Button;\nconst StatusIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  margin-bottom: 16px;\n  \n  &.connected {\n    background: rgba(75, 255, 181, 0.1);\n    border: 1px solid rgba(75, 255, 181, 0.3);\n    color: #4bffb5;\n  }\n  \n  &.disconnected {\n    background: rgba(255, 73, 118, 0.1);\n    border: 1px solid rgba(255, 73, 118, 0.3);\n    color: #ff4976;\n  }\n  \n  /* Mock data styling removed - Alpaca only implementation */\n`;\n_c1 = StatusIndicator;\nconst StatusDot = styled.div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: currentColor;\n`;\n_c10 = StatusDot;\nconst HelpText = styled.div`\n  font-size: 11px;\n  color: #666;\n  margin-top: 4px;\n  line-height: 1.4;\n`;\n_c11 = HelpText;\nconst FileUploadArea = styled.div`\n  border: 2px dashed ${props => props.$dragActive ? '#4bffb5' : '#444'};\n  border-radius: 8px;\n  padding: 24px;\n  text-align: center;\n  background: ${props => props.$dragActive ? 'rgba(75, 255, 181, 0.05)' : '#2a2a2a'};\n  transition: all 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    border-color: #4bffb5;\n    background: rgba(75, 255, 181, 0.05);\n  }\n`;\n_c12 = FileUploadArea;\nconst FileUploadIcon = styled.div`\n  font-size: 32px;\n  color: #666;\n  margin-bottom: 8px;\n`;\n_c13 = FileUploadIcon;\nconst FileUploadText = styled.div`\n  color: #888;\n  font-size: 14px;\n  margin-bottom: 4px;\n`;\n_c14 = FileUploadText;\nconst FileUploadSubtext = styled.div`\n  color: #666;\n  font-size: 12px;\n`;\n_c15 = FileUploadSubtext;\nconst HiddenFileInput = styled.input`\n  display: none;\n`;\n_c16 = HiddenFileInput;\nconst MethodToggle = styled.div`\n  display: flex;\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 4px;\n  margin-bottom: 16px;\n`;\n_c17 = MethodToggle;\nconst MethodButton = styled.button`\n  flex: 1;\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  background: ${props => props.$active ? '#4bffb5' : 'transparent'};\n  color: ${props => props.$active ? '#000' : '#888'};\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#4bffb5' : 'rgba(75, 255, 181, 0.1)'};\n    color: ${props => props.$active ? '#000' : '#4bffb5'};\n  }\n`;\n_c18 = MethodButton;\nconst JsonPreview = styled.div`\n  background: #1a1a1a;\n  border: 1px solid #333;\n  border-radius: 4px;\n  padding: 12px;\n  font-family: 'Courier New', monospace;\n  font-size: 11px;\n  color: #4bffb5;\n  margin-bottom: 16px;\n  max-height: 120px;\n  overflow-y: auto;\n`;\n_c19 = JsonPreview;\nconst ApiConfigPanel = ({\n  onApiUpdate,\n  currentStatus = 'live',\n  onDataSourceSwitch\n}) => {\n  _s();\n  // 🔥 PERSISTENCE: Load saved config from localStorage on component mount\n  const [config, setConfig] = useState(() => {\n    try {\n      const savedConfig = localStorage.getItem('autotradz_api_config');\n      if (savedConfig) {\n        const parsed = JSON.parse(savedConfig);\n        console.log('📱 Loaded saved API config from localStorage');\n        return {\n          apiKey: parsed.apiKey || '',\n          apiSecret: parsed.apiSecret || '',\n          paper: parsed.paper !== undefined ? parsed.paper : true\n        };\n      }\n    } catch (error) {\n      console.error('Error loading saved API config:', error);\n    }\n    return {\n      apiKey: '',\n      apiSecret: '',\n      paper: true\n    };\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [inputMethod, setInputMethod] = useState('manual'); // 'manual' or 'file'\n  const [dragActive, setDragActive] = useState(false);\n\n  // 🔥 PERSISTENCE: Auto-restore API config on component mount\n  React.useEffect(() => {\n    const savedConfig = localStorage.getItem('autotradz_api_config');\n    if (savedConfig && config.apiKey && config.apiSecret) {\n      // If we have saved config and it's loaded, try to restore the connection\n      console.log('🔄 Auto-restoring API connection from saved config');\n      // Optionally auto-configure the API on page load\n      // handleSubmit could be called here if you want automatic restoration\n    }\n  }, []);\n\n  // 🔥 PERSISTENCE: Clear saved config\n  const clearSavedConfig = () => {\n    try {\n      localStorage.removeItem('autotradz_api_config');\n      setConfig({\n        apiKey: '',\n        apiSecret: '',\n        paper: true\n      });\n      console.log('🗑️ Cleared saved API config');\n      alert('Saved API configuration cleared!');\n    } catch (error) {\n      console.error('Error clearing saved config:', error);\n    }\n  };\n\n  // 🔥 PERSISTENCE: Save config to localStorage whenever it changes\n  const saveConfigToStorage = newConfig => {\n    try {\n      localStorage.setItem('autotradz_api_config', JSON.stringify(newConfig));\n      console.log('💾 Saved API config to localStorage');\n    } catch (error) {\n      console.error('Error saving API config to localStorage:', error);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setConfig(prev => {\n      const newConfig = {\n        ...prev,\n        [field]: value\n      };\n      saveConfigToStorage(newConfig);\n      return newConfig;\n    });\n  };\n  const handleFileUpload = file => {\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = e => {\n      try {\n        const jsonData = JSON.parse(e.target.result);\n\n        // Support different JSON formats\n        let apiConfig = {};\n\n        // Format 1: Direct Alpaca format\n        if (jsonData.apiKey || jsonData.api_key) {\n          apiConfig = {\n            apiKey: jsonData.apiKey || jsonData.api_key,\n            apiSecret: jsonData.apiSecret || jsonData.api_secret || jsonData.secret,\n            paper: jsonData.paper !== undefined ? jsonData.paper : jsonData.sandbox !== undefined ? jsonData.sandbox : true\n          };\n        }\n        // Format 2: Legacy Coinbase CDP format (convert to Alpaca)\n        else if (jsonData.name && jsonData.privateKey) {\n          // This is a legacy Coinbase format - user needs to update to Alpaca\n          alert('⚠️ This appears to be a Coinbase API config. Please update to use Alpaca API credentials instead.');\n          return;\n        }\n        // Format 3: Nested format\n        else if (jsonData.alpaca || jsonData.api) {\n          const apiData = jsonData.alpaca || jsonData.api;\n          apiConfig = {\n            apiKey: apiData.apiKey || apiData.api_key || apiData.key,\n            apiSecret: apiData.apiSecret || apiData.api_secret || apiData.secret,\n            paper: apiData.paper !== undefined ? apiData.paper : apiData.sandbox !== undefined ? apiData.sandbox : true\n          };\n        }\n        if (apiConfig.apiKey && apiConfig.apiSecret) {\n          setConfig(apiConfig);\n          saveConfigToStorage(apiConfig); // 🔥 PERSISTENCE: Save to localStorage\n          alert('API configuration loaded successfully from JSON file!');\n        } else {\n          alert('Invalid JSON format. Please ensure the file contains apiKey and apiSecret fields.');\n        }\n      } catch (error) {\n        alert('Error parsing JSON file. Please check the file format.');\n        console.error('JSON parse error:', error);\n      }\n    };\n    reader.readAsText(file);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setDragActive(false);\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      const file = files[0];\n      if (file.type === 'application/json' || file.name.endsWith('.json')) {\n        handleFileUpload(file);\n      } else {\n        alert('Please upload a JSON file.');\n      }\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    setDragActive(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setDragActive(false);\n  };\n  const handleFileInputChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      handleFileUpload(file);\n    }\n  };\n  const downloadTemplate = () => {\n    const template = {\n      apiKey: \"PKTEST1234567890abcdef\",\n      apiSecret: \"your_alpaca_api_secret_here\",\n      paper: true,\n      _instructions: [\"1. Replace the values above with your actual Alpaca API credentials\", \"2. Get your API keys from: https://app.alpaca.markets/\", \"3. API Key format: PKTEST... (paper trading) or PK... (live trading)\", \"4. API Secret is a simple string (not PEM format like Coinbase)\", \"5. Set paper to true for paper trading, false for live trading\", \"6. Save this file and upload it to AutoTradz AI\", \"7. Delete this _instructions section before uploading (optional)\"]\n    };\n    const blob = new Blob([JSON.stringify(template, null, 2)], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'alpaca-api-config.json';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      // Send API configuration to backend\n      const response = await fetch('http://localhost:8000/api/configure', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(config)\n      });\n      if (response.ok) {\n        onApiUpdate && onApiUpdate(config);\n        setShowForm(false);\n        alert('API configuration updated successfully! You can now switch to live data.');\n\n        // Automatically switch to live data after successful configuration\n        if (onDataSourceSwitch) {\n          onDataSourceSwitch('live');\n        }\n      } else {\n        alert('Failed to update API configuration');\n      }\n    } catch (error) {\n      console.error('Error updating API config:', error);\n      alert('Error updating API configuration');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleTestConnection = async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/test-connection', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(config)\n      });\n      const result = await response.json();\n      if (response.ok && result.success) {\n        alert('Connection test successful!');\n      } else {\n        alert(`Connection test failed: ${result.error || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error testing connection:', error);\n      alert('Error testing connection');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleDataSourceSwitch = async dataSource => {\n    setIsLoading(true);\n    try {\n      const response = await fetch(`http://localhost:8000/api/switch-data-source?data_source=${dataSource}`, {\n        method: 'POST'\n      });\n      if (response.ok) {\n        const result = await response.json();\n        onDataSourceSwitch && onDataSourceSwitch(result.data_source);\n        alert(result.message);\n      } else {\n        alert('Failed to switch data source');\n      }\n    } catch (error) {\n      console.error('Error switching data source:', error);\n      alert('Error switching data source');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getStatusInfo = () => {\n    switch (currentStatus) {\n      case 'live':\n        return {\n          className: 'connected',\n          text: 'Live Data Connected',\n          description: 'Using real Coinbase market data'\n        };\n      case 'connected':\n        return {\n          className: 'connected',\n          text: 'API Connected',\n          description: 'Ready to use live data'\n        };\n      case 'disconnected':\n        return {\n          className: 'disconnected',\n          text: 'API Disconnected',\n          description: 'Check your API credentials'\n        };\n      default:\n        return {\n          className: 'disconnected',\n          text: 'Alpaca API Required',\n          description: 'Configure Alpaca API credentials'\n        };\n    }\n  };\n  const statusInfo = getStatusInfo();\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n      children: [/*#__PURE__*/_jsxDEV(ApiIcon, {\n        children: \"API\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this), \"Alpaca API Configuration\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n      className: statusInfo.className,\n      children: [/*#__PURE__*/_jsxDEV(StatusDot, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 600\n          },\n          children: statusInfo.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '10px',\n            opacity: 0.8\n          },\n          children: statusInfo.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this), !showForm ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(ButtonGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          className: \"primary\",\n          onClick: () => setShowForm(true),\n          children: config.apiKey ? '🔧 Update API' : 'Configure API'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          className: \"secondary\",\n          onClick: () => window.open('https://app.alpaca.markets/', '_blank'),\n          children: \"Get API Keys\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 11\n      }, this), config.apiKey && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"secondary\",\n          onClick: clearSavedConfig,\n          style: {\n            width: '100%',\n            fontSize: '12px',\n            padding: '6px 12px',\n            background: '#2a1a1a',\n            borderColor: '#444',\n            color: '#888'\n          },\n          children: \"\\uD83D\\uDDD1\\uFE0F Clear Saved Config\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 13\n      }, this), currentStatus === 'connected' || currentStatus === 'live' || currentStatus === 'websocket' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '12px'\n        },\n        children: /*#__PURE__*/_jsxDEV(ButtonGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            className: currentStatus === 'websocket' ? 'secondary' : 'primary',\n            onClick: () => handleDataSourceSwitch('websocket'),\n            disabled: isLoading || currentStatus === 'websocket',\n            children: currentStatus === 'websocket' ? '✅ WebSocket Live' : '🔌 Real-time WebSocket'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: currentStatus === 'live' ? 'secondary' : 'primary',\n            onClick: () => handleDataSourceSwitch('live'),\n            disabled: isLoading || currentStatus === 'live',\n            children: currentStatus === 'live' ? '✅ Live REST API' : '🔴 Switch to Live'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 13\n      }, this) : null]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(ConfigForm, {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(MethodToggle, {\n        children: [/*#__PURE__*/_jsxDEV(MethodButton, {\n          type: \"button\",\n          $active: inputMethod === 'manual',\n          onClick: () => setInputMethod('manual'),\n          children: \"Manual Entry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MethodButton, {\n          type: \"button\",\n          $active: inputMethod === 'file',\n          onClick: () => setInputMethod('file'),\n          children: \"JSON File Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 11\n      }, this), inputMethod === 'file' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(HiddenFileInput, {\n          type: \"file\",\n          accept: \".json\",\n          onChange: handleFileInputChange,\n          id: \"json-file-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(FileUploadArea, {\n          $dragActive: dragActive,\n          onDrop: handleDrop,\n          onDragOver: handleDragOver,\n          onDragLeave: handleDragLeave,\n          onClick: () => document.getElementById('json-file-input').click(),\n          children: [/*#__PURE__*/_jsxDEV(FileUploadIcon, {\n            children: \"\\uD83D\\uDCC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FileUploadText, {\n            children: dragActive ? 'Drop your JSON file here' : 'Click to upload or drag & drop'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FileUploadSubtext, {\n            children: \"Upload your Alpaca API credentials JSON file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(HelpText, {\n            children: \"Supported formats: Alpaca API export, custom JSON with apiKey/apiSecret fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '4px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              className: \"secondary\",\n              onClick: downloadTemplate,\n              style: {\n                padding: '4px 8px',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDCE5 Download Template\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              className: \"secondary\",\n              onClick: () => window.open('http://localhost:8000/api/json-format-example', '_blank'),\n              style: {\n                padding: '4px 8px',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDCCB Examples\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 15\n        }, this), config.apiKey && /*#__PURE__*/_jsxDEV(JsonPreview, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u2705 API Key: \", config.apiKey.substring(0, 20), \"...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u2705 API Secret: \", config.apiSecret ? '••••••••' : 'Missing']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\uD83C\\uDF0D Environment: \", config.paper ? 'Paper Trading' : 'Live Trading']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"API Key\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            placeholder: \"PKTEST1234567890abcdef (paper) or PK1234567890abcdef (live)\",\n            value: config.apiKey,\n            onChange: e => handleInputChange('apiKey', e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(HelpText, {\n            children: \"Format: PKTEST... (paper trading) or PK... (live trading)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"API Secret\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"password\",\n            placeholder: \"your_alpaca_api_secret_here\",\n            value: config.apiSecret,\n            onChange: e => handleInputChange('apiSecret', e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(HelpText, {\n            children: \"Your Alpaca API secret key (keep this secret!)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Trading Mode\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: config.paper,\n            onChange: e => handleInputChange('paper', e.target.value === 'true'),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"true\",\n              children: \"Paper Trading (Testing)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"false\",\n              children: \"Live Trading (Real Money)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(HelpText, {\n            children: \"Use paper trading for testing, live trading for real money\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          className: \"secondary\",\n          onClick: handleTestConnection,\n          disabled: isLoading || !config.apiKey || !config.apiSecret,\n          children: isLoading ? 'Testing...' : 'Test Connection'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          className: \"primary\",\n          disabled: isLoading || !config.apiKey || !config.apiSecret,\n          children: isLoading ? 'Saving...' : 'Save & Connect'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"button\",\n        className: \"secondary\",\n        onClick: () => setShowForm(false),\n        style: {\n          marginTop: '8px'\n        },\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 545,\n    columnNumber: 5\n  }, this);\n};\n_s(ApiConfigPanel, \"v5cyT4Tysmbrt598bCNuqsWmzwg=\");\n_c20 = ApiConfigPanel;\nexport default ApiConfigPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"PanelHeader\");\n$RefreshReg$(_c3, \"ApiIcon\");\n$RefreshReg$(_c4, \"ConfigForm\");\n$RefreshReg$(_c5, \"FormGroup\");\n$RefreshReg$(_c6, \"Label\");\n$RefreshReg$(_c7, \"Input\");\n$RefreshReg$(_c8, \"Select\");\n$RefreshReg$(_c9, \"ButtonGroup\");\n$RefreshReg$(_c0, \"Button\");\n$RefreshReg$(_c1, \"StatusIndicator\");\n$RefreshReg$(_c10, \"StatusDot\");\n$RefreshReg$(_c11, \"HelpText\");\n$RefreshReg$(_c12, \"FileUploadArea\");\n$RefreshReg$(_c13, \"FileUploadIcon\");\n$RefreshReg$(_c14, \"FileUploadText\");\n$RefreshReg$(_c15, \"FileUploadSubtext\");\n$RefreshReg$(_c16, \"HiddenFileInput\");\n$RefreshReg$(_c17, \"MethodToggle\");\n$RefreshReg$(_c18, \"MethodButton\");\n$RefreshReg$(_c19, \"JsonPreview\");\n$RefreshReg$(_c20, \"ApiConfigPanel\");", "map": {"version": 3, "names": ["React", "useState", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PanelContainer", "div", "_c", "PanelHeader", "h3", "_c2", "ApiIcon", "_c3", "ConfigForm", "form", "_c4", "FormGroup", "_c5", "Label", "label", "_c6", "Input", "input", "_c7", "Select", "select", "_c8", "ButtonGroup", "_c9", "<PERSON><PERSON>", "button", "_c0", "StatusIndicator", "_c1", "StatusDot", "_c10", "HelpText", "_c11", "FileUploadArea", "props", "$dragActive", "_c12", "FileUploadIcon", "_c13", "FileUploadText", "_c14", "FileUploadSubtext", "_c15", "HiddenFileInput", "_c16", "MethodToggle", "_c17", "MethodButton", "$active", "_c18", "JsonPreview", "_c19", "ApiConfigPanel", "onApiUpdate", "currentStatus", "onDataSourceSwitch", "_s", "config", "setConfig", "savedConfig", "localStorage", "getItem", "parsed", "JSON", "parse", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "apiSecret", "paper", "undefined", "error", "isLoading", "setIsLoading", "showForm", "setShowForm", "inputMethod", "setInputMethod", "dragActive", "setDragActive", "useEffect", "clearSavedConfig", "removeItem", "alert", "saveConfigToStorage", "newConfig", "setItem", "stringify", "handleInputChange", "field", "value", "prev", "handleFileUpload", "file", "reader", "FileReader", "onload", "e", "jsonData", "target", "result", "apiConfig", "api_key", "api_secret", "secret", "sandbox", "name", "privateKey", "alpaca", "api", "apiData", "key", "readAsText", "handleDrop", "preventDefault", "files", "dataTransfer", "length", "type", "endsWith", "handleDragOver", "handleDragLeave", "handleFileInputChange", "downloadTemplate", "template", "_instructions", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleSubmit", "response", "fetch", "method", "headers", "ok", "handleTestConnection", "json", "success", "handleDataSourceSwitch", "dataSource", "data_source", "message", "getStatusInfo", "className", "text", "description", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontWeight", "fontSize", "opacity", "onClick", "window", "open", "marginTop", "width", "padding", "background", "borderColor", "color", "disabled", "onSubmit", "accept", "onChange", "id", "onDrop", "onDragOver", "onDragLeave", "getElementById", "display", "justifyContent", "alignItems", "marginBottom", "gap", "substring", "placeholder", "required", "_c20", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/ApiConfigPanel.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\n\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst ApiIcon = styled.div`\n  width: 20px;\n  height: 20px;\n  background: #4bffb5;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #000;\n  font-weight: bold;\n`;\n\nconst ConfigForm = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n`;\n\nconst Label = styled.label`\n  color: #888;\n  font-size: 12px;\n  font-weight: 500;\n`;\n\nconst Input = styled.input`\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n    box-shadow: 0 0 0 2px rgba(75, 255, 181, 0.2);\n  }\n  \n  &::placeholder {\n    color: #666;\n  }\n`;\n\nconst Select = styled.select`\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n  }\n  \n  option {\n    background: #2a2a2a;\n    color: #fff;\n  }\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-top: 8px;\n`;\n\nconst Button = styled.button`\n  flex: 1;\n  padding: 10px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.primary {\n    background: #4bffb5;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #3de89f;\n      transform: translateY(-1px);\n    }\n  }\n  \n  &.secondary {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n      border-color: #555;\n    }\n  }\n`;\n\nconst StatusIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  margin-bottom: 16px;\n  \n  &.connected {\n    background: rgba(75, 255, 181, 0.1);\n    border: 1px solid rgba(75, 255, 181, 0.3);\n    color: #4bffb5;\n  }\n  \n  &.disconnected {\n    background: rgba(255, 73, 118, 0.1);\n    border: 1px solid rgba(255, 73, 118, 0.3);\n    color: #ff4976;\n  }\n  \n  /* Mock data styling removed - Alpaca only implementation */\n`;\n\nconst StatusDot = styled.div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: currentColor;\n`;\n\nconst HelpText = styled.div`\n  font-size: 11px;\n  color: #666;\n  margin-top: 4px;\n  line-height: 1.4;\n`;\n\nconst FileUploadArea = styled.div`\n  border: 2px dashed ${props => props.$dragActive ? '#4bffb5' : '#444'};\n  border-radius: 8px;\n  padding: 24px;\n  text-align: center;\n  background: ${props => props.$dragActive ? 'rgba(75, 255, 181, 0.05)' : '#2a2a2a'};\n  transition: all 0.2s ease;\n  cursor: pointer;\n\n  &:hover {\n    border-color: #4bffb5;\n    background: rgba(75, 255, 181, 0.05);\n  }\n`;\n\nconst FileUploadIcon = styled.div`\n  font-size: 32px;\n  color: #666;\n  margin-bottom: 8px;\n`;\n\nconst FileUploadText = styled.div`\n  color: #888;\n  font-size: 14px;\n  margin-bottom: 4px;\n`;\n\nconst FileUploadSubtext = styled.div`\n  color: #666;\n  font-size: 12px;\n`;\n\nconst HiddenFileInput = styled.input`\n  display: none;\n`;\n\nconst MethodToggle = styled.div`\n  display: flex;\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 4px;\n  margin-bottom: 16px;\n`;\n\nconst MethodButton = styled.button`\n  flex: 1;\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  background: ${props => props.$active ? '#4bffb5' : 'transparent'};\n  color: ${props => props.$active ? '#000' : '#888'};\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#4bffb5' : 'rgba(75, 255, 181, 0.1)'};\n    color: ${props => props.$active ? '#000' : '#4bffb5'};\n  }\n`;\n\nconst JsonPreview = styled.div`\n  background: #1a1a1a;\n  border: 1px solid #333;\n  border-radius: 4px;\n  padding: 12px;\n  font-family: 'Courier New', monospace;\n  font-size: 11px;\n  color: #4bffb5;\n  margin-bottom: 16px;\n  max-height: 120px;\n  overflow-y: auto;\n`;\n\nconst ApiConfigPanel = ({ onApiUpdate, currentStatus = 'live', onDataSourceSwitch }) => {\n  // 🔥 PERSISTENCE: Load saved config from localStorage on component mount\n  const [config, setConfig] = useState(() => {\n    try {\n      const savedConfig = localStorage.getItem('autotradz_api_config');\n      if (savedConfig) {\n        const parsed = JSON.parse(savedConfig);\n        console.log('📱 Loaded saved API config from localStorage');\n        return {\n          apiKey: parsed.apiKey || '',\n          apiSecret: parsed.apiSecret || '',\n          paper: parsed.paper !== undefined ? parsed.paper : true\n        };\n      }\n    } catch (error) {\n      console.error('Error loading saved API config:', error);\n    }\n    return {\n      apiKey: '',\n      apiSecret: '',\n      paper: true\n    };\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [inputMethod, setInputMethod] = useState('manual'); // 'manual' or 'file'\n  const [dragActive, setDragActive] = useState(false);\n\n  // 🔥 PERSISTENCE: Auto-restore API config on component mount\n  React.useEffect(() => {\n    const savedConfig = localStorage.getItem('autotradz_api_config');\n    if (savedConfig && config.apiKey && config.apiSecret) {\n      // If we have saved config and it's loaded, try to restore the connection\n      console.log('🔄 Auto-restoring API connection from saved config');\n      // Optionally auto-configure the API on page load\n      // handleSubmit could be called here if you want automatic restoration\n    }\n  }, []);\n\n  // 🔥 PERSISTENCE: Clear saved config\n  const clearSavedConfig = () => {\n    try {\n      localStorage.removeItem('autotradz_api_config');\n      setConfig({\n        apiKey: '',\n        apiSecret: '',\n        paper: true\n      });\n      console.log('🗑️ Cleared saved API config');\n      alert('Saved API configuration cleared!');\n    } catch (error) {\n      console.error('Error clearing saved config:', error);\n    }\n  };\n\n  // 🔥 PERSISTENCE: Save config to localStorage whenever it changes\n  const saveConfigToStorage = (newConfig) => {\n    try {\n      localStorage.setItem('autotradz_api_config', JSON.stringify(newConfig));\n      console.log('💾 Saved API config to localStorage');\n    } catch (error) {\n      console.error('Error saving API config to localStorage:', error);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setConfig(prev => {\n      const newConfig = { ...prev, [field]: value };\n      saveConfigToStorage(newConfig);\n      return newConfig;\n    });\n  };\n\n  const handleFileUpload = (file) => {\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const jsonData = JSON.parse(e.target.result);\n\n        // Support different JSON formats\n        let apiConfig = {};\n\n        // Format 1: Direct Alpaca format\n        if (jsonData.apiKey || jsonData.api_key) {\n          apiConfig = {\n            apiKey: jsonData.apiKey || jsonData.api_key,\n            apiSecret: jsonData.apiSecret || jsonData.api_secret || jsonData.secret,\n            paper: jsonData.paper !== undefined ? jsonData.paper : (jsonData.sandbox !== undefined ? jsonData.sandbox : true)\n          };\n        }\n        // Format 2: Legacy Coinbase CDP format (convert to Alpaca)\n        else if (jsonData.name && jsonData.privateKey) {\n          // This is a legacy Coinbase format - user needs to update to Alpaca\n          alert('⚠️ This appears to be a Coinbase API config. Please update to use Alpaca API credentials instead.');\n          return;\n        }\n        // Format 3: Nested format\n        else if (jsonData.alpaca || jsonData.api) {\n          const apiData = jsonData.alpaca || jsonData.api;\n          apiConfig = {\n            apiKey: apiData.apiKey || apiData.api_key || apiData.key,\n            apiSecret: apiData.apiSecret || apiData.api_secret || apiData.secret,\n            paper: apiData.paper !== undefined ? apiData.paper : (apiData.sandbox !== undefined ? apiData.sandbox : true)\n          };\n        }\n\n        if (apiConfig.apiKey && apiConfig.apiSecret) {\n          setConfig(apiConfig);\n          saveConfigToStorage(apiConfig); // 🔥 PERSISTENCE: Save to localStorage\n          alert('API configuration loaded successfully from JSON file!');\n        } else {\n          alert('Invalid JSON format. Please ensure the file contains apiKey and apiSecret fields.');\n        }\n\n      } catch (error) {\n        alert('Error parsing JSON file. Please check the file format.');\n        console.error('JSON parse error:', error);\n      }\n    };\n\n    reader.readAsText(file);\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    setDragActive(false);\n\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      const file = files[0];\n      if (file.type === 'application/json' || file.name.endsWith('.json')) {\n        handleFileUpload(file);\n      } else {\n        alert('Please upload a JSON file.');\n      }\n    }\n  };\n\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    setDragActive(true);\n  };\n\n  const handleDragLeave = (e) => {\n    e.preventDefault();\n    setDragActive(false);\n  };\n\n  const handleFileInputChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      handleFileUpload(file);\n    }\n  };\n\n  const downloadTemplate = () => {\n    const template = {\n      apiKey: \"PKTEST1234567890abcdef\",\n      apiSecret: \"your_alpaca_api_secret_here\",\n      paper: true,\n      _instructions: [\n        \"1. Replace the values above with your actual Alpaca API credentials\",\n        \"2. Get your API keys from: https://app.alpaca.markets/\",\n        \"3. API Key format: PKTEST... (paper trading) or PK... (live trading)\",\n        \"4. API Secret is a simple string (not PEM format like Coinbase)\",\n        \"5. Set paper to true for paper trading, false for live trading\",\n        \"6. Save this file and upload it to AutoTradz AI\",\n        \"7. Delete this _instructions section before uploading (optional)\"\n      ]\n    };\n\n    const blob = new Blob([JSON.stringify(template, null, 2)], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'alpaca-api-config.json';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsLoading(true);\n    \n    try {\n      // Send API configuration to backend\n      const response = await fetch('http://localhost:8000/api/configure', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(config),\n      });\n      \n      if (response.ok) {\n        onApiUpdate && onApiUpdate(config);\n        setShowForm(false);\n        alert('API configuration updated successfully! You can now switch to live data.');\n\n        // Automatically switch to live data after successful configuration\n        if (onDataSourceSwitch) {\n          onDataSourceSwitch('live');\n        }\n      } else {\n        alert('Failed to update API configuration');\n      }\n    } catch (error) {\n      console.error('Error updating API config:', error);\n      alert('Error updating API configuration');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleTestConnection = async () => {\n    setIsLoading(true);\n    \n    try {\n      const response = await fetch('http://localhost:8000/api/test-connection', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(config),\n      });\n      \n      const result = await response.json();\n      \n      if (response.ok && result.success) {\n        alert('Connection test successful!');\n      } else {\n        alert(`Connection test failed: ${result.error || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error testing connection:', error);\n      alert('Error testing connection');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDataSourceSwitch = async (dataSource) => {\n    setIsLoading(true);\n    try {\n      const response = await fetch(`http://localhost:8000/api/switch-data-source?data_source=${dataSource}`, {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        onDataSourceSwitch && onDataSourceSwitch(result.data_source);\n        alert(result.message);\n      } else {\n        alert('Failed to switch data source');\n      }\n    } catch (error) {\n      console.error('Error switching data source:', error);\n      alert('Error switching data source');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getStatusInfo = () => {\n    switch (currentStatus) {\n      case 'live':\n        return {\n          className: 'connected',\n          text: 'Live Data Connected',\n          description: 'Using real Coinbase market data'\n        };\n      case 'connected':\n        return {\n          className: 'connected',\n          text: 'API Connected',\n          description: 'Ready to use live data'\n        };\n      case 'disconnected':\n        return {\n          className: 'disconnected',\n          text: 'API Disconnected',\n          description: 'Check your API credentials'\n        };\n      default:\n        return {\n          className: 'disconnected',\n          text: 'Alpaca API Required',\n          description: 'Configure Alpaca API credentials'\n        };\n    }\n  };\n\n  const statusInfo = getStatusInfo();\n\n  return (\n    <PanelContainer>\n      <PanelHeader>\n        <ApiIcon>API</ApiIcon>\n        Alpaca API Configuration\n      </PanelHeader>\n      \n      <StatusIndicator className={statusInfo.className}>\n        <StatusDot />\n        <div>\n          <div style={{ fontWeight: 600 }}>{statusInfo.text}</div>\n          <div style={{ fontSize: '10px', opacity: 0.8 }}>{statusInfo.description}</div>\n        </div>\n      </StatusIndicator>\n      \n      {!showForm ? (\n        <>\n          <ButtonGroup>\n            <Button\n              className=\"primary\"\n              onClick={() => setShowForm(true)}\n            >\n              {config.apiKey ? '🔧 Update API' : 'Configure API'}\n            </Button>\n            <Button\n              className=\"secondary\"\n              onClick={() => window.open('https://app.alpaca.markets/', '_blank')}\n            >\n              Get API Keys\n            </Button>\n          </ButtonGroup>\n\n          {/* 🔥 PERSISTENCE: Show clear config button if config exists */}\n          {config.apiKey && (\n            <div style={{ marginTop: '8px' }}>\n              <Button\n                className=\"secondary\"\n                onClick={clearSavedConfig}\n                style={{\n                  width: '100%',\n                  fontSize: '12px',\n                  padding: '6px 12px',\n                  background: '#2a1a1a',\n                  borderColor: '#444',\n                  color: '#888'\n                }}\n              >\n                🗑️ Clear Saved Config\n              </Button>\n            </div>\n          )}\n\n          {currentStatus === 'connected' || currentStatus === 'live' || currentStatus === 'websocket' ? (\n            <div style={{ marginTop: '12px' }}>\n              <ButtonGroup>\n                <Button\n                  className={currentStatus === 'websocket' ? 'secondary' : 'primary'}\n                  onClick={() => handleDataSourceSwitch('websocket')}\n                  disabled={isLoading || currentStatus === 'websocket'}\n                >\n                  {currentStatus === 'websocket' ? '✅ WebSocket Live' : '🔌 Real-time WebSocket'}\n                </Button>\n                <Button\n                  className={currentStatus === 'live' ? 'secondary' : 'primary'}\n                  onClick={() => handleDataSourceSwitch('live')}\n                  disabled={isLoading || currentStatus === 'live'}\n                >\n                  {currentStatus === 'live' ? '✅ Live REST API' : '🔴 Switch to Live'}\n                </Button>\n                {/* Mock data option removed - Alpaca only implementation */}\n              </ButtonGroup>\n            </div>\n          ) : null}\n        </>\n      ) : (\n        <ConfigForm onSubmit={handleSubmit}>\n          <MethodToggle>\n            <MethodButton\n              type=\"button\"\n              $active={inputMethod === 'manual'}\n              onClick={() => setInputMethod('manual')}\n            >\n              Manual Entry\n            </MethodButton>\n            <MethodButton\n              type=\"button\"\n              $active={inputMethod === 'file'}\n              onClick={() => setInputMethod('file')}\n            >\n              JSON File Upload\n            </MethodButton>\n          </MethodToggle>\n\n          {inputMethod === 'file' ? (\n            <>\n              <HiddenFileInput\n                type=\"file\"\n                accept=\".json\"\n                onChange={handleFileInputChange}\n                id=\"json-file-input\"\n              />\n\n              <FileUploadArea\n                $dragActive={dragActive}\n                onDrop={handleDrop}\n                onDragOver={handleDragOver}\n                onDragLeave={handleDragLeave}\n                onClick={() => document.getElementById('json-file-input').click()}\n              >\n                <FileUploadIcon>📄</FileUploadIcon>\n                <FileUploadText>\n                  {dragActive ? 'Drop your JSON file here' : 'Click to upload or drag & drop'}\n                </FileUploadText>\n                <FileUploadSubtext>\n                  Upload your Alpaca API credentials JSON file\n                </FileUploadSubtext>\n              </FileUploadArea>\n\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>\n                <HelpText>\n                  Supported formats: Alpaca API export, custom JSON with apiKey/apiSecret fields\n                </HelpText>\n                <div style={{ display: 'flex', gap: '4px' }}>\n                  <Button\n                    type=\"button\"\n                    className=\"secondary\"\n                    onClick={downloadTemplate}\n                    style={{ padding: '4px 8px', fontSize: '11px' }}\n                  >\n                    📥 Download Template\n                  </Button>\n                  <Button\n                    type=\"button\"\n                    className=\"secondary\"\n                    onClick={() => window.open('http://localhost:8000/api/json-format-example', '_blank')}\n                    style={{ padding: '4px 8px', fontSize: '11px' }}\n                  >\n                    📋 Examples\n                  </Button>\n                </div>\n              </div>\n\n              {config.apiKey && (\n                <JsonPreview>\n                  <div>✅ API Key: {config.apiKey.substring(0, 20)}...</div>\n                  <div>✅ API Secret: {config.apiSecret ? '••••••••' : 'Missing'}</div>\n                  <div>🌍 Environment: {config.paper ? 'Paper Trading' : 'Live Trading'}</div>\n                </JsonPreview>\n              )}\n            </>\n          ) : (\n            <>\n              <FormGroup>\n                <Label>API Key</Label>\n                <Input\n                  type=\"text\"\n                  placeholder=\"PKTEST1234567890abcdef (paper) or PK1234567890abcdef (live)\"\n                  value={config.apiKey}\n                  onChange={(e) => handleInputChange('apiKey', e.target.value)}\n                  required\n                />\n                <HelpText>\n                  Format: PKTEST... (paper trading) or PK... (live trading)\n                </HelpText>\n              </FormGroup>\n\n              <FormGroup>\n                <Label>API Secret</Label>\n                <Input\n                  type=\"password\"\n                  placeholder=\"your_alpaca_api_secret_here\"\n                  value={config.apiSecret}\n                  onChange={(e) => handleInputChange('apiSecret', e.target.value)}\n                  required\n                />\n                <HelpText>\n                  Your Alpaca API secret key (keep this secret!)\n                </HelpText>\n              </FormGroup>\n\n              <FormGroup>\n                <Label>Trading Mode</Label>\n                <Select\n                  value={config.paper}\n                  onChange={(e) => handleInputChange('paper', e.target.value === 'true')}\n                >\n                  <option value=\"true\">Paper Trading (Testing)</option>\n                  <option value=\"false\">Live Trading (Real Money)</option>\n                </Select>\n                <HelpText>\n                  Use paper trading for testing, live trading for real money\n                </HelpText>\n              </FormGroup>\n            </>\n          )}\n          \n          <ButtonGroup>\n            <Button \n              type=\"button\"\n              className=\"secondary\" \n              onClick={handleTestConnection}\n              disabled={isLoading || !config.apiKey || !config.apiSecret}\n            >\n              {isLoading ? 'Testing...' : 'Test Connection'}\n            </Button>\n            <Button \n              type=\"submit\"\n              className=\"primary\"\n              disabled={isLoading || !config.apiKey || !config.apiSecret}\n            >\n              {isLoading ? 'Saving...' : 'Save & Connect'}\n            </Button>\n          </ButtonGroup>\n          \n          <Button \n            type=\"button\"\n            className=\"secondary\"\n            onClick={() => setShowForm(false)}\n            style={{ marginTop: '8px' }}\n          >\n            Cancel\n          </Button>\n        </ConfigForm>\n      )}\n    </PanelContainer>\n  );\n};\n\nexport default ApiConfigPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,cAAc,GAAGL,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,WAAW,GAAGR,MAAM,CAACS,EAAE;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,WAAW;AASjB,MAAMG,OAAO,GAAGX,MAAM,CAACM,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,OAAO;AAab,MAAME,UAAU,GAAGb,MAAM,CAACc,IAAI;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,SAAS,GAAGhB,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,SAAS;AAMf,MAAME,KAAK,GAAGlB,MAAM,CAACmB,KAAK;AAC1B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,KAAK,GAAGrB,MAAM,CAACsB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIF,KAAK;AAmBX,MAAMG,MAAM,GAAGxB,MAAM,CAACyB,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIF,MAAM;AAmBZ,MAAMG,WAAW,GAAG3B,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAJID,WAAW;AAMjB,MAAME,MAAM,GAAG7B,MAAM,CAAC8B,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnCIF,MAAM;AAqCZ,MAAMG,eAAe,GAAGhC,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GAtBID,eAAe;AAwBrB,MAAME,SAAS,GAAGlC,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGpC,MAAM,CAACM,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GALID,QAAQ;AAOd,MAAME,cAAc,GAAGtC,MAAM,CAACM,GAAG;AACjC,uBAAuBiC,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,SAAS,GAAG,MAAM;AACtE;AACA;AACA;AACA,gBAAgBD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,0BAA0B,GAAG,SAAS;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAbIH,cAAc;AAepB,MAAMI,cAAc,GAAG1C,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GAJID,cAAc;AAMpB,MAAME,cAAc,GAAG5C,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GAJID,cAAc;AAMpB,MAAME,iBAAiB,GAAG9C,MAAM,CAACM,GAAG;AACpC;AACA;AACA,CAAC;AAACyC,IAAA,GAHID,iBAAiB;AAKvB,MAAME,eAAe,GAAGhD,MAAM,CAACsB,KAAK;AACpC;AACA,CAAC;AAAC2B,IAAA,GAFID,eAAe;AAIrB,MAAME,YAAY,GAAGlD,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GANID,YAAY;AAQlB,MAAME,YAAY,GAAGpD,MAAM,CAAC8B,MAAM;AAClC;AACA;AACA;AACA;AACA,gBAAgBS,KAAK,IAAIA,KAAK,CAACc,OAAO,GAAG,SAAS,GAAG,aAAa;AAClE,WAAWd,KAAK,IAAIA,KAAK,CAACc,OAAO,GAAG,MAAM,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBd,KAAK,IAAIA,KAAK,CAACc,OAAO,GAAG,SAAS,GAAG,yBAAyB;AAChF,aAAad,KAAK,IAAIA,KAAK,CAACc,OAAO,GAAG,MAAM,GAAG,SAAS;AACxD;AACA,CAAC;AAACC,IAAA,GAhBIF,YAAY;AAkBlB,MAAMG,WAAW,GAAGvD,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkD,IAAA,GAXID,WAAW;AAajB,MAAME,cAAc,GAAGA,CAAC;EAAEC,WAAW;EAAEC,aAAa,GAAG,MAAM;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EACtF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhE,QAAQ,CAAC,MAAM;IACzC,IAAI;MACF,MAAMiE,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAChE,IAAIF,WAAW,EAAE;QACf,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;QACtCM,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3D,OAAO;UACLC,MAAM,EAAEL,MAAM,CAACK,MAAM,IAAI,EAAE;UAC3BC,SAAS,EAAEN,MAAM,CAACM,SAAS,IAAI,EAAE;UACjCC,KAAK,EAAEP,MAAM,CAACO,KAAK,KAAKC,SAAS,GAAGR,MAAM,CAACO,KAAK,GAAG;QACrD,CAAC;MACH;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;IACA,OAAO;MACLJ,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE;IACT,CAAC;EACH,CAAC,CAAC;EACF,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgF,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkF,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAD,KAAK,CAACuF,SAAS,CAAC,MAAM;IACpB,MAAMrB,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IAChE,IAAIF,WAAW,IAAIF,MAAM,CAACU,MAAM,IAAIV,MAAM,CAACW,SAAS,EAAE;MACpD;MACAH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE;MACA;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACFrB,YAAY,CAACsB,UAAU,CAAC,sBAAsB,CAAC;MAC/CxB,SAAS,CAAC;QACRS,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC,CAAC;MACFJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CiB,KAAK,CAAC,kCAAkC,CAAC;IAC3C,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMa,mBAAmB,GAAIC,SAAS,IAAK;IACzC,IAAI;MACFzB,YAAY,CAAC0B,OAAO,CAAC,sBAAsB,EAAEvB,IAAI,CAACwB,SAAS,CAACF,SAAS,CAAC,CAAC;MACvEpB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAClE;EACF,CAAC;EAED,MAAMiB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1ChC,SAAS,CAACiC,IAAI,IAAI;MAChB,MAAMN,SAAS,GAAG;QAAE,GAAGM,IAAI;QAAE,CAACF,KAAK,GAAGC;MAAM,CAAC;MAC7CN,mBAAmB,CAACC,SAAS,CAAC;MAC9B,OAAOA,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE;IAEX,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrB,IAAI;QACF,MAAMC,QAAQ,GAAGnC,IAAI,CAACC,KAAK,CAACiC,CAAC,CAACE,MAAM,CAACC,MAAM,CAAC;;QAE5C;QACA,IAAIC,SAAS,GAAG,CAAC,CAAC;;QAElB;QACA,IAAIH,QAAQ,CAAC/B,MAAM,IAAI+B,QAAQ,CAACI,OAAO,EAAE;UACvCD,SAAS,GAAG;YACVlC,MAAM,EAAE+B,QAAQ,CAAC/B,MAAM,IAAI+B,QAAQ,CAACI,OAAO;YAC3ClC,SAAS,EAAE8B,QAAQ,CAAC9B,SAAS,IAAI8B,QAAQ,CAACK,UAAU,IAAIL,QAAQ,CAACM,MAAM;YACvEnC,KAAK,EAAE6B,QAAQ,CAAC7B,KAAK,KAAKC,SAAS,GAAG4B,QAAQ,CAAC7B,KAAK,GAAI6B,QAAQ,CAACO,OAAO,KAAKnC,SAAS,GAAG4B,QAAQ,CAACO,OAAO,GAAG;UAC9G,CAAC;QACH;QACA;QAAA,KACK,IAAIP,QAAQ,CAACQ,IAAI,IAAIR,QAAQ,CAACS,UAAU,EAAE;UAC7C;UACAxB,KAAK,CAAC,mGAAmG,CAAC;UAC1G;QACF;QACA;QAAA,KACK,IAAIe,QAAQ,CAACU,MAAM,IAAIV,QAAQ,CAACW,GAAG,EAAE;UACxC,MAAMC,OAAO,GAAGZ,QAAQ,CAACU,MAAM,IAAIV,QAAQ,CAACW,GAAG;UAC/CR,SAAS,GAAG;YACVlC,MAAM,EAAE2C,OAAO,CAAC3C,MAAM,IAAI2C,OAAO,CAACR,OAAO,IAAIQ,OAAO,CAACC,GAAG;YACxD3C,SAAS,EAAE0C,OAAO,CAAC1C,SAAS,IAAI0C,OAAO,CAACP,UAAU,IAAIO,OAAO,CAACN,MAAM;YACpEnC,KAAK,EAAEyC,OAAO,CAACzC,KAAK,KAAKC,SAAS,GAAGwC,OAAO,CAACzC,KAAK,GAAIyC,OAAO,CAACL,OAAO,KAAKnC,SAAS,GAAGwC,OAAO,CAACL,OAAO,GAAG;UAC1G,CAAC;QACH;QAEA,IAAIJ,SAAS,CAAClC,MAAM,IAAIkC,SAAS,CAACjC,SAAS,EAAE;UAC3CV,SAAS,CAAC2C,SAAS,CAAC;UACpBjB,mBAAmB,CAACiB,SAAS,CAAC,CAAC,CAAC;UAChClB,KAAK,CAAC,uDAAuD,CAAC;QAChE,CAAC,MAAM;UACLA,KAAK,CAAC,mFAAmF,CAAC;QAC5F;MAEF,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdY,KAAK,CAAC,wDAAwD,CAAC;QAC/DlB,OAAO,CAACM,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC3C;IACF,CAAC;IAEDuB,MAAM,CAACkB,UAAU,CAACnB,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,UAAU,GAAIhB,CAAC,IAAK;IACxBA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBnC,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMoC,KAAK,GAAGlB,CAAC,CAACmB,YAAY,CAACD,KAAK;IAClC,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MACpB,MAAMxB,IAAI,GAAGsB,KAAK,CAAC,CAAC,CAAC;MACrB,IAAItB,IAAI,CAACyB,IAAI,KAAK,kBAAkB,IAAIzB,IAAI,CAACa,IAAI,CAACa,QAAQ,CAAC,OAAO,CAAC,EAAE;QACnE3B,gBAAgB,CAACC,IAAI,CAAC;MACxB,CAAC,MAAM;QACLV,KAAK,CAAC,4BAA4B,CAAC;MACrC;IACF;EACF,CAAC;EAED,MAAMqC,cAAc,GAAIvB,CAAC,IAAK;IAC5BA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBnC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM0C,eAAe,GAAIxB,CAAC,IAAK;IAC7BA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBnC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM2C,qBAAqB,GAAIzB,CAAC,IAAK;IACnC,MAAMJ,IAAI,GAAGI,CAAC,CAACE,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAItB,IAAI,EAAE;MACRD,gBAAgB,CAACC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAM8B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,QAAQ,GAAG;MACfzD,MAAM,EAAE,wBAAwB;MAChCC,SAAS,EAAE,6BAA6B;MACxCC,KAAK,EAAE,IAAI;MACXwD,aAAa,EAAE,CACb,qEAAqE,EACrE,wDAAwD,EACxD,sEAAsE,EACtE,iEAAiE,EACjE,gEAAgE,EAChE,iDAAiD,EACjD,kEAAkE;IAEtE,CAAC;IAED,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAChE,IAAI,CAACwB,SAAS,CAACqC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;MAAEN,IAAI,EAAE;IAAmB,CAAC,CAAC;IACxF,MAAMU,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,wBAAwB;IACrCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMa,YAAY,GAAG,MAAO5C,CAAC,IAAK;IAChCA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBzC,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAMqE,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDT,IAAI,EAAEzE,IAAI,CAACwB,SAAS,CAAC9B,MAAM;MAC7B,CAAC,CAAC;MAEF,IAAIqF,QAAQ,CAACI,EAAE,EAAE;QACf7F,WAAW,IAAIA,WAAW,CAACI,MAAM,CAAC;QAClCkB,WAAW,CAAC,KAAK,CAAC;QAClBQ,KAAK,CAAC,0EAA0E,CAAC;;QAEjF;QACA,IAAI5B,kBAAkB,EAAE;UACtBA,kBAAkB,CAAC,MAAM,CAAC;QAC5B;MACF,CAAC,MAAM;QACL4B,KAAK,CAAC,oCAAoC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDY,KAAK,CAAC,kCAAkC,CAAC;IAC3C,CAAC,SAAS;MACRV,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM0E,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC1E,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMqE,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;QACxEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDT,IAAI,EAAEzE,IAAI,CAACwB,SAAS,CAAC9B,MAAM;MAC7B,CAAC,CAAC;MAEF,MAAM2C,MAAM,GAAG,MAAM0C,QAAQ,CAACM,IAAI,CAAC,CAAC;MAEpC,IAAIN,QAAQ,CAACI,EAAE,IAAI9C,MAAM,CAACiD,OAAO,EAAE;QACjClE,KAAK,CAAC,6BAA6B,CAAC;MACtC,CAAC,MAAM;QACLA,KAAK,CAAC,2BAA2BiB,MAAM,CAAC7B,KAAK,IAAI,eAAe,EAAE,CAAC;MACrE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDY,KAAK,CAAC,0BAA0B,CAAC;IACnC,CAAC,SAAS;MACRV,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM6E,sBAAsB,GAAG,MAAOC,UAAU,IAAK;IACnD9E,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMqE,QAAQ,GAAG,MAAMC,KAAK,CAAC,4DAA4DQ,UAAU,EAAE,EAAE;QACrGP,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACI,EAAE,EAAE;QACf,MAAM9C,MAAM,GAAG,MAAM0C,QAAQ,CAACM,IAAI,CAAC,CAAC;QACpC7F,kBAAkB,IAAIA,kBAAkB,CAAC6C,MAAM,CAACoD,WAAW,CAAC;QAC5DrE,KAAK,CAACiB,MAAM,CAACqD,OAAO,CAAC;MACvB,CAAC,MAAM;QACLtE,KAAK,CAAC,8BAA8B,CAAC;MACvC;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDY,KAAK,CAAC,6BAA6B,CAAC;IACtC,CAAC,SAAS;MACRV,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiF,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQpG,aAAa;MACnB,KAAK,MAAM;QACT,OAAO;UACLqG,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,WAAW;QACd,OAAO;UACLF,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,eAAe;UACrBC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,cAAc;QACjB,OAAO;UACLF,SAAS,EAAE,cAAc;UACzBC,IAAI,EAAE,kBAAkB;UACxBC,WAAW,EAAE;QACf,CAAC;MACH;QACE,OAAO;UACLF,SAAS,EAAE,cAAc;UACzBC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE;QACf,CAAC;IACL;EACF,CAAC;EAED,MAAMC,UAAU,GAAGJ,aAAa,CAAC,CAAC;EAElC,oBACE7J,OAAA,CAACG,cAAc;IAAA+J,QAAA,gBACblK,OAAA,CAACM,WAAW;MAAA4J,QAAA,gBACVlK,OAAA,CAACS,OAAO;QAAAyJ,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,4BAExB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAEdtK,OAAA,CAAC8B,eAAe;MAACgI,SAAS,EAAEG,UAAU,CAACH,SAAU;MAAAI,QAAA,gBAC/ClK,OAAA,CAACgC,SAAS;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbtK,OAAA;QAAAkK,QAAA,gBACElK,OAAA;UAAKuK,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE;UAAAN,QAAA,EAAED,UAAU,CAACF;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDtK,OAAA;UAAKuK,KAAK,EAAE;YAAEE,QAAQ,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAR,QAAA,EAAED,UAAU,CAACD;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,EAEjB,CAACzF,QAAQ,gBACR7E,OAAA,CAAAE,SAAA;MAAAgK,QAAA,gBACElK,OAAA,CAACyB,WAAW;QAAAyI,QAAA,gBACVlK,OAAA,CAAC2B,MAAM;UACLmI,SAAS,EAAC,SAAS;UACnBa,OAAO,EAAEA,CAAA,KAAM7F,WAAW,CAAC,IAAI,CAAE;UAAAoF,QAAA,EAEhCtG,MAAM,CAACU,MAAM,GAAG,eAAe,GAAG;QAAe;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACTtK,OAAA,CAAC2B,MAAM;UACLmI,SAAS,EAAC,WAAW;UACrBa,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAAC,6BAA6B,EAAE,QAAQ,CAAE;UAAAX,QAAA,EACrE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGb1G,MAAM,CAACU,MAAM,iBACZtE,OAAA;QAAKuK,KAAK,EAAE;UAAEO,SAAS,EAAE;QAAM,CAAE;QAAAZ,QAAA,eAC/BlK,OAAA,CAAC2B,MAAM;UACLmI,SAAS,EAAC,WAAW;UACrBa,OAAO,EAAEvF,gBAAiB;UAC1BmF,KAAK,EAAE;YACLQ,KAAK,EAAE,MAAM;YACbN,QAAQ,EAAE,MAAM;YAChBO,OAAO,EAAE,UAAU;YACnBC,UAAU,EAAE,SAAS;YACrBC,WAAW,EAAE,MAAM;YACnBC,KAAK,EAAE;UACT,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEA7G,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,WAAW,gBACzFzD,OAAA;QAAKuK,KAAK,EAAE;UAAEO,SAAS,EAAE;QAAO,CAAE;QAAAZ,QAAA,eAChClK,OAAA,CAACyB,WAAW;UAAAyI,QAAA,gBACVlK,OAAA,CAAC2B,MAAM;YACLmI,SAAS,EAAErG,aAAa,KAAK,WAAW,GAAG,WAAW,GAAG,SAAU;YACnEkH,OAAO,EAAEA,CAAA,KAAMlB,sBAAsB,CAAC,WAAW,CAAE;YACnD2B,QAAQ,EAAEzG,SAAS,IAAIlB,aAAa,KAAK,WAAY;YAAAyG,QAAA,EAEpDzG,aAAa,KAAK,WAAW,GAAG,kBAAkB,GAAG;UAAwB;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACTtK,OAAA,CAAC2B,MAAM;YACLmI,SAAS,EAAErG,aAAa,KAAK,MAAM,GAAG,WAAW,GAAG,SAAU;YAC9DkH,OAAO,EAAEA,CAAA,KAAMlB,sBAAsB,CAAC,MAAM,CAAE;YAC9C2B,QAAQ,EAAEzG,SAAS,IAAIlB,aAAa,KAAK,MAAO;YAAAyG,QAAA,EAE/CzG,aAAa,KAAK,MAAM,GAAG,iBAAiB,GAAG;UAAmB;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,GACJ,IAAI;IAAA,eACR,CAAC,gBAEHtK,OAAA,CAACW,UAAU;MAAC0K,QAAQ,EAAErC,YAAa;MAAAkB,QAAA,gBACjClK,OAAA,CAACgD,YAAY;QAAAkH,QAAA,gBACXlK,OAAA,CAACkD,YAAY;UACXuE,IAAI,EAAC,QAAQ;UACbtE,OAAO,EAAE4B,WAAW,KAAK,QAAS;UAClC4F,OAAO,EAAEA,CAAA,KAAM3F,cAAc,CAAC,QAAQ,CAAE;UAAAkF,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACftK,OAAA,CAACkD,YAAY;UACXuE,IAAI,EAAC,QAAQ;UACbtE,OAAO,EAAE4B,WAAW,KAAK,MAAO;UAChC4F,OAAO,EAAEA,CAAA,KAAM3F,cAAc,CAAC,MAAM,CAAE;UAAAkF,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEdvF,WAAW,KAAK,MAAM,gBACrB/E,OAAA,CAAAE,SAAA;QAAAgK,QAAA,gBACElK,OAAA,CAAC8C,eAAe;UACd2E,IAAI,EAAC,MAAM;UACX6D,MAAM,EAAC,OAAO;UACdC,QAAQ,EAAE1D,qBAAsB;UAChC2D,EAAE,EAAC;QAAiB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEFtK,OAAA,CAACoC,cAAc;UACbE,WAAW,EAAE2C,UAAW;UACxBwG,MAAM,EAAErE,UAAW;UACnBsE,UAAU,EAAE/D,cAAe;UAC3BgE,WAAW,EAAE/D,eAAgB;UAC7B+C,OAAO,EAAEA,CAAA,KAAMpC,QAAQ,CAACqD,cAAc,CAAC,iBAAiB,CAAC,CAAC/C,KAAK,CAAC,CAAE;UAAAqB,QAAA,gBAElElK,OAAA,CAACwC,cAAc;YAAA0H,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC,eACnCtK,OAAA,CAAC0C,cAAc;YAAAwH,QAAA,EACZjF,UAAU,GAAG,0BAA0B,GAAG;UAAgC;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACjBtK,OAAA,CAAC4C,iBAAiB;YAAAsH,QAAA,EAAC;UAEnB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEjBtK,OAAA;UAAKuK,KAAK,EAAE;YAAEsB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAA9B,QAAA,gBAC1GlK,OAAA,CAACkC,QAAQ;YAAAgI,QAAA,EAAC;UAEV;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXtK,OAAA;YAAKuK,KAAK,EAAE;cAAEsB,OAAO,EAAE,MAAM;cAAEI,GAAG,EAAE;YAAM,CAAE;YAAA/B,QAAA,gBAC1ClK,OAAA,CAAC2B,MAAM;cACL8F,IAAI,EAAC,QAAQ;cACbqC,SAAS,EAAC,WAAW;cACrBa,OAAO,EAAE7C,gBAAiB;cAC1ByC,KAAK,EAAE;gBAAES,OAAO,EAAE,SAAS;gBAAEP,QAAQ,EAAE;cAAO,CAAE;cAAAP,QAAA,EACjD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtK,OAAA,CAAC2B,MAAM;cACL8F,IAAI,EAAC,QAAQ;cACbqC,SAAS,EAAC,WAAW;cACrBa,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAAC,+CAA+C,EAAE,QAAQ,CAAE;cACtFN,KAAK,EAAE;gBAAES,OAAO,EAAE,SAAS;gBAAEP,QAAQ,EAAE;cAAO,CAAE;cAAAP,QAAA,EACjD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL1G,MAAM,CAACU,MAAM,iBACZtE,OAAA,CAACqD,WAAW;UAAA6G,QAAA,gBACVlK,OAAA;YAAAkK,QAAA,GAAK,kBAAW,EAACtG,MAAM,CAACU,MAAM,CAAC4H,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAAG;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzDtK,OAAA;YAAAkK,QAAA,GAAK,qBAAc,EAACtG,MAAM,CAACW,SAAS,GAAG,UAAU,GAAG,SAAS;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpEtK,OAAA;YAAAkK,QAAA,GAAK,4BAAgB,EAACtG,MAAM,CAACY,KAAK,GAAG,eAAe,GAAG,cAAc;UAAA;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CACd;MAAA,eACD,CAAC,gBAEHtK,OAAA,CAAAE,SAAA;QAAAgK,QAAA,gBACElK,OAAA,CAACc,SAAS;UAAAoJ,QAAA,gBACRlK,OAAA,CAACgB,KAAK;YAAAkJ,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtBtK,OAAA,CAACmB,KAAK;YACJsG,IAAI,EAAC,MAAM;YACX0E,WAAW,EAAC,6DAA6D;YACzEtG,KAAK,EAAEjC,MAAM,CAACU,MAAO;YACrBiH,QAAQ,EAAGnF,CAAC,IAAKT,iBAAiB,CAAC,QAAQ,EAAES,CAAC,CAACE,MAAM,CAACT,KAAK,CAAE;YAC7DuG,QAAQ;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFtK,OAAA,CAACkC,QAAQ;YAAAgI,QAAA,EAAC;UAEV;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEZtK,OAAA,CAACc,SAAS;UAAAoJ,QAAA,gBACRlK,OAAA,CAACgB,KAAK;YAAAkJ,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzBtK,OAAA,CAACmB,KAAK;YACJsG,IAAI,EAAC,UAAU;YACf0E,WAAW,EAAC,6BAA6B;YACzCtG,KAAK,EAAEjC,MAAM,CAACW,SAAU;YACxBgH,QAAQ,EAAGnF,CAAC,IAAKT,iBAAiB,CAAC,WAAW,EAAES,CAAC,CAACE,MAAM,CAACT,KAAK,CAAE;YAChEuG,QAAQ;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFtK,OAAA,CAACkC,QAAQ;YAAAgI,QAAA,EAAC;UAEV;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEZtK,OAAA,CAACc,SAAS;UAAAoJ,QAAA,gBACRlK,OAAA,CAACgB,KAAK;YAAAkJ,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3BtK,OAAA,CAACsB,MAAM;YACLuE,KAAK,EAAEjC,MAAM,CAACY,KAAM;YACpB+G,QAAQ,EAAGnF,CAAC,IAAKT,iBAAiB,CAAC,OAAO,EAAES,CAAC,CAACE,MAAM,CAACT,KAAK,KAAK,MAAM,CAAE;YAAAqE,QAAA,gBAEvElK,OAAA;cAAQ6F,KAAK,EAAC,MAAM;cAAAqE,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrDtK,OAAA;cAAQ6F,KAAK,EAAC,OAAO;cAAAqE,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACTtK,OAAA,CAACkC,QAAQ;YAAAgI,QAAA,EAAC;UAEV;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,eACZ,CACH,eAEDtK,OAAA,CAACyB,WAAW;QAAAyI,QAAA,gBACVlK,OAAA,CAAC2B,MAAM;UACL8F,IAAI,EAAC,QAAQ;UACbqC,SAAS,EAAC,WAAW;UACrBa,OAAO,EAAErB,oBAAqB;UAC9B8B,QAAQ,EAAEzG,SAAS,IAAI,CAACf,MAAM,CAACU,MAAM,IAAI,CAACV,MAAM,CAACW,SAAU;UAAA2F,QAAA,EAE1DvF,SAAS,GAAG,YAAY,GAAG;QAAiB;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACTtK,OAAA,CAAC2B,MAAM;UACL8F,IAAI,EAAC,QAAQ;UACbqC,SAAS,EAAC,SAAS;UACnBsB,QAAQ,EAAEzG,SAAS,IAAI,CAACf,MAAM,CAACU,MAAM,IAAI,CAACV,MAAM,CAACW,SAAU;UAAA2F,QAAA,EAE1DvF,SAAS,GAAG,WAAW,GAAG;QAAgB;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEdtK,OAAA,CAAC2B,MAAM;QACL8F,IAAI,EAAC,QAAQ;QACbqC,SAAS,EAAC,WAAW;QACrBa,OAAO,EAAEA,CAAA,KAAM7F,WAAW,CAAC,KAAK,CAAE;QAClCyF,KAAK,EAAE;UAAEO,SAAS,EAAE;QAAM,CAAE;QAAAZ,QAAA,EAC7B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAAC3G,EAAA,CA5gBIJ,cAAc;AAAA8I,IAAA,GAAd9I,cAAc;AA8gBpB,eAAeA,cAAc;AAAC,IAAAlD,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA+I,IAAA;AAAAC,YAAA,CAAAjM,EAAA;AAAAiM,YAAA,CAAA9L,GAAA;AAAA8L,YAAA,CAAA5L,GAAA;AAAA4L,YAAA,CAAAzL,GAAA;AAAAyL,YAAA,CAAAvL,GAAA;AAAAuL,YAAA,CAAApL,GAAA;AAAAoL,YAAA,CAAAjL,GAAA;AAAAiL,YAAA,CAAA9K,GAAA;AAAA8K,YAAA,CAAA5K,GAAA;AAAA4K,YAAA,CAAAzK,GAAA;AAAAyK,YAAA,CAAAvK,GAAA;AAAAuK,YAAA,CAAArK,IAAA;AAAAqK,YAAA,CAAAnK,IAAA;AAAAmK,YAAA,CAAA/J,IAAA;AAAA+J,YAAA,CAAA7J,IAAA;AAAA6J,YAAA,CAAA3J,IAAA;AAAA2J,YAAA,CAAAzJ,IAAA;AAAAyJ,YAAA,CAAAvJ,IAAA;AAAAuJ,YAAA,CAAArJ,IAAA;AAAAqJ,YAAA,CAAAlJ,IAAA;AAAAkJ,YAAA,CAAAhJ,IAAA;AAAAgJ,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}