{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\SimpleChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { createChart, ColorType } from 'lightweight-charts';\nimport styled from 'styled-components';\n\n// Simple cache for market data to speed up chart loading\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst dataCache = new Map();\nconst CACHE_DURATION = 30000; // 30 seconds\n\nconst getCacheKey = (symbol, timeframe) => `${symbol}-${timeframe}`;\nconst getCachedData = (symbol, timeframe) => {\n  const key = getCacheKey(symbol, timeframe);\n  const cached = dataCache.get(key);\n  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n    console.log(`📦 Using cached data for ${symbol} ${timeframe}`);\n    return cached.data;\n  }\n  return null;\n};\nconst setCachedData = (symbol, timeframe, data) => {\n  const key = getCacheKey(symbol, timeframe);\n  dataCache.set(key, {\n    data,\n    timestamp: Date.now()\n  });\n  console.log(`💾 Cached data for ${symbol} ${timeframe}: ${data.length} points`);\n};\nconst ChartContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n`;\n_c = ChartContainer;\nconst ChartHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  color: #ffffff;\n`;\n_c2 = ChartHeader;\nconst TimeframeSelector = styled.div`\n  display: flex;\n  gap: 8px;\n  align-items: center;\n`;\n_c3 = TimeframeSelector;\nconst TimeframeButton = styled.button`\n  background: ${props => props.$active ? '#4bffb5' : 'transparent'};\n  color: ${props => props.$active ? '#000' : '#fff'};\n  border: 1px solid ${props => props.$active ? '#4bffb5' : '#555'};\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s;\n\n  &:hover {\n    background: ${props => props.$active ? '#4bffb5' : '#333'};\n  }\n`;\n_c4 = TimeframeButton;\nconst PriceInfo = styled.div`\n  display: flex;\n  gap: 20px;\n  font-size: 14px;\n`;\n_c5 = PriceInfo;\nconst PriceItem = styled.div`\n  display: flex;\n  flex-direction: column;\n\n  .label {\n    color: #888;\n    font-size: 12px;\n  }\n\n  .value {\n    color: ${props => props.color || '#fff'};\n    font-weight: bold;\n  }\n`;\n_c6 = PriceItem;\nconst SimpleChart = ({\n  marketData,\n  signals,\n  symbol = 'XRP/USD'\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n  const [currentPrice, setCurrentPrice] = useState(null);\n  const [priceChange, setPriceChange] = useState(0);\n  const [selectedTimeframe, setSelectedTimeframe] = useState('1m');\n  const [chartData, setChartData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const timeframes = [{\n    label: '1m',\n    value: '1m'\n  }, {\n    label: '5m',\n    value: '5m'\n  }, {\n    label: '15m',\n    value: '15m'\n  }, {\n    label: '1h',\n    value: '1h'\n  }, {\n    label: '4h',\n    value: '4h'\n  }, {\n    label: '1d',\n    value: '1d'\n  }];\n\n  // Initialize chart\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n    try {\n      // Create chart\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          background: {\n            type: ColorType.Solid,\n            color: '#1a1a1a'\n          },\n          textColor: '#ffffff'\n        },\n        grid: {\n          vertLines: {\n            color: '#2B2B43'\n          },\n          horzLines: {\n            color: '#2B2B43'\n          }\n        },\n        crosshair: {\n          mode: 1\n        },\n        rightPriceScale: {\n          borderColor: '#485c7b'\n        },\n        timeScale: {\n          borderColor: '#485c7b',\n          timeVisible: true,\n          secondsVisible: false\n        }\n      });\n\n      // Add candlestick series with TradingView colors (green bullish, red bearish)\n      candlestickSeries.current = chart.current.addCandlestickSeries({\n        upColor: '#26a69a',\n        // Green for bullish candles (TradingView style)\n        downColor: '#ef5350',\n        // Red for bearish candles (TradingView style)\n        borderDownColor: '#ef5350',\n        // Red border for bearish\n        borderUpColor: '#26a69a',\n        // Green border for bullish\n        wickDownColor: '#ef5350',\n        // Red wicks for bearish\n        wickUpColor: '#26a69a',\n        // Green wicks for bullish\n        priceLineVisible: false // Hide price line for cleaner look\n      });\n\n      // Add volume series\n      volumeSeries.current = chart.current.addHistogramSeries({\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume'\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.8,\n          bottom: 0\n        }\n      });\n      console.log('Chart initialized successfully');\n    } catch (error) {\n      console.error('Error initializing chart:', error);\n    }\n    return () => {\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, []);\n\n  // Use marketData prop directly instead of fetching separately\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      console.log(`📊 SimpleChart: Received ${marketData.length} data points for ${symbol}`);\n      setChartData(marketData);\n    }\n  }, [marketData, symbol]);\n\n  // Enhanced data fetching with historical data integration and caching\n  const fetchMarketData = async (timeframe, forceRefresh = false) => {\n    setLoading(true);\n    try {\n      console.log(`📊 Fetching ${timeframe} data for ${symbol}${forceRefresh ? ' (force refresh)' : ''}`);\n\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const cachedData = getCachedData(symbol, timeframe);\n        if (cachedData && cachedData.length > 0) {\n          setChartData(cachedData);\n          setLoading(false);\n          return;\n        }\n      }\n\n      // First try to get historical data for better chart initialization\n      const pairSymbol = symbol.replace('/', ''); // Convert \"XRP/USD\" to \"XRPUSD\"\n      const historicalResponse = await fetch(`http://localhost:8000/api/crypto-pairs/${pairSymbol}/historical?timeframe=${timeframe}&limit=500`);\n      let historicalData = [];\n      if (historicalResponse.ok) {\n        const histResult = await historicalResponse.json();\n        if (histResult.success && histResult.data.length > 0) {\n          historicalData = histResult.data;\n          console.log(`📈 Retrieved ${historicalData.length} historical ${timeframe} candles for ${symbol}`);\n        }\n      }\n\n      // Then get live market data to combine with historical\n      const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n      const liveResponse = await fetch(`http://localhost:8000/market-data?limit=200&timeframe=${timeframe}${pairParam}`);\n      let liveData = [];\n      if (liveResponse.ok) {\n        liveData = await liveResponse.json();\n        console.log(`📡 Retrieved ${liveData.length} live ${timeframe} data points for ${symbol}`);\n      }\n\n      // Combine historical and live data, removing duplicates\n      const combinedData = [...historicalData, ...liveData];\n      const uniqueData = [];\n      const seenTimestamps = new Set();\n\n      // Sort by timestamp and remove duplicates (keep latest)\n      combinedData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)).forEach(item => {\n        const timestamp = new Date(item.timestamp).getTime();\n        if (!seenTimestamps.has(timestamp)) {\n          seenTimestamps.add(timestamp);\n          uniqueData.push(item);\n        }\n      });\n      console.log(`📊 Combined data: ${uniqueData.length} unique ${timeframe} candles for ${symbol}`);\n      setChartData(uniqueData);\n\n      // Cache the combined data for faster future access\n      if (uniqueData.length > 0) {\n        setCachedData(symbol, timeframe, uniqueData);\n      }\n    } catch (error) {\n      console.error('Error fetching market data:', error);\n      // Fallback to live data only if historical fails\n      try {\n        const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n        const response = await fetch(`http://localhost:8000/market-data?limit=200&timeframe=${timeframe}${pairParam}`);\n        if (response.ok) {\n          const data = await response.json();\n          console.log(`📊 Fallback: ${data.length} ${timeframe} data points for ${symbol}`);\n          setChartData(data);\n        }\n      } catch (fallbackError) {\n        console.error('Fallback data fetch failed:', fallbackError);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data when timeframe changes or symbol changes\n  useEffect(() => {\n    console.log(`🔄 Timeframe/Symbol changed: ${selectedTimeframe} for ${symbol}`);\n    fetchMarketData(selectedTimeframe, true); // Force refresh on timeframe change\n\n    // Listen for real-time market data updates\n    const handleMarketDataUpdate = event => {\n      const {\n        symbol: updateSymbol,\n        candlestick\n      } = event.detail;\n\n      // Only update if this chart is showing the same symbol\n      if (updateSymbol === symbol && candlestickSeries.current) {\n        try {\n          // Update the chart with new candlestick data\n          candlestickSeries.current.update(candlestick);\n\n          // Update volume if available\n          if (volumeSeries.current && candlestick.volume) {\n            volumeSeries.current.update({\n              time: candlestick.time,\n              value: candlestick.volume,\n              color: candlestick.close >= candlestick.open ? '#26a69a44' : '#ef535044'\n            });\n          }\n          console.log('📊 Real-time chart update:', updateSymbol, '@', candlestick.close);\n          setCurrentPrice(candlestick.close);\n          setLastUpdate(new Date());\n          setIsUpdating(true);\n          setTimeout(() => setIsUpdating(false), 500);\n        } catch (error) {\n          console.error('Error updating chart with real-time data:', error);\n        }\n      }\n    };\n\n    // Add event listener for real-time updates\n    window.addEventListener('marketDataUpdate', handleMarketDataUpdate);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('marketDataUpdate', handleMarketDataUpdate);\n    };\n  }, [selectedTimeframe, symbol]);\n\n  // Update current price from live data AND update chart in real-time\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      const latest = marketData[marketData.length - 1];\n      if (latest && typeof latest.close === 'number') {\n        setCurrentPrice(latest.close);\n        if (marketData.length > 1) {\n          const previous = marketData[marketData.length - 2];\n          if (previous && typeof previous.close === 'number') {\n            setPriceChange((latest.close - previous.close) / previous.close * 100);\n          }\n        }\n\n        // 🔥 REAL-TIME CHART UPDATE: Use setData to avoid timestamp conflicts\n        if (candlestickSeries.current && volumeSeries.current) {\n          try {\n            // Process and deduplicate all market data\n            const processedData = [...marketData].map(item => ({\n              time: Math.floor(new Date(item.timestamp).getTime() / 1000),\n              open: parseFloat(item.open),\n              high: parseFloat(item.high),\n              low: parseFloat(item.low),\n              close: parseFloat(item.close),\n              volume: parseFloat(item.volume)\n            })).filter(item => !isNaN(item.time) && !isNaN(item.close)) // Remove invalid data\n            .sort((a, b) => a.time - b.time); // Sort by time ascending\n\n            // Remove duplicates by keeping the latest data for each timestamp\n            const uniqueData = [];\n            const timeMap = new Map();\n            processedData.forEach(item => {\n              if (!timeMap.has(item.time) || timeMap.get(item.time).close !== item.close) {\n                timeMap.set(item.time, item);\n              }\n            });\n\n            // Convert map back to array and sort again\n            const finalData = Array.from(timeMap.values()).sort((a, b) => a.time - b.time);\n\n            // Prepare candlestick data\n            const candleData = finalData.map(item => ({\n              time: item.time,\n              open: item.open,\n              high: item.high,\n              low: item.low,\n              close: item.close\n            }));\n\n            // Prepare volume data\n            const volumeData = finalData.map(item => ({\n              time: item.time,\n              value: item.volume,\n              color: item.close >= item.open ? '#00ff8844' : '#ff336644'\n            }));\n\n            // Update charts with clean, ordered data\n            candlestickSeries.current.setData(candleData);\n            volumeSeries.current.setData(volumeData);\n            console.log(`📊 Real-time chart updated: ${latest.close} (${finalData.length} unique points) at ${new Date().toLocaleTimeString()}`);\n            setLastUpdate(new Date());\n            setIsUpdating(true);\n            setTimeout(() => setIsUpdating(false), 500);\n          } catch (error) {\n            console.error('Error updating chart in real-time:', error);\n          }\n        }\n      }\n    }\n  }, [marketData]);\n\n  // Update chart data\n  useEffect(() => {\n    if (!candlestickSeries.current || !volumeSeries.current || !Array.isArray(chartData) || chartData.length === 0) {\n      return;\n    }\n    try {\n      setIsUpdating(true);\n      setLastUpdate(new Date());\n\n      // Sort and deduplicate data by timestamp\n      const sortedData = chartData.filter(item => item && item.timestamp && typeof item.open === 'number').sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());\n\n      // Remove duplicates by keeping the latest entry for each timestamp\n      const uniqueData = [];\n      const seenTimes = new Set();\n      for (let i = sortedData.length - 1; i >= 0; i--) {\n        const timeKey = new Date(sortedData[i].timestamp).getTime();\n        if (!seenTimes.has(timeKey)) {\n          seenTimes.add(timeKey);\n          uniqueData.unshift(sortedData[i]);\n        }\n      }\n      const formattedData = uniqueData.map(item => ({\n        time: new Date(item.timestamp).getTime() / 1000,\n        open: item.open,\n        high: item.high,\n        low: item.low,\n        close: item.close\n      }));\n      const volumeData = uniqueData.filter(item => typeof item.volume === 'number').map(item => ({\n        time: new Date(item.timestamp).getTime() / 1000,\n        value: item.volume,\n        color: item.close >= item.open ? '#00ff8844' : '#ff336644'\n      }));\n      console.log('📊 Chart updated with', formattedData.length, 'candles at', new Date().toLocaleTimeString());\n      if (formattedData.length > 0) {\n        candlestickSeries.current.setData(formattedData);\n      }\n      if (volumeData.length > 0) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // Clear updating indicator after a short delay\n      setTimeout(() => setIsUpdating(false), 500);\n    } catch (error) {\n      console.error('Error updating chart data:', error);\n      setIsUpdating(false);\n    }\n  }, [chartData]);\n\n  // Add signal markers with enhanced trade execution indicators\n  useEffect(() => {\n    if (!candlestickSeries.current || !Array.isArray(signals) || signals.length === 0) return;\n    try {\n      const markers = signals.filter(signal => signal && signal.signal && signal.signal !== 'HOLD' && signal.timestamp).map(signal => {\n        const isBuy = signal.signal === 'BUY';\n        return {\n          time: new Date(signal.timestamp).getTime() / 1000,\n          position: isBuy ? 'belowBar' : 'aboveBar',\n          color: isBuy ? '#00ff88' : '#ff3366',\n          shape: isBuy ? 'arrowUp' : 'arrowDown',\n          text: `${signal.signal} ${signal.price ? `@$${signal.price.toFixed(4)}` : ''} (${((signal.confidence || 0) * 100).toFixed(1)}%)`,\n          size: 2 // Larger markers for better visibility\n        };\n      });\n\n      // Add executed trade markers if available\n      if (signals.some(s => s.executed)) {\n        const executedMarkers = signals.filter(signal => signal.executed && signal.execution_price).map(signal => {\n          const isBuy = signal.signal === 'BUY';\n          return {\n            time: new Date(signal.execution_timestamp || signal.timestamp).getTime() / 1000,\n            position: isBuy ? 'belowBar' : 'aboveBar',\n            color: isBuy ? '#4bffb5' : '#ff4976',\n            // Brighter colors for executed trades\n            shape: 'circle',\n            text: `✓ EXECUTED ${signal.signal} @$${signal.execution_price.toFixed(4)}`,\n            size: 3 // Even larger for executed trades\n          };\n        });\n        markers.push(...executedMarkers);\n      }\n      candlestickSeries.current.setMarkers(markers);\n      console.log('📍 Updated chart markers:', markers.length, 'total markers');\n    } catch (error) {\n      console.error('Error setting signal markers:', error);\n    }\n  }, [signals]);\n  const handleTimeframeChange = timeframe => {\n    console.log(`🕐 Switching timeframe from ${selectedTimeframe} to ${timeframe} for ${symbol}`);\n    setSelectedTimeframe(timeframe);\n    // Clear current data to show loading state\n    setChartData([]);\n    setCurrentPrice(null);\n    setPriceChange(0);\n  };\n  return /*#__PURE__*/_jsxDEV(ChartContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ChartHeader, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [symbol, \" Live Chart\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '8px',\n              height: '8px',\n              borderRadius: '50%',\n              backgroundColor: isUpdating ? '#4bffb5' : '#666',\n              animation: isUpdating ? 'pulse 1s infinite' : 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px',\n              color: '#888',\n              fontFamily: 'monospace'\n            },\n            children: lastUpdate ? `Last: ${lastUpdate.toLocaleTimeString()}` : 'Waiting...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TimeframeSelector, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#888',\n              fontSize: '12px',\n              marginRight: '8px'\n            },\n            children: \"Timeframe:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), timeframes.map(tf => /*#__PURE__*/_jsxDEV(TimeframeButton, {\n            $active: selectedTimeframe === tf.value,\n            onClick: () => handleTimeframeChange(tf.value),\n            children: tf.label\n          }, tf.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PriceInfo, {\n          children: [/*#__PURE__*/_jsxDEV(PriceItem, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Current Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: [\"$\", (currentPrice === null || currentPrice === void 0 ? void 0 : currentPrice.toFixed(4)) || '0.0000']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PriceItem, {\n            color: priceChange >= 0 ? '#00ff88' : '#ff3366',\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Change\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: [priceChange >= 0 ? '+' : '', priceChange.toFixed(2), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: '450px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          background: 'rgba(0, 0, 0, 0.8)',\n          color: '#fff',\n          padding: '12px 24px',\n          borderRadius: '8px',\n          fontSize: '14px',\n          zIndex: 10\n        },\n        children: [\"Loading \", selectedTimeframe, \" data...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 504,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleChart, \"boqgyjP8LnL9P8CRebmVQXUfWcY=\");\n_c7 = SimpleChart;\nexport default SimpleChart;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"ChartContainer\");\n$RefreshReg$(_c2, \"ChartHeader\");\n$RefreshReg$(_c3, \"TimeframeSelector\");\n$RefreshReg$(_c4, \"TimeframeButton\");\n$RefreshReg$(_c5, \"PriceInfo\");\n$RefreshReg$(_c6, \"PriceItem\");\n$RefreshReg$(_c7, \"SimpleChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "createChart", "ColorType", "styled", "jsxDEV", "_jsxDEV", "dataCache", "Map", "CACHE_DURATION", "get<PERSON><PERSON><PERSON><PERSON>", "symbol", "timeframe", "getCachedData", "key", "cached", "get", "Date", "now", "timestamp", "console", "log", "data", "setCachedData", "set", "length", "ChartContainer", "div", "_c", "ChartHeader", "_c2", "TimeframeSelector", "_c3", "TimeframeButton", "button", "props", "$active", "_c4", "PriceInfo", "_c5", "PriceItem", "color", "_c6", "<PERSON><PERSON><PERSON>", "marketData", "signals", "_s", "chartContainerRef", "chart", "candlestickSeries", "volumeSeries", "currentPrice", "setCurrentPrice", "priceChange", "setPriceChange", "selectedTimeframe", "setSelectedTimeframe", "chartData", "setChartData", "loading", "setLoading", "lastUpdate", "setLastUpdate", "isUpdating", "setIsUpdating", "timeframes", "label", "value", "current", "layout", "background", "type", "Solid", "textColor", "grid", "vertLines", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "priceLineVisible", "addHistogramSeries", "priceFormat", "priceScaleId", "scale<PERSON>argins", "top", "bottom", "error", "remove", "Array", "isArray", "fetchMarketData", "forceRefresh", "cachedData", "pairSymbol", "replace", "historicalResponse", "fetch", "historicalData", "ok", "histResult", "json", "success", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "liveResponse", "liveData", "combinedData", "uniqueData", "seenTimestamps", "Set", "sort", "a", "b", "for<PERSON>ach", "item", "getTime", "has", "add", "push", "response", "fallback<PERSON><PERSON>r", "handleMarketDataUpdate", "event", "updateSymbol", "candlestick", "detail", "update", "volume", "time", "close", "open", "setTimeout", "window", "addEventListener", "removeEventListener", "latest", "previous", "processedData", "map", "Math", "floor", "parseFloat", "high", "low", "filter", "isNaN", "timeMap", "finalData", "from", "values", "candleData", "volumeData", "setData", "toLocaleTimeString", "sortedData", "seenTimes", "i", "<PERSON><PERSON><PERSON>", "unshift", "formattedData", "markers", "signal", "isBuy", "position", "shape", "text", "price", "toFixed", "confidence", "size", "some", "s", "executed", "executed<PERSON><PERSON><PERSON>", "execution_price", "execution_timestamp", "setMarkers", "handleTimeframeChange", "children", "style", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "borderRadius", "backgroundColor", "animation", "fontSize", "fontFamily", "marginRight", "tf", "onClick", "className", "ref", "left", "transform", "padding", "zIndex", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/SimpleChart.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { createChart, ColorType } from 'lightweight-charts';\nimport styled from 'styled-components';\n\n// Simple cache for market data to speed up chart loading\nconst dataCache = new Map();\nconst CACHE_DURATION = 30000; // 30 seconds\n\nconst getCacheKey = (symbol, timeframe) => `${symbol}-${timeframe}`;\n\nconst getCachedData = (symbol, timeframe) => {\n  const key = getCacheKey(symbol, timeframe);\n  const cached = dataCache.get(key);\n  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n    console.log(`📦 Using cached data for ${symbol} ${timeframe}`);\n    return cached.data;\n  }\n  return null;\n};\n\nconst setCachedData = (symbol, timeframe, data) => {\n  const key = getCacheKey(symbol, timeframe);\n  dataCache.set(key, {\n    data,\n    timestamp: Date.now()\n  });\n  console.log(`💾 Cached data for ${symbol} ${timeframe}: ${data.length} points`);\n};\n\nconst ChartContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n`;\n\nconst ChartHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  color: #ffffff;\n`;\n\nconst TimeframeSelector = styled.div`\n  display: flex;\n  gap: 8px;\n  align-items: center;\n`;\n\nconst TimeframeButton = styled.button`\n  background: ${props => props.$active ? '#4bffb5' : 'transparent'};\n  color: ${props => props.$active ? '#000' : '#fff'};\n  border: 1px solid ${props => props.$active ? '#4bffb5' : '#555'};\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s;\n\n  &:hover {\n    background: ${props => props.$active ? '#4bffb5' : '#333'};\n  }\n`;\n\nconst PriceInfo = styled.div`\n  display: flex;\n  gap: 20px;\n  font-size: 14px;\n`;\n\nconst PriceItem = styled.div`\n  display: flex;\n  flex-direction: column;\n\n  .label {\n    color: #888;\n    font-size: 12px;\n  }\n\n  .value {\n    color: ${props => props.color || '#fff'};\n    font-weight: bold;\n  }\n`;\n\nconst SimpleChart = ({ marketData, signals, symbol = 'XRP/USD' }) => {\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n  const [currentPrice, setCurrentPrice] = useState(null);\n  const [priceChange, setPriceChange] = useState(0);\n  const [selectedTimeframe, setSelectedTimeframe] = useState('1m');\n  const [chartData, setChartData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  const timeframes = [\n    { label: '1m', value: '1m' },\n    { label: '5m', value: '5m' },\n    { label: '15m', value: '15m' },\n    { label: '1h', value: '1h' },\n    { label: '4h', value: '4h' },\n    { label: '1d', value: '1d' }\n  ];\n\n  // Initialize chart\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    try {\n      // Create chart\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          background: { type: ColorType.Solid, color: '#1a1a1a' },\n          textColor: '#ffffff',\n        },\n        grid: {\n          vertLines: { color: '#2B2B43' },\n          horzLines: { color: '#2B2B43' },\n        },\n        crosshair: {\n          mode: 1,\n        },\n        rightPriceScale: {\n          borderColor: '#485c7b',\n        },\n        timeScale: {\n          borderColor: '#485c7b',\n          timeVisible: true,\n          secondsVisible: false,\n        },\n      });\n\n      // Add candlestick series with TradingView colors (green bullish, red bearish)\n      candlestickSeries.current = chart.current.addCandlestickSeries({\n        upColor: '#26a69a',        // Green for bullish candles (TradingView style)\n        downColor: '#ef5350',      // Red for bearish candles (TradingView style)\n        borderDownColor: '#ef5350', // Red border for bearish\n        borderUpColor: '#26a69a',   // Green border for bullish\n        wickDownColor: '#ef5350',   // Red wicks for bearish\n        wickUpColor: '#26a69a',     // Green wicks for bullish\n        priceLineVisible: false,    // Hide price line for cleaner look\n      });\n\n      // Add volume series\n      volumeSeries.current = chart.current.addHistogramSeries({\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume',\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.8,\n          bottom: 0,\n        },\n      });\n\n      console.log('Chart initialized successfully');\n\n    } catch (error) {\n      console.error('Error initializing chart:', error);\n    }\n\n    return () => {\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, []);\n\n  // Use marketData prop directly instead of fetching separately\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      console.log(`📊 SimpleChart: Received ${marketData.length} data points for ${symbol}`);\n      setChartData(marketData);\n    }\n  }, [marketData, symbol]);\n\n  // Enhanced data fetching with historical data integration and caching\n  const fetchMarketData = async (timeframe, forceRefresh = false) => {\n    setLoading(true);\n    try {\n      console.log(`📊 Fetching ${timeframe} data for ${symbol}${forceRefresh ? ' (force refresh)' : ''}`);\n\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const cachedData = getCachedData(symbol, timeframe);\n        if (cachedData && cachedData.length > 0) {\n          setChartData(cachedData);\n          setLoading(false);\n          return;\n        }\n      }\n\n      // First try to get historical data for better chart initialization\n      const pairSymbol = symbol.replace('/', '');  // Convert \"XRP/USD\" to \"XRPUSD\"\n      const historicalResponse = await fetch(\n        `http://localhost:8000/api/crypto-pairs/${pairSymbol}/historical?timeframe=${timeframe}&limit=500`\n      );\n\n      let historicalData = [];\n      if (historicalResponse.ok) {\n        const histResult = await historicalResponse.json();\n        if (histResult.success && histResult.data.length > 0) {\n          historicalData = histResult.data;\n          console.log(`📈 Retrieved ${historicalData.length} historical ${timeframe} candles for ${symbol}`);\n        }\n      }\n\n      // Then get live market data to combine with historical\n      const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n      const liveResponse = await fetch(\n        `http://localhost:8000/market-data?limit=200&timeframe=${timeframe}${pairParam}`\n      );\n\n      let liveData = [];\n      if (liveResponse.ok) {\n        liveData = await liveResponse.json();\n        console.log(`📡 Retrieved ${liveData.length} live ${timeframe} data points for ${symbol}`);\n      }\n\n      // Combine historical and live data, removing duplicates\n      const combinedData = [...historicalData, ...liveData];\n      const uniqueData = [];\n      const seenTimestamps = new Set();\n\n      // Sort by timestamp and remove duplicates (keep latest)\n      combinedData\n        .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))\n        .forEach(item => {\n          const timestamp = new Date(item.timestamp).getTime();\n          if (!seenTimestamps.has(timestamp)) {\n            seenTimestamps.add(timestamp);\n            uniqueData.push(item);\n          }\n        });\n\n      console.log(`📊 Combined data: ${uniqueData.length} unique ${timeframe} candles for ${symbol}`);\n      setChartData(uniqueData);\n\n      // Cache the combined data for faster future access\n      if (uniqueData.length > 0) {\n        setCachedData(symbol, timeframe, uniqueData);\n      }\n\n    } catch (error) {\n      console.error('Error fetching market data:', error);\n      // Fallback to live data only if historical fails\n      try {\n        const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n        const response = await fetch(`http://localhost:8000/market-data?limit=200&timeframe=${timeframe}${pairParam}`);\n        if (response.ok) {\n          const data = await response.json();\n          console.log(`📊 Fallback: ${data.length} ${timeframe} data points for ${symbol}`);\n          setChartData(data);\n        }\n      } catch (fallbackError) {\n        console.error('Fallback data fetch failed:', fallbackError);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data when timeframe changes or symbol changes\n  useEffect(() => {\n    console.log(`🔄 Timeframe/Symbol changed: ${selectedTimeframe} for ${symbol}`);\n    fetchMarketData(selectedTimeframe, true); // Force refresh on timeframe change\n\n    // Listen for real-time market data updates\n    const handleMarketDataUpdate = (event) => {\n      const { symbol: updateSymbol, candlestick } = event.detail;\n\n      // Only update if this chart is showing the same symbol\n      if (updateSymbol === symbol && candlestickSeries.current) {\n        try {\n          // Update the chart with new candlestick data\n          candlestickSeries.current.update(candlestick);\n\n          // Update volume if available\n          if (volumeSeries.current && candlestick.volume) {\n            volumeSeries.current.update({\n              time: candlestick.time,\n              value: candlestick.volume,\n              color: candlestick.close >= candlestick.open ? '#26a69a44' : '#ef535044'\n            });\n          }\n\n          console.log('📊 Real-time chart update:', updateSymbol, '@', candlestick.close);\n          setCurrentPrice(candlestick.close);\n          setLastUpdate(new Date());\n          setIsUpdating(true);\n          setTimeout(() => setIsUpdating(false), 500);\n        } catch (error) {\n          console.error('Error updating chart with real-time data:', error);\n        }\n      }\n    };\n\n    // Add event listener for real-time updates\n    window.addEventListener('marketDataUpdate', handleMarketDataUpdate);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('marketDataUpdate', handleMarketDataUpdate);\n    };\n  }, [selectedTimeframe, symbol]);\n\n  // Update current price from live data AND update chart in real-time\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      const latest = marketData[marketData.length - 1];\n      if (latest && typeof latest.close === 'number') {\n        setCurrentPrice(latest.close);\n        if (marketData.length > 1) {\n          const previous = marketData[marketData.length - 2];\n          if (previous && typeof previous.close === 'number') {\n            setPriceChange(((latest.close - previous.close) / previous.close) * 100);\n          }\n        }\n\n        // 🔥 REAL-TIME CHART UPDATE: Use setData to avoid timestamp conflicts\n        if (candlestickSeries.current && volumeSeries.current) {\n          try {\n            // Process and deduplicate all market data\n            const processedData = [...marketData]\n              .map(item => ({\n                time: Math.floor(new Date(item.timestamp).getTime() / 1000),\n                open: parseFloat(item.open),\n                high: parseFloat(item.high),\n                low: parseFloat(item.low),\n                close: parseFloat(item.close),\n                volume: parseFloat(item.volume)\n              }))\n              .filter(item => !isNaN(item.time) && !isNaN(item.close)) // Remove invalid data\n              .sort((a, b) => a.time - b.time); // Sort by time ascending\n\n            // Remove duplicates by keeping the latest data for each timestamp\n            const uniqueData = [];\n            const timeMap = new Map();\n\n            processedData.forEach(item => {\n              if (!timeMap.has(item.time) || timeMap.get(item.time).close !== item.close) {\n                timeMap.set(item.time, item);\n              }\n            });\n\n            // Convert map back to array and sort again\n            const finalData = Array.from(timeMap.values()).sort((a, b) => a.time - b.time);\n\n            // Prepare candlestick data\n            const candleData = finalData.map(item => ({\n              time: item.time,\n              open: item.open,\n              high: item.high,\n              low: item.low,\n              close: item.close\n            }));\n\n            // Prepare volume data\n            const volumeData = finalData.map(item => ({\n              time: item.time,\n              value: item.volume,\n              color: item.close >= item.open ? '#00ff8844' : '#ff336644'\n            }));\n\n            // Update charts with clean, ordered data\n            candlestickSeries.current.setData(candleData);\n            volumeSeries.current.setData(volumeData);\n\n            console.log(`📊 Real-time chart updated: ${latest.close} (${finalData.length} unique points) at ${new Date().toLocaleTimeString()}`);\n            setLastUpdate(new Date());\n            setIsUpdating(true);\n            setTimeout(() => setIsUpdating(false), 500);\n\n          } catch (error) {\n            console.error('Error updating chart in real-time:', error);\n          }\n        }\n      }\n    }\n  }, [marketData]);\n\n  // Update chart data\n  useEffect(() => {\n    if (!candlestickSeries.current || !volumeSeries.current || !Array.isArray(chartData) || chartData.length === 0) {\n      return;\n    }\n\n    try {\n      setIsUpdating(true);\n      setLastUpdate(new Date());\n\n      // Sort and deduplicate data by timestamp\n      const sortedData = chartData\n        .filter(item => item && item.timestamp && typeof item.open === 'number')\n        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());\n\n      // Remove duplicates by keeping the latest entry for each timestamp\n      const uniqueData = [];\n      const seenTimes = new Set();\n\n      for (let i = sortedData.length - 1; i >= 0; i--) {\n        const timeKey = new Date(sortedData[i].timestamp).getTime();\n        if (!seenTimes.has(timeKey)) {\n          seenTimes.add(timeKey);\n          uniqueData.unshift(sortedData[i]);\n        }\n      }\n\n      const formattedData = uniqueData.map(item => ({\n        time: new Date(item.timestamp).getTime() / 1000,\n        open: item.open,\n        high: item.high,\n        low: item.low,\n        close: item.close,\n      }));\n\n      const volumeData = uniqueData\n        .filter(item => typeof item.volume === 'number')\n        .map(item => ({\n          time: new Date(item.timestamp).getTime() / 1000,\n          value: item.volume,\n          color: item.close >= item.open ? '#00ff8844' : '#ff336644',\n        }));\n\n      console.log('📊 Chart updated with', formattedData.length, 'candles at', new Date().toLocaleTimeString());\n\n      if (formattedData.length > 0) {\n        candlestickSeries.current.setData(formattedData);\n      }\n\n      if (volumeData.length > 0) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // Clear updating indicator after a short delay\n      setTimeout(() => setIsUpdating(false), 500);\n\n    } catch (error) {\n      console.error('Error updating chart data:', error);\n      setIsUpdating(false);\n    }\n  }, [chartData]);\n\n  // Add signal markers with enhanced trade execution indicators\n  useEffect(() => {\n    if (!candlestickSeries.current || !Array.isArray(signals) || signals.length === 0) return;\n\n    try {\n      const markers = signals\n        .filter(signal => signal && signal.signal && signal.signal !== 'HOLD' && signal.timestamp)\n        .map(signal => {\n          const isBuy = signal.signal === 'BUY';\n          return {\n            time: new Date(signal.timestamp).getTime() / 1000,\n            position: isBuy ? 'belowBar' : 'aboveBar',\n            color: isBuy ? '#00ff88' : '#ff3366',\n            shape: isBuy ? 'arrowUp' : 'arrowDown',\n            text: `${signal.signal} ${signal.price ? `@$${signal.price.toFixed(4)}` : ''} (${((signal.confidence || 0) * 100).toFixed(1)}%)`,\n            size: 2, // Larger markers for better visibility\n          };\n        });\n\n      // Add executed trade markers if available\n      if (signals.some(s => s.executed)) {\n        const executedMarkers = signals\n          .filter(signal => signal.executed && signal.execution_price)\n          .map(signal => {\n            const isBuy = signal.signal === 'BUY';\n            return {\n              time: new Date(signal.execution_timestamp || signal.timestamp).getTime() / 1000,\n              position: isBuy ? 'belowBar' : 'aboveBar',\n              color: isBuy ? '#4bffb5' : '#ff4976', // Brighter colors for executed trades\n              shape: 'circle',\n              text: `✓ EXECUTED ${signal.signal} @$${signal.execution_price.toFixed(4)}`,\n              size: 3, // Even larger for executed trades\n            };\n          });\n\n        markers.push(...executedMarkers);\n      }\n\n      candlestickSeries.current.setMarkers(markers);\n      console.log('📍 Updated chart markers:', markers.length, 'total markers');\n    } catch (error) {\n      console.error('Error setting signal markers:', error);\n    }\n  }, [signals]);\n\n  const handleTimeframeChange = (timeframe) => {\n    console.log(`🕐 Switching timeframe from ${selectedTimeframe} to ${timeframe} for ${symbol}`);\n    setSelectedTimeframe(timeframe);\n    // Clear current data to show loading state\n    setChartData([]);\n    setCurrentPrice(null);\n    setPriceChange(0);\n  };\n\n  return (\n    <ChartContainer>\n      <ChartHeader>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <h3>{symbol} Live Chart</h3>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            <div style={{\n              width: '8px',\n              height: '8px',\n              borderRadius: '50%',\n              backgroundColor: isUpdating ? '#4bffb5' : '#666',\n              animation: isUpdating ? 'pulse 1s infinite' : 'none'\n            }} />\n            <span style={{\n              fontSize: '12px',\n              color: '#888',\n              fontFamily: 'monospace'\n            }}>\n              {lastUpdate ? `Last: ${lastUpdate.toLocaleTimeString()}` : 'Waiting...'}\n            </span>\n          </div>\n        </div>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>\n          <TimeframeSelector>\n            <span style={{ color: '#888', fontSize: '12px', marginRight: '8px' }}>Timeframe:</span>\n            {timeframes.map(tf => (\n              <TimeframeButton\n                key={tf.value}\n                $active={selectedTimeframe === tf.value}\n                onClick={() => handleTimeframeChange(tf.value)}\n              >\n                {tf.label}\n              </TimeframeButton>\n            ))}\n          </TimeframeSelector>\n          <PriceInfo>\n            <PriceItem>\n              <span className=\"label\">Current Price</span>\n              <span className=\"value\">${currentPrice?.toFixed(4) || '0.0000'}</span>\n            </PriceItem>\n            <PriceItem color={priceChange >= 0 ? '#00ff88' : '#ff3366'}>\n              <span className=\"label\">Change</span>\n              <span className=\"value\">\n                {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}%\n              </span>\n            </PriceItem>\n          </PriceInfo>\n        </div>\n      </ChartHeader>\n      <div style={{ position: 'relative' }}>\n        <div ref={chartContainerRef} style={{ width: '100%', height: '450px' }} />\n        {loading && (\n          <div style={{\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            background: 'rgba(0, 0, 0, 0.8)',\n            color: '#fff',\n            padding: '12px 24px',\n            borderRadius: '8px',\n            fontSize: '14px',\n            zIndex: 10\n          }}>\n            Loading {selectedTimeframe} data...\n          </div>\n        )}\n      </div>\n    </ChartContainer>\n  );\n};\n\nexport default SimpleChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,SAAS,QAAQ,oBAAoB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,MAAMC,cAAc,GAAG,KAAK,CAAC,CAAC;;AAE9B,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK,GAAGD,MAAM,IAAIC,SAAS,EAAE;AAEnE,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,SAAS,KAAK;EAC3C,MAAME,GAAG,GAAGJ,WAAW,CAACC,MAAM,EAAEC,SAAS,CAAC;EAC1C,MAAMG,MAAM,GAAGR,SAAS,CAACS,GAAG,CAACF,GAAG,CAAC;EACjC,IAAIC,MAAM,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACI,SAAS,GAAGV,cAAc,EAAE;IAC5DW,OAAO,CAACC,GAAG,CAAC,4BAA4BV,MAAM,IAAIC,SAAS,EAAE,CAAC;IAC9D,OAAOG,MAAM,CAACO,IAAI;EACpB;EACA,OAAO,IAAI;AACb,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACZ,MAAM,EAAEC,SAAS,EAAEU,IAAI,KAAK;EACjD,MAAMR,GAAG,GAAGJ,WAAW,CAACC,MAAM,EAAEC,SAAS,CAAC;EAC1CL,SAAS,CAACiB,GAAG,CAACV,GAAG,EAAE;IACjBQ,IAAI;IACJH,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC;EACtB,CAAC,CAAC;EACFE,OAAO,CAACC,GAAG,CAAC,sBAAsBV,MAAM,IAAIC,SAAS,KAAKU,IAAI,CAACG,MAAM,SAAS,CAAC;AACjF,CAAC;AAED,MAAMC,cAAc,GAAGtB,MAAM,CAACuB,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,cAAc;AAOpB,MAAMG,WAAW,GAAGzB,MAAM,CAACuB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,WAAW;AAQjB,MAAME,iBAAiB,GAAG3B,MAAM,CAACuB,GAAG;AACpC;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAJID,iBAAiB;AAMvB,MAAME,eAAe,GAAG7B,MAAM,CAAC8B,MAAM;AACrC,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,aAAa;AAClE,WAAWD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,MAAM,GAAG,MAAM;AACnD,sBAAsBD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AAC7D;AACA,CAAC;AAACC,GAAA,GAbIJ,eAAe;AAerB,MAAMK,SAAS,GAAGlC,MAAM,CAACuB,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAGpC,MAAM,CAACuB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaQ,KAAK,IAAIA,KAAK,CAACM,KAAK,IAAI,MAAM;AAC3C;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,SAAS;AAef,MAAMG,WAAW,GAAGA,CAAC;EAAEC,UAAU;EAAEC,OAAO;EAAElC,MAAM,GAAG;AAAU,CAAC,KAAK;EAAAmC,EAAA;EACnE,MAAMC,iBAAiB,GAAG9C,MAAM,CAAC,CAAC;EAClC,MAAM+C,KAAK,GAAG/C,MAAM,CAAC,CAAC;EACtB,MAAMgD,iBAAiB,GAAGhD,MAAM,CAAC,CAAC;EAClC,MAAMiD,YAAY,GAAGjD,MAAM,CAAC,CAAC;EAC7B,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMiE,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,CAC7B;;EAED;EACApE,SAAS,CAAC,MAAM;IACd,IAAI,CAACgD,iBAAiB,CAACqB,OAAO,EAAE;IAEhC,IAAI;MACF;MACApB,KAAK,CAACoB,OAAO,GAAGlE,WAAW,CAAC6C,iBAAiB,CAACqB,OAAO,EAAE;QACrDC,MAAM,EAAE;UACNC,UAAU,EAAE;YAAEC,IAAI,EAAEpE,SAAS,CAACqE,KAAK;YAAE/B,KAAK,EAAE;UAAU,CAAC;UACvDgC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAE;UACJC,SAAS,EAAE;YAAElC,KAAK,EAAE;UAAU,CAAC;UAC/BmC,SAAS,EAAE;YAAEnC,KAAK,EAAE;UAAU;QAChC,CAAC;QACDoC,SAAS,EAAE;UACTC,IAAI,EAAE;QACR,CAAC;QACDC,eAAe,EAAE;UACfC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTD,WAAW,EAAE,SAAS;UACtBE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;;MAEF;MACAlC,iBAAiB,CAACmB,OAAO,GAAGpB,KAAK,CAACoB,OAAO,CAACgB,oBAAoB,CAAC;QAC7DC,OAAO,EAAE,SAAS;QAAS;QAC3BC,SAAS,EAAE,SAAS;QAAO;QAC3BC,eAAe,EAAE,SAAS;QAAE;QAC5BC,aAAa,EAAE,SAAS;QAAI;QAC5BC,aAAa,EAAE,SAAS;QAAI;QAC5BC,WAAW,EAAE,SAAS;QAAM;QAC5BC,gBAAgB,EAAE,KAAK,CAAK;MAC9B,CAAC,CAAC;;MAEF;MACAzC,YAAY,CAACkB,OAAO,GAAGpB,KAAK,CAACoB,OAAO,CAACwB,kBAAkB,CAAC;QACtDnD,KAAK,EAAE,SAAS;QAChBoD,WAAW,EAAE;UACXtB,IAAI,EAAE;QACR,CAAC;QACDuB,YAAY,EAAE,QAAQ;QACtBC,YAAY,EAAE;UACZC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE;QACV;MACF,CAAC,CAAC;MAEF7E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE/C,CAAC,CAAC,OAAO6E,KAAK,EAAE;MACd9E,OAAO,CAAC8E,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;IAEA,OAAO,MAAM;MACX,IAAIlD,KAAK,CAACoB,OAAO,EAAE;QACjBpB,KAAK,CAACoB,OAAO,CAAC+B,MAAM,CAAC,CAAC;MACxB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApG,SAAS,CAAC,MAAM;IACd,IAAIqG,KAAK,CAACC,OAAO,CAACzD,UAAU,CAAC,IAAIA,UAAU,CAACnB,MAAM,GAAG,CAAC,EAAE;MACtDL,OAAO,CAACC,GAAG,CAAC,4BAA4BuB,UAAU,CAACnB,MAAM,oBAAoBd,MAAM,EAAE,CAAC;MACtF+C,YAAY,CAACd,UAAU,CAAC;IAC1B;EACF,CAAC,EAAE,CAACA,UAAU,EAAEjC,MAAM,CAAC,CAAC;;EAExB;EACA,MAAM2F,eAAe,GAAG,MAAAA,CAAO1F,SAAS,EAAE2F,YAAY,GAAG,KAAK,KAAK;IACjE3C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFxC,OAAO,CAACC,GAAG,CAAC,eAAeT,SAAS,aAAaD,MAAM,GAAG4F,YAAY,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC;;MAEnG;MACA,IAAI,CAACA,YAAY,EAAE;QACjB,MAAMC,UAAU,GAAG3F,aAAa,CAACF,MAAM,EAAEC,SAAS,CAAC;QACnD,IAAI4F,UAAU,IAAIA,UAAU,CAAC/E,MAAM,GAAG,CAAC,EAAE;UACvCiC,YAAY,CAAC8C,UAAU,CAAC;UACxB5C,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA,MAAM6C,UAAU,GAAG9F,MAAM,CAAC+F,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAE;MAC7C,MAAMC,kBAAkB,GAAG,MAAMC,KAAK,CACpC,0CAA0CH,UAAU,yBAAyB7F,SAAS,YACxF,CAAC;MAED,IAAIiG,cAAc,GAAG,EAAE;MACvB,IAAIF,kBAAkB,CAACG,EAAE,EAAE;QACzB,MAAMC,UAAU,GAAG,MAAMJ,kBAAkB,CAACK,IAAI,CAAC,CAAC;QAClD,IAAID,UAAU,CAACE,OAAO,IAAIF,UAAU,CAACzF,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;UACpDoF,cAAc,GAAGE,UAAU,CAACzF,IAAI;UAChCF,OAAO,CAACC,GAAG,CAAC,gBAAgBwF,cAAc,CAACpF,MAAM,eAAeb,SAAS,gBAAgBD,MAAM,EAAE,CAAC;QACpG;MACF;;MAEA;MACA,MAAMuG,SAAS,GAAGvG,MAAM,GAAG,SAASwG,kBAAkB,CAACxG,MAAM,CAAC,EAAE,GAAG,EAAE;MACrE,MAAMyG,YAAY,GAAG,MAAMR,KAAK,CAC9B,yDAAyDhG,SAAS,GAAGsG,SAAS,EAChF,CAAC;MAED,IAAIG,QAAQ,GAAG,EAAE;MACjB,IAAID,YAAY,CAACN,EAAE,EAAE;QACnBO,QAAQ,GAAG,MAAMD,YAAY,CAACJ,IAAI,CAAC,CAAC;QACpC5F,OAAO,CAACC,GAAG,CAAC,gBAAgBgG,QAAQ,CAAC5F,MAAM,SAASb,SAAS,oBAAoBD,MAAM,EAAE,CAAC;MAC5F;;MAEA;MACA,MAAM2G,YAAY,GAAG,CAAC,GAAGT,cAAc,EAAE,GAAGQ,QAAQ,CAAC;MACrD,MAAME,UAAU,GAAG,EAAE;MACrB,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;;MAEhC;MACAH,YAAY,CACTI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI3G,IAAI,CAAC0G,CAAC,CAACxG,SAAS,CAAC,GAAG,IAAIF,IAAI,CAAC2G,CAAC,CAACzG,SAAS,CAAC,CAAC,CAC7D0G,OAAO,CAACC,IAAI,IAAI;QACf,MAAM3G,SAAS,GAAG,IAAIF,IAAI,CAAC6G,IAAI,CAAC3G,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC;QACpD,IAAI,CAACP,cAAc,CAACQ,GAAG,CAAC7G,SAAS,CAAC,EAAE;UAClCqG,cAAc,CAACS,GAAG,CAAC9G,SAAS,CAAC;UAC7BoG,UAAU,CAACW,IAAI,CAACJ,IAAI,CAAC;QACvB;MACF,CAAC,CAAC;MAEJ1G,OAAO,CAACC,GAAG,CAAC,qBAAqBkG,UAAU,CAAC9F,MAAM,WAAWb,SAAS,gBAAgBD,MAAM,EAAE,CAAC;MAC/F+C,YAAY,CAAC6D,UAAU,CAAC;;MAExB;MACA,IAAIA,UAAU,CAAC9F,MAAM,GAAG,CAAC,EAAE;QACzBF,aAAa,CAACZ,MAAM,EAAEC,SAAS,EAAE2G,UAAU,CAAC;MAC9C;IAEF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACd9E,OAAO,CAAC8E,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACA,IAAI;QACF,MAAMgB,SAAS,GAAGvG,MAAM,GAAG,SAASwG,kBAAkB,CAACxG,MAAM,CAAC,EAAE,GAAG,EAAE;QACrE,MAAMwH,QAAQ,GAAG,MAAMvB,KAAK,CAAC,yDAAyDhG,SAAS,GAAGsG,SAAS,EAAE,CAAC;QAC9G,IAAIiB,QAAQ,CAACrB,EAAE,EAAE;UACf,MAAMxF,IAAI,GAAG,MAAM6G,QAAQ,CAACnB,IAAI,CAAC,CAAC;UAClC5F,OAAO,CAACC,GAAG,CAAC,gBAAgBC,IAAI,CAACG,MAAM,IAAIb,SAAS,oBAAoBD,MAAM,EAAE,CAAC;UACjF+C,YAAY,CAACpC,IAAI,CAAC;QACpB;MACF,CAAC,CAAC,OAAO8G,aAAa,EAAE;QACtBhH,OAAO,CAAC8E,KAAK,CAAC,6BAA6B,EAAEkC,aAAa,CAAC;MAC7D;IACF,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA7D,SAAS,CAAC,MAAM;IACdqB,OAAO,CAACC,GAAG,CAAC,gCAAgCkC,iBAAiB,QAAQ5C,MAAM,EAAE,CAAC;IAC9E2F,eAAe,CAAC/C,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE1C;IACA,MAAM8E,sBAAsB,GAAIC,KAAK,IAAK;MACxC,MAAM;QAAE3H,MAAM,EAAE4H,YAAY;QAAEC;MAAY,CAAC,GAAGF,KAAK,CAACG,MAAM;;MAE1D;MACA,IAAIF,YAAY,KAAK5H,MAAM,IAAIsC,iBAAiB,CAACmB,OAAO,EAAE;QACxD,IAAI;UACF;UACAnB,iBAAiB,CAACmB,OAAO,CAACsE,MAAM,CAACF,WAAW,CAAC;;UAE7C;UACA,IAAItF,YAAY,CAACkB,OAAO,IAAIoE,WAAW,CAACG,MAAM,EAAE;YAC9CzF,YAAY,CAACkB,OAAO,CAACsE,MAAM,CAAC;cAC1BE,IAAI,EAAEJ,WAAW,CAACI,IAAI;cACtBzE,KAAK,EAAEqE,WAAW,CAACG,MAAM;cACzBlG,KAAK,EAAE+F,WAAW,CAACK,KAAK,IAAIL,WAAW,CAACM,IAAI,GAAG,WAAW,GAAG;YAC/D,CAAC,CAAC;UACJ;UAEA1H,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkH,YAAY,EAAE,GAAG,EAAEC,WAAW,CAACK,KAAK,CAAC;UAC/EzF,eAAe,CAACoF,WAAW,CAACK,KAAK,CAAC;UAClC/E,aAAa,CAAC,IAAI7C,IAAI,CAAC,CAAC,CAAC;UACzB+C,aAAa,CAAC,IAAI,CAAC;UACnB+E,UAAU,CAAC,MAAM/E,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;QAC7C,CAAC,CAAC,OAAOkC,KAAK,EAAE;UACd9E,OAAO,CAAC8E,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACnE;MACF;IACF,CAAC;;IAED;IACA8C,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEZ,sBAAsB,CAAC;;IAEnE;IACA,OAAO,MAAM;MACXW,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAEb,sBAAsB,CAAC;IACxE,CAAC;EACH,CAAC,EAAE,CAAC9E,iBAAiB,EAAE5C,MAAM,CAAC,CAAC;;EAE/B;EACAZ,SAAS,CAAC,MAAM;IACd,IAAIqG,KAAK,CAACC,OAAO,CAACzD,UAAU,CAAC,IAAIA,UAAU,CAACnB,MAAM,GAAG,CAAC,EAAE;MACtD,MAAM0H,MAAM,GAAGvG,UAAU,CAACA,UAAU,CAACnB,MAAM,GAAG,CAAC,CAAC;MAChD,IAAI0H,MAAM,IAAI,OAAOA,MAAM,CAACN,KAAK,KAAK,QAAQ,EAAE;QAC9CzF,eAAe,CAAC+F,MAAM,CAACN,KAAK,CAAC;QAC7B,IAAIjG,UAAU,CAACnB,MAAM,GAAG,CAAC,EAAE;UACzB,MAAM2H,QAAQ,GAAGxG,UAAU,CAACA,UAAU,CAACnB,MAAM,GAAG,CAAC,CAAC;UAClD,IAAI2H,QAAQ,IAAI,OAAOA,QAAQ,CAACP,KAAK,KAAK,QAAQ,EAAE;YAClDvF,cAAc,CAAE,CAAC6F,MAAM,CAACN,KAAK,GAAGO,QAAQ,CAACP,KAAK,IAAIO,QAAQ,CAACP,KAAK,GAAI,GAAG,CAAC;UAC1E;QACF;;QAEA;QACA,IAAI5F,iBAAiB,CAACmB,OAAO,IAAIlB,YAAY,CAACkB,OAAO,EAAE;UACrD,IAAI;YACF;YACA,MAAMiF,aAAa,GAAG,CAAC,GAAGzG,UAAU,CAAC,CAClC0G,GAAG,CAACxB,IAAI,KAAK;cACZc,IAAI,EAAEW,IAAI,CAACC,KAAK,CAAC,IAAIvI,IAAI,CAAC6G,IAAI,CAAC3G,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;cAC3De,IAAI,EAAEW,UAAU,CAAC3B,IAAI,CAACgB,IAAI,CAAC;cAC3BY,IAAI,EAAED,UAAU,CAAC3B,IAAI,CAAC4B,IAAI,CAAC;cAC3BC,GAAG,EAAEF,UAAU,CAAC3B,IAAI,CAAC6B,GAAG,CAAC;cACzBd,KAAK,EAAEY,UAAU,CAAC3B,IAAI,CAACe,KAAK,CAAC;cAC7BF,MAAM,EAAEc,UAAU,CAAC3B,IAAI,CAACa,MAAM;YAChC,CAAC,CAAC,CAAC,CACFiB,MAAM,CAAC9B,IAAI,IAAI,CAAC+B,KAAK,CAAC/B,IAAI,CAACc,IAAI,CAAC,IAAI,CAACiB,KAAK,CAAC/B,IAAI,CAACe,KAAK,CAAC,CAAC,CAAC;YAAA,CACxDnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACiB,IAAI,GAAGhB,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC;;YAEpC;YACA,MAAMrB,UAAU,GAAG,EAAE;YACrB,MAAMuC,OAAO,GAAG,IAAItJ,GAAG,CAAC,CAAC;YAEzB6I,aAAa,CAACxB,OAAO,CAACC,IAAI,IAAI;cAC5B,IAAI,CAACgC,OAAO,CAAC9B,GAAG,CAACF,IAAI,CAACc,IAAI,CAAC,IAAIkB,OAAO,CAAC9I,GAAG,CAAC8G,IAAI,CAACc,IAAI,CAAC,CAACC,KAAK,KAAKf,IAAI,CAACe,KAAK,EAAE;gBAC1EiB,OAAO,CAACtI,GAAG,CAACsG,IAAI,CAACc,IAAI,EAAEd,IAAI,CAAC;cAC9B;YACF,CAAC,CAAC;;YAEF;YACA,MAAMiC,SAAS,GAAG3D,KAAK,CAAC4D,IAAI,CAACF,OAAO,CAACG,MAAM,CAAC,CAAC,CAAC,CAACvC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACiB,IAAI,GAAGhB,CAAC,CAACgB,IAAI,CAAC;;YAE9E;YACA,MAAMsB,UAAU,GAAGH,SAAS,CAACT,GAAG,CAACxB,IAAI,KAAK;cACxCc,IAAI,EAAEd,IAAI,CAACc,IAAI;cACfE,IAAI,EAAEhB,IAAI,CAACgB,IAAI;cACfY,IAAI,EAAE5B,IAAI,CAAC4B,IAAI;cACfC,GAAG,EAAE7B,IAAI,CAAC6B,GAAG;cACbd,KAAK,EAAEf,IAAI,CAACe;YACd,CAAC,CAAC,CAAC;;YAEH;YACA,MAAMsB,UAAU,GAAGJ,SAAS,CAACT,GAAG,CAACxB,IAAI,KAAK;cACxCc,IAAI,EAAEd,IAAI,CAACc,IAAI;cACfzE,KAAK,EAAE2D,IAAI,CAACa,MAAM;cAClBlG,KAAK,EAAEqF,IAAI,CAACe,KAAK,IAAIf,IAAI,CAACgB,IAAI,GAAG,WAAW,GAAG;YACjD,CAAC,CAAC,CAAC;;YAEH;YACA7F,iBAAiB,CAACmB,OAAO,CAACgG,OAAO,CAACF,UAAU,CAAC;YAC7ChH,YAAY,CAACkB,OAAO,CAACgG,OAAO,CAACD,UAAU,CAAC;YAExC/I,OAAO,CAACC,GAAG,CAAC,+BAA+B8H,MAAM,CAACN,KAAK,KAAKkB,SAAS,CAACtI,MAAM,sBAAsB,IAAIR,IAAI,CAAC,CAAC,CAACoJ,kBAAkB,CAAC,CAAC,EAAE,CAAC;YACpIvG,aAAa,CAAC,IAAI7C,IAAI,CAAC,CAAC,CAAC;YACzB+C,aAAa,CAAC,IAAI,CAAC;YACnB+E,UAAU,CAAC,MAAM/E,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;UAE7C,CAAC,CAAC,OAAOkC,KAAK,EAAE;YACd9E,OAAO,CAAC8E,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC5D;QACF;MACF;IACF;EACF,CAAC,EAAE,CAACtD,UAAU,CAAC,CAAC;;EAEhB;EACA7C,SAAS,CAAC,MAAM;IACd,IAAI,CAACkD,iBAAiB,CAACmB,OAAO,IAAI,CAAClB,YAAY,CAACkB,OAAO,IAAI,CAACgC,KAAK,CAACC,OAAO,CAAC5C,SAAS,CAAC,IAAIA,SAAS,CAAChC,MAAM,KAAK,CAAC,EAAE;MAC9G;IACF;IAEA,IAAI;MACFuC,aAAa,CAAC,IAAI,CAAC;MACnBF,aAAa,CAAC,IAAI7C,IAAI,CAAC,CAAC,CAAC;;MAEzB;MACA,MAAMqJ,UAAU,GAAG7G,SAAS,CACzBmG,MAAM,CAAC9B,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAAC3G,SAAS,IAAI,OAAO2G,IAAI,CAACgB,IAAI,KAAK,QAAQ,CAAC,CACvEpB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI3G,IAAI,CAAC0G,CAAC,CAACxG,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC,GAAG,IAAI9G,IAAI,CAAC2G,CAAC,CAACzG,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC,CAAC;;MAEpF;MACA,MAAMR,UAAU,GAAG,EAAE;MACrB,MAAMgD,SAAS,GAAG,IAAI9C,GAAG,CAAC,CAAC;MAE3B,KAAK,IAAI+C,CAAC,GAAGF,UAAU,CAAC7I,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/C,MAAMC,OAAO,GAAG,IAAIxJ,IAAI,CAACqJ,UAAU,CAACE,CAAC,CAAC,CAACrJ,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC;QAC3D,IAAI,CAACwC,SAAS,CAACvC,GAAG,CAACyC,OAAO,CAAC,EAAE;UAC3BF,SAAS,CAACtC,GAAG,CAACwC,OAAO,CAAC;UACtBlD,UAAU,CAACmD,OAAO,CAACJ,UAAU,CAACE,CAAC,CAAC,CAAC;QACnC;MACF;MAEA,MAAMG,aAAa,GAAGpD,UAAU,CAAC+B,GAAG,CAACxB,IAAI,KAAK;QAC5Cc,IAAI,EAAE,IAAI3H,IAAI,CAAC6G,IAAI,CAAC3G,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC,GAAG,IAAI;QAC/Ce,IAAI,EAAEhB,IAAI,CAACgB,IAAI;QACfY,IAAI,EAAE5B,IAAI,CAAC4B,IAAI;QACfC,GAAG,EAAE7B,IAAI,CAAC6B,GAAG;QACbd,KAAK,EAAEf,IAAI,CAACe;MACd,CAAC,CAAC,CAAC;MAEH,MAAMsB,UAAU,GAAG5C,UAAU,CAC1BqC,MAAM,CAAC9B,IAAI,IAAI,OAAOA,IAAI,CAACa,MAAM,KAAK,QAAQ,CAAC,CAC/CW,GAAG,CAACxB,IAAI,KAAK;QACZc,IAAI,EAAE,IAAI3H,IAAI,CAAC6G,IAAI,CAAC3G,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC,GAAG,IAAI;QAC/C5D,KAAK,EAAE2D,IAAI,CAACa,MAAM;QAClBlG,KAAK,EAAEqF,IAAI,CAACe,KAAK,IAAIf,IAAI,CAACgB,IAAI,GAAG,WAAW,GAAG;MACjD,CAAC,CAAC,CAAC;MAEL1H,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsJ,aAAa,CAAClJ,MAAM,EAAE,YAAY,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACoJ,kBAAkB,CAAC,CAAC,CAAC;MAEzG,IAAIM,aAAa,CAAClJ,MAAM,GAAG,CAAC,EAAE;QAC5BwB,iBAAiB,CAACmB,OAAO,CAACgG,OAAO,CAACO,aAAa,CAAC;MAClD;MAEA,IAAIR,UAAU,CAAC1I,MAAM,GAAG,CAAC,EAAE;QACzByB,YAAY,CAACkB,OAAO,CAACgG,OAAO,CAACD,UAAU,CAAC;MAC1C;;MAEA;MACApB,UAAU,CAAC,MAAM/E,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;IAE7C,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACd9E,OAAO,CAAC8E,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDlC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;;EAEf;EACA1D,SAAS,CAAC,MAAM;IACd,IAAI,CAACkD,iBAAiB,CAACmB,OAAO,IAAI,CAACgC,KAAK,CAACC,OAAO,CAACxD,OAAO,CAAC,IAAIA,OAAO,CAACpB,MAAM,KAAK,CAAC,EAAE;IAEnF,IAAI;MACF,MAAMmJ,OAAO,GAAG/H,OAAO,CACpB+G,MAAM,CAACiB,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACA,MAAM,KAAK,MAAM,IAAIA,MAAM,CAAC1J,SAAS,CAAC,CACzFmI,GAAG,CAACuB,MAAM,IAAI;QACb,MAAMC,KAAK,GAAGD,MAAM,CAACA,MAAM,KAAK,KAAK;QACrC,OAAO;UACLjC,IAAI,EAAE,IAAI3H,IAAI,CAAC4J,MAAM,CAAC1J,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC,GAAG,IAAI;UACjDgD,QAAQ,EAAED,KAAK,GAAG,UAAU,GAAG,UAAU;UACzCrI,KAAK,EAAEqI,KAAK,GAAG,SAAS,GAAG,SAAS;UACpCE,KAAK,EAAEF,KAAK,GAAG,SAAS,GAAG,WAAW;UACtCG,IAAI,EAAE,GAAGJ,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACK,KAAK,GAAG,KAAKL,MAAM,CAACK,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAACN,MAAM,CAACO,UAAU,IAAI,CAAC,IAAI,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,IAAI;UAChIE,IAAI,EAAE,CAAC,CAAE;QACX,CAAC;MACH,CAAC,CAAC;;MAEJ;MACA,IAAIxI,OAAO,CAACyI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,EAAE;QACjC,MAAMC,eAAe,GAAG5I,OAAO,CAC5B+G,MAAM,CAACiB,MAAM,IAAIA,MAAM,CAACW,QAAQ,IAAIX,MAAM,CAACa,eAAe,CAAC,CAC3DpC,GAAG,CAACuB,MAAM,IAAI;UACb,MAAMC,KAAK,GAAGD,MAAM,CAACA,MAAM,KAAK,KAAK;UACrC,OAAO;YACLjC,IAAI,EAAE,IAAI3H,IAAI,CAAC4J,MAAM,CAACc,mBAAmB,IAAId,MAAM,CAAC1J,SAAS,CAAC,CAAC4G,OAAO,CAAC,CAAC,GAAG,IAAI;YAC/EgD,QAAQ,EAAED,KAAK,GAAG,UAAU,GAAG,UAAU;YACzCrI,KAAK,EAAEqI,KAAK,GAAG,SAAS,GAAG,SAAS;YAAE;YACtCE,KAAK,EAAE,QAAQ;YACfC,IAAI,EAAE,cAAcJ,MAAM,CAACA,MAAM,MAAMA,MAAM,CAACa,eAAe,CAACP,OAAO,CAAC,CAAC,CAAC,EAAE;YAC1EE,IAAI,EAAE,CAAC,CAAE;UACX,CAAC;QACH,CAAC,CAAC;QAEJT,OAAO,CAAC1C,IAAI,CAAC,GAAGuD,eAAe,CAAC;MAClC;MAEAxI,iBAAiB,CAACmB,OAAO,CAACwH,UAAU,CAAChB,OAAO,CAAC;MAC7CxJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuJ,OAAO,CAACnJ,MAAM,EAAE,eAAe,CAAC;IAC3E,CAAC,CAAC,OAAOyE,KAAK,EAAE;MACd9E,OAAO,CAAC8E,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC,EAAE,CAACrD,OAAO,CAAC,CAAC;EAEb,MAAMgJ,qBAAqB,GAAIjL,SAAS,IAAK;IAC3CQ,OAAO,CAACC,GAAG,CAAC,+BAA+BkC,iBAAiB,OAAO3C,SAAS,QAAQD,MAAM,EAAE,CAAC;IAC7F6C,oBAAoB,CAAC5C,SAAS,CAAC;IAC/B;IACA8C,YAAY,CAAC,EAAE,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,oBACEhD,OAAA,CAACoB,cAAc;IAAAoK,QAAA,gBACbxL,OAAA,CAACuB,WAAW;MAAAiK,QAAA,gBACVxL,OAAA;QAAKyL,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjExL,OAAA;UAAAwL,QAAA,GAAKnL,MAAM,EAAC,aAAW;QAAA;UAAAwL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BhM,OAAA;UAAKyL,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAJ,QAAA,gBAChExL,OAAA;YAAKyL,KAAK,EAAE;cACVQ,KAAK,EAAE,KAAK;cACZC,MAAM,EAAE,KAAK;cACbC,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAE3I,UAAU,GAAG,SAAS,GAAG,MAAM;cAChD4I,SAAS,EAAE5I,UAAU,GAAG,mBAAmB,GAAG;YAChD;UAAE;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACLhM,OAAA;YAAMyL,KAAK,EAAE;cACXa,QAAQ,EAAE,MAAM;cAChBnK,KAAK,EAAE,MAAM;cACboK,UAAU,EAAE;YACd,CAAE;YAAAf,QAAA,EACCjI,UAAU,GAAG,SAASA,UAAU,CAACwG,kBAAkB,CAAC,CAAC,EAAE,GAAG;UAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhM,OAAA;QAAKyL,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjExL,OAAA,CAACyB,iBAAiB;UAAA+J,QAAA,gBAChBxL,OAAA;YAAMyL,KAAK,EAAE;cAAEtJ,KAAK,EAAE,MAAM;cAAEmK,QAAQ,EAAE,MAAM;cAAEE,WAAW,EAAE;YAAM,CAAE;YAAAhB,QAAA,EAAC;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACtFrI,UAAU,CAACqF,GAAG,CAACyD,EAAE,iBAChBzM,OAAA,CAAC2B,eAAe;YAEdG,OAAO,EAAEmB,iBAAiB,KAAKwJ,EAAE,CAAC5I,KAAM;YACxC6I,OAAO,EAAEA,CAAA,KAAMnB,qBAAqB,CAACkB,EAAE,CAAC5I,KAAK,CAAE;YAAA2H,QAAA,EAE9CiB,EAAE,CAAC7I;UAAK,GAJJ6I,EAAE,CAAC5I,KAAK;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKE,CAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC,eACpBhM,OAAA,CAACgC,SAAS;UAAAwJ,QAAA,gBACRxL,OAAA,CAACkC,SAAS;YAAAsJ,QAAA,gBACRxL,OAAA;cAAM2M,SAAS,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5ChM,OAAA;cAAM2M,SAAS,EAAC,OAAO;cAAAnB,QAAA,GAAC,GAAC,EAAC,CAAA3I,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgI,OAAO,CAAC,CAAC,CAAC,KAAI,QAAQ;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACZhM,OAAA,CAACkC,SAAS;YAACC,KAAK,EAAEY,WAAW,IAAI,CAAC,GAAG,SAAS,GAAG,SAAU;YAAAyI,QAAA,gBACzDxL,OAAA;cAAM2M,SAAS,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrChM,OAAA;cAAM2M,SAAS,EAAC,OAAO;cAAAnB,QAAA,GACpBzI,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,WAAW,CAAC8H,OAAO,CAAC,CAAC,CAAC,EAAC,GACvD;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACdhM,OAAA;MAAKyL,KAAK,EAAE;QAAEhB,QAAQ,EAAE;MAAW,CAAE;MAAAe,QAAA,gBACnCxL,OAAA;QAAK4M,GAAG,EAAEnK,iBAAkB;QAACgJ,KAAK,EAAE;UAAEQ,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAQ;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACzE3I,OAAO,iBACNrD,OAAA;QAAKyL,KAAK,EAAE;UACVhB,QAAQ,EAAE,UAAU;UACpB/E,GAAG,EAAE,KAAK;UACVmH,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE,uBAAuB;UAClC9I,UAAU,EAAE,oBAAoB;UAChC7B,KAAK,EAAE,MAAM;UACb4K,OAAO,EAAE,WAAW;UACpBZ,YAAY,EAAE,KAAK;UACnBG,QAAQ,EAAE,MAAM;UAChBU,MAAM,EAAE;QACV,CAAE;QAAAxB,QAAA,GAAC,UACO,EAACvI,iBAAiB,EAAC,UAC7B;MAAA;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAErB,CAAC;AAACxJ,EAAA,CAteIH,WAAW;AAAA4K,GAAA,GAAX5K,WAAW;AAwejB,eAAeA,WAAW;AAAC,IAAAf,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAA6K,GAAA;AAAAC,YAAA,CAAA5L,EAAA;AAAA4L,YAAA,CAAA1L,GAAA;AAAA0L,YAAA,CAAAxL,GAAA;AAAAwL,YAAA,CAAAnL,GAAA;AAAAmL,YAAA,CAAAjL,GAAA;AAAAiL,YAAA,CAAA9K,GAAA;AAAA8K,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}