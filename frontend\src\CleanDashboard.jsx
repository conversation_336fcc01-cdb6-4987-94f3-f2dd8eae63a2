import React, { useState } from 'react';
import styled from 'styled-components';
import CryptoPairSelector from './CryptoPairSelector';
import PairAnalytics from './PairAnalytics';
import AdvancedChart from './AdvancedChart';
import SimpleChart from './SimpleChart';
import PortfolioPanel from './PortfolioPanel';
import DecisionPanel from './DecisionPanel';
import PerformancePanel from './PerformancePanel';
import RiskDashboard from './RiskDashboard';
import ActivePositionsDashboard from './ActivePositionsDashboard';
import ControlPanel from './ControlPanel';
import ApiConfigPanel from './ApiConfigPanel';
import AlpacaMarketData from './AlpacaMarketData';
import AlpacaAccount from './AlpacaAccount';
import AIThinkingDashboard from './AIThinkingDashboard';

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #0a0a0a;
  min-height: calc(100vh - 80px);
  
  @media (max-width: 768px) {
    padding: 12px;
    gap: 12px;
  }
`;

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
  
  @media (max-width: 768px) {
    gap: 12px;
  }
`;

const MainSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const SideSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const TabContainer = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  border-bottom: 1px solid #333;
  padding-bottom: 8px;
`;

const Tab = styled.button`
  background: ${props => props.$active ? '#4bffb5' : 'transparent'};
  color: ${props => props.$active ? '#000' : '#4bffb5'};
  border: 1px solid #4bffb5;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? '#4bffb5' : 'rgba(75, 255, 181, 0.1)'};
  }
`;

const BottomGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-top: 12px;
  }
`;

const SectionTitle = styled.h3`
  color: #4bffb5;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #333;
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 12px 16px;
  background: rgba(75, 255, 181, 0.1);
  border: 1px solid rgba(75, 255, 181, 0.3);
  border-radius: 12px;
  font-size: 12px;
  color: #4bffb5;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
`;

const CleanDashboard = ({
  marketData,
  portfolio,
  signals,
  performance,
  tradingActive,
  dataSource,
  wsConnected,
  onStartTrading,
  onStopTrading,
  onApiUpdate,
  onDataSourceSwitch
}) => {
  const [activeChartTab, setActiveChartTab] = useState('advanced');
  const [activeSideTab, setActiveSideTab] = useState('portfolio');
  const [currentPair, setCurrentPair] = useState('XRP/USD');
  const [pairSwitching, setPairSwitching] = useState(false);
  const [pairMarketData, setPairMarketData] = useState(marketData || []);
  const [pairSignals, setPairSignals] = useState(signals || []);

  // Update pair-specific data when marketData or signals change
  React.useEffect(() => {
    setPairMarketData(marketData || []);
    setPairSignals(signals || []);
  }, [marketData, signals]);

  const handlePairChange = async (newPair) => {
    if (newPair === currentPair) return;

    setPairSwitching(true);
    try {
      console.log('🔄 Switching crypto pair with data isolation:', newPair);

      // 🧹 STEP 1: Clear all cached frontend data to prevent mixing
      console.log('🧹 Clearing frontend cached data to prevent mixing');
      setPairMarketData([]);  // Clear market data for this component
      setPairSignals([]);     // Clear signals for this component

      // 🔄 STEP 2: Switch the pair on backend with data isolation
      const response = await fetch('http://localhost:8000/api/crypto-pairs/switch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ symbol: newPair }),
      });

      const data = await response.json();

      if (data.success) {
        console.log('✅ Crypto pair switched successfully with data isolation:', data);
        console.log(`📊 Data isolation: ${data.data_isolation}, Old data cleared: ${data.old_data_cleared}`);

        // 🔄 STEP 3: Update current pair
        setCurrentPair(newPair);

        // 🔄 STEP 4: Wait a moment for backend to fetch fresh data
        console.log('⏳ Waiting for fresh data to be fetched...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 🔄 STEP 5: Fetch new pair's market data immediately
        console.log('🚀 Fetching fresh market data for new pair');
        await fetchPairData(newPair);

        // 🔄 STEP 6: Trigger a data refresh to update all components
        if (onDataSourceSwitch) {
          onDataSourceSwitch('refresh');
        }

        console.log(`✅ Successfully switched to ${newPair} with clean data isolation`);
      } else {
        console.error('Failed to switch pair:', data.message);
      }
    } catch (error) {
      console.error('Error switching pair:', error);
    } finally {
      setPairSwitching(false);
    }
  };

  const fetchPairData = async (pair) => {
    try {
      // Fetch market data for the specific pair
      const marketResponse = await fetch(`http://localhost:8000/market-data?limit=200&pair=${encodeURIComponent(pair)}`, {
        credentials: 'include',
      });

      if (marketResponse.ok) {
        const marketDataResult = await marketResponse.json();
        setPairMarketData(marketDataResult || []);
        console.log(`Fetched ${marketDataResult?.length || 0} market data points for ${pair}`);
      }

      // Fetch signals for the specific pair
      const signalsResponse = await fetch(`http://localhost:8000/signals?limit=50&pair=${encodeURIComponent(pair)}`, {
        credentials: 'include',
      });

      if (signalsResponse.ok) {
        const signalsResult = await signalsResponse.json();
        setPairSignals(signalsResult || []);
        console.log(`Fetched ${signalsResult?.length || 0} signals for ${pair}`);
      }
    } catch (error) {
      console.error('Error fetching pair data:', error);
    }
  };

  return (
    <DashboardContainer>
      {/* Status Bar with Crypto Pair Selector */}
      <StatusIndicator>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <CryptoPairSelector
            currentPair={currentPair}
            onPairChange={handlePairChange}
          />
          {pairSwitching && (
            <span style={{ color: '#ffa726', fontSize: '12px' }}>
              🔄 Switching pairs...
            </span>
          )}
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span>🔴 {dataSource === 'live' ? 'LIVE DATA' : 'MOCK DATA'}</span>
          <span>•</span>
          <span>{wsConnected ? '🟢 Connected' : '🔴 Disconnected'}</span>
          <span>•</span>
          <span>{tradingActive ? '🟢 Trading Active' : '🟡 Trading Paused'}</span>
        </div>
      </StatusIndicator>

      {/* Main Dashboard Grid */}
      <DashboardGrid>
        {/* Main Chart Section */}
        <MainSection>
          <div>
            <TabContainer>
              <Tab
                $active={activeChartTab === 'advanced'}
                onClick={() => setActiveChartTab('advanced')}
              >
                📈 Advanced Chart
              </Tab>
              <Tab
                $active={activeChartTab === 'simple'}
                onClick={() => setActiveChartTab('simple')}
              >
                📊 Simple Chart
              </Tab>
            </TabContainer>
            
            {activeChartTab === 'advanced' ? (
              <AdvancedChart
                marketData={pairMarketData}
                signals={pairSignals}
                symbol={currentPair}
                key={currentPair} // Force re-render when pair changes
              />
            ) : (
              <SimpleChart
                marketData={pairMarketData}
                signals={pairSignals}
                symbol={currentPair}
                key={currentPair} // Force re-render when pair changes
              />
            )}
          </div>
        </MainSection>

        {/* Side Panel */}
        <SideSection>
          <TabContainer>
            <Tab
              $active={activeSideTab === 'portfolio'}
              onClick={() => setActiveSideTab('portfolio')}
            >
              💼 Portfolio
            </Tab>
            <Tab
              $active={activeSideTab === 'decisions'}
              onClick={() => setActiveSideTab('decisions')}
            >
              🧠 AI Decisions
            </Tab>
            <Tab
              $active={activeSideTab === 'analytics'}
              onClick={() => setActiveSideTab('analytics')}
            >
              📊 Pair Analytics
            </Tab>
            <Tab
              $active={activeSideTab === 'control'}
              onClick={() => setActiveSideTab('control')}
            >
              ⚙️ Controls
            </Tab>
            <Tab
              $active={activeSideTab === 'alpaca'}
              onClick={() => setActiveSideTab('alpaca')}
            >
              🚀 Alpaca Live
            </Tab>
          </TabContainer>

          {activeSideTab === 'portfolio' && (
            <PortfolioPanel portfolio={portfolio} key={currentPair} />
          )}

          {activeSideTab === 'decisions' && (
            <DecisionPanel signals={signals} key={currentPair} />
          )}

          {activeSideTab === 'analytics' && (
            <PairAnalytics currentPair={currentPair} key={currentPair} />
          )}

          {activeSideTab === 'control' && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <ControlPanel
                tradingActive={tradingActive}
                onStart={onStartTrading}
                onStop={onStopTrading}
              />
              <ApiConfigPanel
                onUpdate={onApiUpdate}
                onDataSourceSwitch={onDataSourceSwitch}
                currentDataSource={dataSource}
              />
            </div>
          )}

          {activeSideTab === 'alpaca' && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <AlpacaAccount />
            </div>
          )}
        </SideSection>
      </DashboardGrid>

      {/* AI Thinking Process Section */}
      <div>
        <SectionTitle>🧠 AI Decision-Making Process</SectionTitle>
        <AIThinkingDashboard />
      </div>

      {/* Alpaca Live Market Data Section */}
      <div>
        <SectionTitle>🚀 Live Alpaca Market Data</SectionTitle>
        <AlpacaMarketData />
      </div>

      {/* Bottom Section - Key Metrics */}
      <div>
        <SectionTitle>📊 Key Metrics & Analytics</SectionTitle>
        <BottomGrid>
          <PerformancePanel performance={performance} />
          <ActivePositionsDashboard />
          <RiskDashboard />
        </BottomGrid>
      </div>
    </DashboardContainer>
  );
};

export default CleanDashboard;
