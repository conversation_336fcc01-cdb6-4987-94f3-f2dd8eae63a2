{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\CleanDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport CryptoPairSelector from './CryptoPairSelector';\nimport PairAnalytics from './PairAnalytics';\nimport AdvancedChart from './AdvancedChart';\nimport SimpleChart from './SimpleChart';\nimport PortfolioPanel from './PortfolioPanel';\nimport DecisionPanel from './DecisionPanel';\nimport PerformancePanel from './PerformancePanel';\nimport RiskDashboard from './RiskDashboard';\nimport ActivePositionsDashboard from './ActivePositionsDashboard';\nimport ControlPanel from './ControlPanel';\nimport ApiConfigPanel from './ApiConfigPanel';\nimport AlpacaMarketData from './AlpacaMarketData';\nimport AlpacaAccount from './AlpacaAccount';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  padding: 20px;\n  background: #0a0a0a;\n  min-height: calc(100vh - 80px);\n  \n  @media (max-width: 768px) {\n    padding: 12px;\n    gap: 12px;\n  }\n`;\n_c = DashboardContainer;\nconst DashboardGrid = styled.div`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  \n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr;\n  }\n  \n  @media (max-width: 768px) {\n    gap: 12px;\n  }\n`;\n_c2 = DashboardGrid;\nconst MainSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c3 = MainSection;\nconst SideSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c4 = SideSection;\nconst TabContainer = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-bottom: 16px;\n  border-bottom: 1px solid #333;\n  padding-bottom: 8px;\n`;\n_c5 = TabContainer;\nconst Tab = styled.button`\n  background: ${props => props.$active ? '#4bffb5' : 'transparent'};\n  color: ${props => props.$active ? '#000' : '#4bffb5'};\n  border: 1px solid #4bffb5;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#4bffb5' : 'rgba(75, 255, 181, 0.1)'};\n  }\n`;\n_c6 = Tab;\nconst BottomGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 20px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 12px;\n    margin-top: 12px;\n  }\n`;\n_c7 = BottomGrid;\nconst SectionTitle = styled.h3`\n  color: #4bffb5;\n  font-size: 18px;\n  font-weight: 600;\n  margin: 0 0 16px 0;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #333;\n`;\n_c8 = SectionTitle;\nconst StatusIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 16px;\n  padding: 12px 16px;\n  background: rgba(75, 255, 181, 0.1);\n  border: 1px solid rgba(75, 255, 181, 0.3);\n  border-radius: 12px;\n  font-size: 12px;\n  color: #4bffb5;\n  margin-bottom: 16px;\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n  }\n`;\n_c9 = StatusIndicator;\nconst CleanDashboard = ({\n  marketData,\n  portfolio,\n  signals,\n  performance,\n  tradingActive,\n  dataSource,\n  wsConnected,\n  onStartTrading,\n  onStopTrading,\n  onApiUpdate,\n  onDataSourceSwitch\n}) => {\n  _s();\n  const [activeChartTab, setActiveChartTab] = useState('advanced');\n  const [activeSideTab, setActiveSideTab] = useState('portfolio');\n  const [currentPair, setCurrentPair] = useState('XRP/USD');\n  const [pairSwitching, setPairSwitching] = useState(false);\n  const [pairMarketData, setPairMarketData] = useState(marketData || []);\n  const [pairSignals, setPairSignals] = useState(signals || []);\n\n  // Update pair-specific data when marketData or signals change\n  React.useEffect(() => {\n    setPairMarketData(marketData || []);\n    setPairSignals(signals || []);\n  }, [marketData, signals]);\n  const handlePairChange = async newPair => {\n    if (newPair === currentPair) return;\n    setPairSwitching(true);\n    try {\n      console.log('🔄 Switching crypto pair with data isolation:', newPair);\n\n      // 🧹 STEP 1: Clear all cached frontend data to prevent mixing\n      console.log('🧹 Clearing frontend cached data to prevent mixing');\n      setPairMarketData([]); // Clear market data for this component\n      setPairSignals([]); // Clear signals for this component\n\n      // 🔄 STEP 2: Switch the pair on backend with data isolation\n      const response = await fetch('http://localhost:8000/api/crypto-pairs/switch', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          symbol: newPair\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        console.log('✅ Crypto pair switched successfully with data isolation:', data);\n        console.log(`📊 Data isolation: ${data.data_isolation}, Old data cleared: ${data.old_data_cleared}`);\n\n        // 🔄 STEP 3: Update current pair\n        setCurrentPair(newPair);\n\n        // 🔄 STEP 4: Wait a moment for backend to fetch fresh data\n        console.log('⏳ Waiting for fresh data to be fetched...');\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 🔄 STEP 5: Fetch new pair's market data immediately\n        console.log('🚀 Fetching fresh market data for new pair');\n        await fetchPairData(newPair);\n\n        // 🔄 STEP 6: Trigger a data refresh to update all components\n        if (onDataSourceSwitch) {\n          onDataSourceSwitch('refresh');\n        }\n        console.log(`✅ Successfully switched to ${newPair} with clean data isolation`);\n      } else {\n        console.error('Failed to switch pair:', data.message);\n      }\n    } catch (error) {\n      console.error('Error switching pair:', error);\n    } finally {\n      setPairSwitching(false);\n    }\n  };\n  const fetchPairData = async pair => {\n    try {\n      // Fetch market data for the specific pair\n      const marketResponse = await fetch(`http://localhost:8000/market-data?limit=200&pair=${encodeURIComponent(pair)}`, {\n        credentials: 'include'\n      });\n      if (marketResponse.ok) {\n        const marketDataResult = await marketResponse.json();\n        setPairMarketData(marketDataResult || []);\n        console.log(`Fetched ${(marketDataResult === null || marketDataResult === void 0 ? void 0 : marketDataResult.length) || 0} market data points for ${pair}`);\n      }\n\n      // Fetch signals for the specific pair\n      const signalsResponse = await fetch(`http://localhost:8000/signals?limit=50&pair=${encodeURIComponent(pair)}`, {\n        credentials: 'include'\n      });\n      if (signalsResponse.ok) {\n        const signalsResult = await signalsResponse.json();\n        setPairSignals(signalsResult || []);\n        console.log(`Fetched ${(signalsResult === null || signalsResult === void 0 ? void 0 : signalsResult.length) || 0} signals for ${pair}`);\n      }\n    } catch (error) {\n      console.error('Error fetching pair data:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DashboardContainer, {\n    children: [/*#__PURE__*/_jsxDEV(StatusIndicator, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CryptoPairSelector, {\n          currentPair: currentPair,\n          onPairChange: handlePairChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), pairSwitching && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#ffa726',\n            fontSize: '12px'\n          },\n          children: \"\\uD83D\\uDD04 Switching pairs...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDD34 \", dataSource === 'live' ? 'LIVE DATA' : 'MOCK DATA']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2022\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: wsConnected ? '🟢 Connected' : '🔴 Disconnected'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2022\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: tradingActive ? '🟢 Trading Active' : '🟡 Trading Paused'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DashboardGrid, {\n      children: [/*#__PURE__*/_jsxDEV(MainSection, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(TabContainer, {\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              $active: activeChartTab === 'advanced',\n              onClick: () => setActiveChartTab('advanced'),\n              children: \"\\uD83D\\uDCC8 Advanced Chart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              $active: activeChartTab === 'simple',\n              onClick: () => setActiveChartTab('simple'),\n              children: \"\\uD83D\\uDCCA Simple Chart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), activeChartTab === 'advanced' ? /*#__PURE__*/_jsxDEV(AdvancedChart, {\n            marketData: pairMarketData,\n            signals: pairSignals,\n            symbol: currentPair\n          }, currentPair, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(SimpleChart, {\n            marketData: pairMarketData,\n            signals: pairSignals,\n            symbol: currentPair\n          }, currentPair, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SideSection, {\n        children: [/*#__PURE__*/_jsxDEV(TabContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            $active: activeSideTab === 'portfolio',\n            onClick: () => setActiveSideTab('portfolio'),\n            children: \"\\uD83D\\uDCBC Portfolio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            $active: activeSideTab === 'decisions',\n            onClick: () => setActiveSideTab('decisions'),\n            children: \"\\uD83E\\uDDE0 AI Decisions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            $active: activeSideTab === 'analytics',\n            onClick: () => setActiveSideTab('analytics'),\n            children: \"\\uD83D\\uDCCA Pair Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            $active: activeSideTab === 'control',\n            onClick: () => setActiveSideTab('control'),\n            children: \"\\u2699\\uFE0F Controls\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            $active: activeSideTab === 'alpaca',\n            onClick: () => setActiveSideTab('alpaca'),\n            children: \"\\uD83D\\uDE80 Alpaca Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), activeSideTab === 'portfolio' && /*#__PURE__*/_jsxDEV(PortfolioPanel, {\n          portfolio: portfolio\n        }, currentPair, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this), activeSideTab === 'decisions' && /*#__PURE__*/_jsxDEV(DecisionPanel, {\n          signals: signals\n        }, currentPair, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), activeSideTab === 'analytics' && /*#__PURE__*/_jsxDEV(PairAnalytics, {\n          currentPair: currentPair\n        }, currentPair, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this), activeSideTab === 'control' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ControlPanel, {\n            tradingActive: tradingActive,\n            onStart: onStartTrading,\n            onStop: onStopTrading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ApiConfigPanel, {\n            onUpdate: onApiUpdate,\n            onDataSourceSwitch: onDataSourceSwitch,\n            currentDataSource: dataSource\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), activeSideTab === 'alpaca' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(AlpacaAccount, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\uD83D\\uDE80 Live Alpaca Market Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AlpacaMarketData, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\uD83D\\uDCCA Key Metrics & Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BottomGrid, {\n        children: [/*#__PURE__*/_jsxDEV(PerformancePanel, {\n          performance: performance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActivePositionsDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RiskDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s(CleanDashboard, \"L+uxbnSttNn1yfJBQ1nC9MTlRNA=\");\n_c0 = CleanDashboard;\nexport default CleanDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"DashboardContainer\");\n$RefreshReg$(_c2, \"DashboardGrid\");\n$RefreshReg$(_c3, \"MainSection\");\n$RefreshReg$(_c4, \"SideSection\");\n$RefreshReg$(_c5, \"TabContainer\");\n$RefreshReg$(_c6, \"Tab\");\n$RefreshReg$(_c7, \"BottomGrid\");\n$RefreshReg$(_c8, \"SectionTitle\");\n$RefreshReg$(_c9, \"StatusIndicator\");\n$RefreshReg$(_c0, \"CleanDashboard\");", "map": {"version": 3, "names": ["React", "useState", "styled", "CryptoPairSelector", "PairAnalytics", "AdvancedChart", "<PERSON><PERSON><PERSON>", "PortfolioPanel", "DecisionPanel", "PerformancePanel", "RiskDashboard", "ActivePositionsDashboard", "ControlPanel", "ApiConfigPanel", "AlpacaMarketData", "AlpacaAccount", "jsxDEV", "_jsxDEV", "DashboardContainer", "div", "_c", "DashboardGrid", "_c2", "MainSection", "_c3", "SideSection", "_c4", "TabContainer", "_c5", "Tab", "button", "props", "$active", "_c6", "BottomGrid", "_c7", "SectionTitle", "h3", "_c8", "StatusIndicator", "_c9", "CleanDashboard", "marketData", "portfolio", "signals", "performance", "tradingActive", "dataSource", "wsConnected", "onStartTrading", "onStopTrading", "onApiUpdate", "onDataSourceSwitch", "_s", "activeChartTab", "setActiveChartTab", "activeSideTab", "setActiveSideTab", "currentPair", "setCurrentPair", "pairSwitching", "setPairSwitching", "pairMarketData", "setPairMarketData", "pairSignals", "setPairSignals", "useEffect", "handlePairChange", "newPair", "console", "log", "response", "fetch", "method", "headers", "credentials", "body", "JSON", "stringify", "symbol", "data", "json", "success", "data_isolation", "old_data_cleared", "Promise", "resolve", "setTimeout", "fetchPairData", "error", "message", "pair", "marketResponse", "encodeURIComponent", "ok", "marketDataResult", "length", "signalsResponse", "signalsResult", "children", "style", "display", "alignItems", "gap", "onPairChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "onClick", "flexDirection", "onStart", "onStop", "onUpdate", "currentDataSource", "_c0", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/CleanDashboard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport CryptoPairSelector from './CryptoPairSelector';\nimport PairAnalytics from './PairAnalytics';\nimport AdvancedChart from './AdvancedChart';\nimport SimpleChart from './SimpleChart';\nimport PortfolioPanel from './PortfolioPanel';\nimport DecisionPanel from './DecisionPanel';\nimport PerformancePanel from './PerformancePanel';\nimport RiskDashboard from './RiskDashboard';\nimport ActivePositionsDashboard from './ActivePositionsDashboard';\nimport ControlPanel from './ControlPanel';\nimport ApiConfigPanel from './ApiConfigPanel';\nimport AlpacaMarketData from './AlpacaMarketData';\nimport AlpacaAccount from './AlpacaAccount';\n\nconst DashboardContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  padding: 20px;\n  background: #0a0a0a;\n  min-height: calc(100vh - 80px);\n  \n  @media (max-width: 768px) {\n    padding: 12px;\n    gap: 12px;\n  }\n`;\n\nconst DashboardGrid = styled.div`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  \n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr;\n  }\n  \n  @media (max-width: 768px) {\n    gap: 12px;\n  }\n`;\n\nconst MainSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst SideSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst TabContainer = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-bottom: 16px;\n  border-bottom: 1px solid #333;\n  padding-bottom: 8px;\n`;\n\nconst Tab = styled.button`\n  background: ${props => props.$active ? '#4bffb5' : 'transparent'};\n  color: ${props => props.$active ? '#000' : '#4bffb5'};\n  border: 1px solid #4bffb5;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#4bffb5' : 'rgba(75, 255, 181, 0.1)'};\n  }\n`;\n\nconst BottomGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 20px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 12px;\n    margin-top: 12px;\n  }\n`;\n\nconst SectionTitle = styled.h3`\n  color: #4bffb5;\n  font-size: 18px;\n  font-weight: 600;\n  margin: 0 0 16px 0;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #333;\n`;\n\nconst StatusIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 16px;\n  padding: 12px 16px;\n  background: rgba(75, 255, 181, 0.1);\n  border: 1px solid rgba(75, 255, 181, 0.3);\n  border-radius: 12px;\n  font-size: 12px;\n  color: #4bffb5;\n  margin-bottom: 16px;\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n  }\n`;\n\nconst CleanDashboard = ({\n  marketData,\n  portfolio,\n  signals,\n  performance,\n  tradingActive,\n  dataSource,\n  wsConnected,\n  onStartTrading,\n  onStopTrading,\n  onApiUpdate,\n  onDataSourceSwitch\n}) => {\n  const [activeChartTab, setActiveChartTab] = useState('advanced');\n  const [activeSideTab, setActiveSideTab] = useState('portfolio');\n  const [currentPair, setCurrentPair] = useState('XRP/USD');\n  const [pairSwitching, setPairSwitching] = useState(false);\n  const [pairMarketData, setPairMarketData] = useState(marketData || []);\n  const [pairSignals, setPairSignals] = useState(signals || []);\n\n  // Update pair-specific data when marketData or signals change\n  React.useEffect(() => {\n    setPairMarketData(marketData || []);\n    setPairSignals(signals || []);\n  }, [marketData, signals]);\n\n  const handlePairChange = async (newPair) => {\n    if (newPair === currentPair) return;\n\n    setPairSwitching(true);\n    try {\n      console.log('🔄 Switching crypto pair with data isolation:', newPair);\n\n      // 🧹 STEP 1: Clear all cached frontend data to prevent mixing\n      console.log('🧹 Clearing frontend cached data to prevent mixing');\n      setPairMarketData([]);  // Clear market data for this component\n      setPairSignals([]);     // Clear signals for this component\n\n      // 🔄 STEP 2: Switch the pair on backend with data isolation\n      const response = await fetch('http://localhost:8000/api/crypto-pairs/switch', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ symbol: newPair }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        console.log('✅ Crypto pair switched successfully with data isolation:', data);\n        console.log(`📊 Data isolation: ${data.data_isolation}, Old data cleared: ${data.old_data_cleared}`);\n\n        // 🔄 STEP 3: Update current pair\n        setCurrentPair(newPair);\n\n        // 🔄 STEP 4: Wait a moment for backend to fetch fresh data\n        console.log('⏳ Waiting for fresh data to be fetched...');\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 🔄 STEP 5: Fetch new pair's market data immediately\n        console.log('🚀 Fetching fresh market data for new pair');\n        await fetchPairData(newPair);\n\n        // 🔄 STEP 6: Trigger a data refresh to update all components\n        if (onDataSourceSwitch) {\n          onDataSourceSwitch('refresh');\n        }\n\n        console.log(`✅ Successfully switched to ${newPair} with clean data isolation`);\n      } else {\n        console.error('Failed to switch pair:', data.message);\n      }\n    } catch (error) {\n      console.error('Error switching pair:', error);\n    } finally {\n      setPairSwitching(false);\n    }\n  };\n\n  const fetchPairData = async (pair) => {\n    try {\n      // Fetch market data for the specific pair\n      const marketResponse = await fetch(`http://localhost:8000/market-data?limit=200&pair=${encodeURIComponent(pair)}`, {\n        credentials: 'include',\n      });\n\n      if (marketResponse.ok) {\n        const marketDataResult = await marketResponse.json();\n        setPairMarketData(marketDataResult || []);\n        console.log(`Fetched ${marketDataResult?.length || 0} market data points for ${pair}`);\n      }\n\n      // Fetch signals for the specific pair\n      const signalsResponse = await fetch(`http://localhost:8000/signals?limit=50&pair=${encodeURIComponent(pair)}`, {\n        credentials: 'include',\n      });\n\n      if (signalsResponse.ok) {\n        const signalsResult = await signalsResponse.json();\n        setPairSignals(signalsResult || []);\n        console.log(`Fetched ${signalsResult?.length || 0} signals for ${pair}`);\n      }\n    } catch (error) {\n      console.error('Error fetching pair data:', error);\n    }\n  };\n\n  return (\n    <DashboardContainer>\n      {/* Status Bar with Crypto Pair Selector */}\n      <StatusIndicator>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n          <CryptoPairSelector\n            currentPair={currentPair}\n            onPairChange={handlePairChange}\n          />\n          {pairSwitching && (\n            <span style={{ color: '#ffa726', fontSize: '12px' }}>\n              🔄 Switching pairs...\n            </span>\n          )}\n        </div>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n          <span>🔴 {dataSource === 'live' ? 'LIVE DATA' : 'MOCK DATA'}</span>\n          <span>•</span>\n          <span>{wsConnected ? '🟢 Connected' : '🔴 Disconnected'}</span>\n          <span>•</span>\n          <span>{tradingActive ? '🟢 Trading Active' : '🟡 Trading Paused'}</span>\n        </div>\n      </StatusIndicator>\n\n      {/* Main Dashboard Grid */}\n      <DashboardGrid>\n        {/* Main Chart Section */}\n        <MainSection>\n          <div>\n            <TabContainer>\n              <Tab\n                $active={activeChartTab === 'advanced'}\n                onClick={() => setActiveChartTab('advanced')}\n              >\n                📈 Advanced Chart\n              </Tab>\n              <Tab\n                $active={activeChartTab === 'simple'}\n                onClick={() => setActiveChartTab('simple')}\n              >\n                📊 Simple Chart\n              </Tab>\n            </TabContainer>\n            \n            {activeChartTab === 'advanced' ? (\n              <AdvancedChart\n                marketData={pairMarketData}\n                signals={pairSignals}\n                symbol={currentPair}\n                key={currentPair} // Force re-render when pair changes\n              />\n            ) : (\n              <SimpleChart\n                marketData={pairMarketData}\n                signals={pairSignals}\n                symbol={currentPair}\n                key={currentPair} // Force re-render when pair changes\n              />\n            )}\n          </div>\n        </MainSection>\n\n        {/* Side Panel */}\n        <SideSection>\n          <TabContainer>\n            <Tab\n              $active={activeSideTab === 'portfolio'}\n              onClick={() => setActiveSideTab('portfolio')}\n            >\n              💼 Portfolio\n            </Tab>\n            <Tab\n              $active={activeSideTab === 'decisions'}\n              onClick={() => setActiveSideTab('decisions')}\n            >\n              🧠 AI Decisions\n            </Tab>\n            <Tab\n              $active={activeSideTab === 'analytics'}\n              onClick={() => setActiveSideTab('analytics')}\n            >\n              📊 Pair Analytics\n            </Tab>\n            <Tab\n              $active={activeSideTab === 'control'}\n              onClick={() => setActiveSideTab('control')}\n            >\n              ⚙️ Controls\n            </Tab>\n            <Tab\n              $active={activeSideTab === 'alpaca'}\n              onClick={() => setActiveSideTab('alpaca')}\n            >\n              🚀 Alpaca Live\n            </Tab>\n          </TabContainer>\n\n          {activeSideTab === 'portfolio' && (\n            <PortfolioPanel portfolio={portfolio} key={currentPair} />\n          )}\n\n          {activeSideTab === 'decisions' && (\n            <DecisionPanel signals={signals} key={currentPair} />\n          )}\n\n          {activeSideTab === 'analytics' && (\n            <PairAnalytics currentPair={currentPair} key={currentPair} />\n          )}\n\n          {activeSideTab === 'control' && (\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>\n              <ControlPanel\n                tradingActive={tradingActive}\n                onStart={onStartTrading}\n                onStop={onStopTrading}\n              />\n              <ApiConfigPanel\n                onUpdate={onApiUpdate}\n                onDataSourceSwitch={onDataSourceSwitch}\n                currentDataSource={dataSource}\n              />\n            </div>\n          )}\n\n          {activeSideTab === 'alpaca' && (\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>\n              <AlpacaAccount />\n            </div>\n          )}\n        </SideSection>\n      </DashboardGrid>\n\n      {/* Alpaca Live Market Data Section */}\n      <div>\n        <SectionTitle>🚀 Live Alpaca Market Data</SectionTitle>\n        <AlpacaMarketData />\n      </div>\n\n      {/* Bottom Section - Key Metrics */}\n      <div>\n        <SectionTitle>📊 Key Metrics & Analytics</SectionTitle>\n        <BottomGrid>\n          <PerformancePanel performance={performance} />\n          <ActivePositionsDashboard />\n          <RiskDashboard />\n        </BottomGrid>\n      </div>\n    </DashboardContainer>\n  );\n};\n\nexport default CleanDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,kBAAkB,GAAGhB,MAAM,CAACiB,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAZIF,kBAAkB;AAcxB,MAAMG,aAAa,GAAGnB,MAAM,CAACiB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAZID,aAAa;AAcnB,MAAME,WAAW,GAAGrB,MAAM,CAACiB,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAJID,WAAW;AAMjB,MAAME,WAAW,GAAGvB,MAAM,CAACiB,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,WAAW;AAMjB,MAAME,YAAY,GAAGzB,MAAM,CAACiB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GANID,YAAY;AAQlB,MAAME,GAAG,GAAG3B,MAAM,CAAC4B,MAAM;AACzB,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,aAAa;AAClE,WAAWD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,MAAM,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,yBAAyB;AAChF;AACA,CAAC;AAACC,GAAA,GAdIJ,GAAG;AAgBT,MAAMK,UAAU,GAAGhC,MAAM,CAACiB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAXID,UAAU;AAahB,MAAME,YAAY,GAAGlC,MAAM,CAACmC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,YAAY;AASlB,MAAMG,eAAe,GAAGrC,MAAM,CAACiB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAlBID,eAAe;AAoBrB,MAAME,cAAc,GAAGA,CAAC;EACtBC,UAAU;EACVC,SAAS;EACTC,OAAO;EACPC,WAAW;EACXC,aAAa;EACbC,UAAU;EACVC,WAAW;EACXC,cAAc;EACdC,aAAa;EACbC,WAAW;EACXC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,UAAU,CAAC;EAChE,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,WAAW,CAAC;EAC/D,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,SAAS,CAAC;EACzD,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAACyC,UAAU,IAAI,EAAE,CAAC;EACtE,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC2C,OAAO,IAAI,EAAE,CAAC;;EAE7D;EACA5C,KAAK,CAACkE,SAAS,CAAC,MAAM;IACpBH,iBAAiB,CAACrB,UAAU,IAAI,EAAE,CAAC;IACnCuB,cAAc,CAACrB,OAAO,IAAI,EAAE,CAAC;EAC/B,CAAC,EAAE,CAACF,UAAU,EAAEE,OAAO,CAAC,CAAC;EAEzB,MAAMuB,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAIA,OAAO,KAAKV,WAAW,EAAE;IAE7BG,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACFQ,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEF,OAAO,CAAC;;MAErE;MACAC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjEP,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAE;MACxBE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAK;;MAExB;MACA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,WAAW,EAAE,SAAS;QACtBC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEX;QAAQ,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMY,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBb,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEU,IAAI,CAAC;QAC7EX,OAAO,CAACC,GAAG,CAAC,sBAAsBU,IAAI,CAACG,cAAc,uBAAuBH,IAAI,CAACI,gBAAgB,EAAE,CAAC;;QAEpG;QACAzB,cAAc,CAACS,OAAO,CAAC;;QAEvB;QACAC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QACxD,MAAM,IAAIe,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACAjB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD,MAAMkB,aAAa,CAACpB,OAAO,CAAC;;QAE5B;QACA,IAAIhB,kBAAkB,EAAE;UACtBA,kBAAkB,CAAC,SAAS,CAAC;QAC/B;QAEAiB,OAAO,CAACC,GAAG,CAAC,8BAA8BF,OAAO,4BAA4B,CAAC;MAChF,CAAC,MAAM;QACLC,OAAO,CAACoB,KAAK,CAAC,wBAAwB,EAAET,IAAI,CAACU,OAAO,CAAC;MACvD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACR5B,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM2B,aAAa,GAAG,MAAOG,IAAI,IAAK;IACpC,IAAI;MACF;MACA,MAAMC,cAAc,GAAG,MAAMpB,KAAK,CAAC,oDAAoDqB,kBAAkB,CAACF,IAAI,CAAC,EAAE,EAAE;QACjHhB,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAIiB,cAAc,CAACE,EAAE,EAAE;QACrB,MAAMC,gBAAgB,GAAG,MAAMH,cAAc,CAACX,IAAI,CAAC,CAAC;QACpDlB,iBAAiB,CAACgC,gBAAgB,IAAI,EAAE,CAAC;QACzC1B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAAyB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEC,MAAM,KAAI,CAAC,2BAA2BL,IAAI,EAAE,CAAC;MACxF;;MAEA;MACA,MAAMM,eAAe,GAAG,MAAMzB,KAAK,CAAC,+CAA+CqB,kBAAkB,CAACF,IAAI,CAAC,EAAE,EAAE;QAC7GhB,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAIsB,eAAe,CAACH,EAAE,EAAE;QACtB,MAAMI,aAAa,GAAG,MAAMD,eAAe,CAAChB,IAAI,CAAC,CAAC;QAClDhB,cAAc,CAACiC,aAAa,IAAI,EAAE,CAAC;QACnC7B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAA4B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEF,MAAM,KAAI,CAAC,gBAAgBL,IAAI,EAAE,CAAC;MAC1E;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,oBACExE,OAAA,CAACC,kBAAkB;IAAAiF,QAAA,gBAEjBlF,OAAA,CAACsB,eAAe;MAAA4D,QAAA,gBACdlF,OAAA;QAAKmF,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjElF,OAAA,CAACd,kBAAkB;UACjBuD,WAAW,EAAEA,WAAY;UACzB8C,YAAY,EAAErC;QAAiB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,EACDhD,aAAa,iBACZ3C,OAAA;UAAMmF,KAAK,EAAE;YAAES,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAX,QAAA,EAAC;QAErD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN3F,OAAA;QAAKmF,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAJ,QAAA,gBAChElF,OAAA;UAAAkF,QAAA,GAAM,eAAG,EAACpD,UAAU,KAAK,MAAM,GAAG,WAAW,GAAG,WAAW;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnE3F,OAAA;UAAAkF,QAAA,EAAM;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACd3F,OAAA;UAAAkF,QAAA,EAAOnD,WAAW,GAAG,cAAc,GAAG;QAAiB;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/D3F,OAAA;UAAAkF,QAAA,EAAM;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACd3F,OAAA;UAAAkF,QAAA,EAAOrD,aAAa,GAAG,mBAAmB,GAAG;QAAmB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAGlB3F,OAAA,CAACI,aAAa;MAAA8E,QAAA,gBAEZlF,OAAA,CAACM,WAAW;QAAA4E,QAAA,eACVlF,OAAA;UAAAkF,QAAA,gBACElF,OAAA,CAACU,YAAY;YAAAwE,QAAA,gBACXlF,OAAA,CAACY,GAAG;cACFG,OAAO,EAAEsB,cAAc,KAAK,UAAW;cACvCyD,OAAO,EAAEA,CAAA,KAAMxD,iBAAiB,CAAC,UAAU,CAAE;cAAA4C,QAAA,EAC9C;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3F,OAAA,CAACY,GAAG;cACFG,OAAO,EAAEsB,cAAc,KAAK,QAAS;cACrCyD,OAAO,EAAEA,CAAA,KAAMxD,iBAAiB,CAAC,QAAQ,CAAE;cAAA4C,QAAA,EAC5C;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,EAEdtD,cAAc,KAAK,UAAU,gBAC5BrC,OAAA,CAACZ,aAAa;YACZqC,UAAU,EAAEoB,cAAe;YAC3BlB,OAAO,EAAEoB,WAAY;YACrBe,MAAM,EAAErB;UAAY,GACfA,WAAW;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAEF3F,OAAA,CAACX,WAAW;YACVoC,UAAU,EAAEoB,cAAe;YAC3BlB,OAAO,EAAEoB,WAAY;YACrBe,MAAM,EAAErB;UAAY,GACfA,WAAW;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGd3F,OAAA,CAACQ,WAAW;QAAA0E,QAAA,gBACVlF,OAAA,CAACU,YAAY;UAAAwE,QAAA,gBACXlF,OAAA,CAACY,GAAG;YACFG,OAAO,EAAEwB,aAAa,KAAK,WAAY;YACvCuD,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,WAAW,CAAE;YAAA0C,QAAA,EAC9C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3F,OAAA,CAACY,GAAG;YACFG,OAAO,EAAEwB,aAAa,KAAK,WAAY;YACvCuD,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,WAAW,CAAE;YAAA0C,QAAA,EAC9C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3F,OAAA,CAACY,GAAG;YACFG,OAAO,EAAEwB,aAAa,KAAK,WAAY;YACvCuD,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,WAAW,CAAE;YAAA0C,QAAA,EAC9C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3F,OAAA,CAACY,GAAG;YACFG,OAAO,EAAEwB,aAAa,KAAK,SAAU;YACrCuD,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,SAAS,CAAE;YAAA0C,QAAA,EAC5C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3F,OAAA,CAACY,GAAG;YACFG,OAAO,EAAEwB,aAAa,KAAK,QAAS;YACpCuD,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,QAAQ,CAAE;YAAA0C,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,EAEdpD,aAAa,KAAK,WAAW,iBAC5BvC,OAAA,CAACV,cAAc;UAACoC,SAAS,EAAEA;QAAU,GAAMe,WAAW;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC1D,EAEApD,aAAa,KAAK,WAAW,iBAC5BvC,OAAA,CAACT,aAAa;UAACoC,OAAO,EAAEA;QAAQ,GAAMc,WAAW;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACrD,EAEApD,aAAa,KAAK,WAAW,iBAC5BvC,OAAA,CAACb,aAAa;UAACsD,WAAW,EAAEA;QAAY,GAAMA,WAAW;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC7D,EAEApD,aAAa,KAAK,SAAS,iBAC1BvC,OAAA;UAAKmF,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEW,aAAa,EAAE,QAAQ;YAAET,GAAG,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBACpElF,OAAA,CAACL,YAAY;YACXkC,aAAa,EAAEA,aAAc;YAC7BmE,OAAO,EAAEhE,cAAe;YACxBiE,MAAM,EAAEhE;UAAc;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACF3F,OAAA,CAACJ,cAAc;YACbsG,QAAQ,EAAEhE,WAAY;YACtBC,kBAAkB,EAAEA,kBAAmB;YACvCgE,iBAAiB,EAAErE;UAAW;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEApD,aAAa,KAAK,QAAQ,iBACzBvC,OAAA;UAAKmF,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEW,aAAa,EAAE,QAAQ;YAAET,GAAG,EAAE;UAAO,CAAE;UAAAJ,QAAA,eACpElF,OAAA,CAACF,aAAa;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGhB3F,OAAA;MAAAkF,QAAA,gBACElF,OAAA,CAACmB,YAAY;QAAA+D,QAAA,EAAC;MAA0B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACvD3F,OAAA,CAACH,gBAAgB;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGN3F,OAAA;MAAAkF,QAAA,gBACElF,OAAA,CAACmB,YAAY;QAAA+D,QAAA,EAAC;MAA0B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACvD3F,OAAA,CAACiB,UAAU;QAAAiE,QAAA,gBACTlF,OAAA,CAACR,gBAAgB;UAACoC,WAAW,EAAEA;QAAY;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C3F,OAAA,CAACN,wBAAwB;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B3F,OAAA,CAACP,aAAa;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAEzB,CAAC;AAACvD,EAAA,CAlQIZ,cAAc;AAAA4E,GAAA,GAAd5E,cAAc;AAoQpB,eAAeA,cAAc;AAAC,IAAArB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA6E,GAAA;AAAAC,YAAA,CAAAlG,EAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}