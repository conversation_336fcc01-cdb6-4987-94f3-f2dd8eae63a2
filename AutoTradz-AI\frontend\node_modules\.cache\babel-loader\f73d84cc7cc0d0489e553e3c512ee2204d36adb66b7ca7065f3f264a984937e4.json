{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\ErrorBoundary.jsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorContainer = styled.div`\n  background: #2a2a2a;\n  border: 1px solid #ff3366;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 20px;\n  color: #fff;\n  text-align: center;\n`;\n_c = ErrorContainer;\nconst ErrorTitle = styled.h2`\n  color: #ff3366;\n  margin-bottom: 16px;\n`;\n_c2 = ErrorTitle;\nconst ErrorMessage = styled.p`\n  color: #ccc;\n  margin-bottom: 16px;\n  font-family: monospace;\n  font-size: 14px;\n`;\n_c3 = ErrorMessage;\nconst RetryButton = styled.button`\n  background: #4bffb5;\n  color: #1a1a1a;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n  \n  &:hover {\n    background: #3de89f;\n  }\n`;\n_c4 = RetryButton;\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.handleRetry = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null\n      });\n      window.location.reload();\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      var _this$state$errorInfo;\n      return /*#__PURE__*/_jsxDEV(ErrorContainer, {\n        children: [/*#__PURE__*/_jsxDEV(ErrorTitle, {\n          children: \"\\u26A0\\uFE0F Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          children: this.state.error && this.state.error.toString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          children: [\"Component stack trace:\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), ((_this$state$errorInfo = this.state.errorInfo) === null || _this$state$errorInfo === void 0 ? void 0 : _this$state$errorInfo.componentStack) || 'Stack trace not available']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RetryButton, {\n          onClick: this.handleRetry,\n          children: \"\\uD83D\\uDD04 Reload Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ErrorContainer\");\n$RefreshReg$(_c2, \"ErrorTitle\");\n$RefreshReg$(_c3, \"ErrorMessage\");\n$RefreshReg$(_c4, \"RetryButton\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h2", "_c2", "ErrorMessage", "p", "_c3", "RetryButton", "button", "_c4", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "window", "location", "reload", "state", "getDerivedStateFromError", "componentDidCatch", "console", "render", "_this$state$errorInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toString", "componentStack", "onClick", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/ErrorBoundary.jsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst ErrorContainer = styled.div`\n  background: #2a2a2a;\n  border: 1px solid #ff3366;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 20px;\n  color: #fff;\n  text-align: center;\n`;\n\nconst ErrorTitle = styled.h2`\n  color: #ff3366;\n  margin-bottom: 16px;\n`;\n\nconst ErrorMessage = styled.p`\n  color: #ccc;\n  margin-bottom: 16px;\n  font-family: monospace;\n  font-size: 14px;\n`;\n\nconst RetryButton = styled.button`\n  background: #4bffb5;\n  color: #1a1a1a;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n  \n  &:hover {\n    background: #3de89f;\n  }\n`;\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  handleRetry = () => {\n    this.setState({ hasError: false, error: null, errorInfo: null });\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <ErrorContainer>\n          <ErrorTitle>⚠️ Something went wrong</ErrorTitle>\n          <ErrorMessage>\n            {this.state.error && this.state.error.toString()}\n          </ErrorMessage>\n          <ErrorMessage>\n            Component stack trace:\n            <br />\n            {this.state.errorInfo?.componentStack || 'Stack trace not available'}\n          </ErrorMessage>\n          <RetryButton onClick={this.handleRetry}>\n            🔄 Reload Page\n          </RetryButton>\n        </ErrorContainer>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,cAAc;AAUpB,MAAMG,UAAU,GAAGN,MAAM,CAACO,EAAE;AAC5B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,UAAU;AAKhB,MAAMG,YAAY,GAAGT,MAAM,CAACU,CAAC;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,WAAW,GAAGZ,MAAM,CAACa,MAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIF,WAAW;AAcjB,MAAMG,aAAa,SAAShB,KAAK,CAACiB,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAAC,KAgBfC,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE;MAAK,CAAC,CAAC;MAChEC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAlBC,IAAI,CAACC,KAAK,GAAG;MAAEN,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOK,wBAAwBA,CAACN,KAAK,EAAE;IACrC,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAQ,iBAAiBA,CAACP,KAAK,EAAEC,SAAS,EAAE;IAClCO,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;IACjE,IAAI,CAACH,QAAQ,CAAC;MACZE,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EAOAQ,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACJ,KAAK,CAACN,QAAQ,EAAE;MAAA,IAAAW,qBAAA;MACvB,oBACE9B,OAAA,CAACC,cAAc;QAAA8B,QAAA,gBACb/B,OAAA,CAACI,UAAU;UAAA2B,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChDnC,OAAA,CAACO,YAAY;UAAAwB,QAAA,EACV,IAAI,CAACN,KAAK,CAACL,KAAK,IAAI,IAAI,CAACK,KAAK,CAACL,KAAK,CAACgB,QAAQ,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACfnC,OAAA,CAACO,YAAY;UAAAwB,QAAA,GAAC,wBAEZ,eAAA/B,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL,EAAAL,qBAAA,OAAI,CAACL,KAAK,CAACJ,SAAS,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBO,cAAc,KAAI,2BAA2B;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACfnC,OAAA,CAACU,WAAW;UAAC4B,OAAO,EAAE,IAAI,CAACrB,WAAY;UAAAc,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAErB;IAEA,OAAO,IAAI,CAACnB,KAAK,CAACe,QAAQ;EAC5B;AACF;AAEA,eAAelB,aAAa;AAAC,IAAAV,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA;AAAA2B,YAAA,CAAApC,EAAA;AAAAoC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA3B,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}