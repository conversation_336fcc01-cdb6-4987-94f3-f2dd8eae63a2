{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\AlpacaAccount.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AccountContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n_c = AccountContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n`;\n_c2 = Header;\nconst Title = styled.h3`\n  color: #4bffb5;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n`;\n_c3 = Title;\nconst RefreshButton = styled.button`\n  background: #4bffb5;\n  color: #000;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: #3de89f;\n  }\n  \n  &:disabled {\n    background: #666;\n    cursor: not-allowed;\n  }\n`;\n_c4 = RefreshButton;\nconst AccountGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 16px;\n  margin-bottom: 20px;\n`;\n_c5 = AccountGrid;\nconst AccountCard = styled.div`\n  background: #0a0a0a;\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid #333;\n`;\n_c6 = AccountCard;\nconst CardTitle = styled.h4`\n  color: #4bffb5;\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n_c7 = CardTitle;\nconst ValueDisplay = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  color: #fff;\n  font-family: 'Courier New', monospace;\n  margin-bottom: 8px;\n`;\n_c8 = ValueDisplay;\nconst SubValue = styled.div`\n  font-size: 12px;\n  color: #888;\n`;\n_c9 = SubValue;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  text-transform: uppercase;\n  background: ${props => {\n  switch (props.$status) {\n    case 'ACTIVE':\n      return 'rgba(75, 255, 181, 0.2)';\n    case 'INACTIVE':\n      return 'rgba(255, 167, 38, 0.2)';\n    case 'BLOCKED':\n      return 'rgba(255, 73, 118, 0.2)';\n    default:\n      return 'rgba(136, 136, 136, 0.2)';\n  }\n}};\n  color: ${props => {\n  switch (props.$status) {\n    case 'ACTIVE':\n      return '#4bffb5';\n    case 'INACTIVE':\n      return '#ffa726';\n    case 'BLOCKED':\n      return '#ff4976';\n    default:\n      return '#888';\n  }\n}};\n`;\n_c0 = StatusBadge;\nconst AccountDetails = styled.div`\n  background: #0a0a0a;\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid #333;\n`;\n_c1 = AccountDetails;\nconst DetailRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #333;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c10 = DetailRow;\nconst DetailLabel = styled.span`\n  color: #888;\n  font-size: 14px;\n`;\n_c11 = DetailLabel;\nconst DetailValue = styled.span`\n  color: #fff;\n  font-weight: 600;\n  font-family: 'Courier New', monospace;\n`;\n_c12 = DetailValue;\nconst ErrorMessage = styled.div`\n  background: rgba(255, 73, 118, 0.1);\n  border: 1px solid #ff4976;\n  border-radius: 8px;\n  padding: 16px;\n  color: #ff4976;\n  text-align: center;\n  margin-top: 16px;\n`;\n_c13 = ErrorMessage;\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: #888;\n  font-size: 14px;\n`;\n_c14 = LoadingSpinner;\nconst AlpacaAccount = () => {\n  _s();\n  const [accountData, setAccountData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const fetchAccountData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('http://localhost:8000/api/alpaca/account');\n      const result = await response.json();\n      if (result.success) {\n        setAccountData(result.account);\n        setLastUpdate(new Date(result.timestamp));\n        console.log('💰 Alpaca account data updated:', result.account.id);\n      } else {\n        setError(result.error || 'Failed to fetch account data');\n      }\n    } catch (err) {\n      setError('Network error: ' + err.message);\n      console.error('Error fetching Alpaca account data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAccountData();\n  }, []);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading && !accountData) {\n    return /*#__PURE__*/_jsxDEV(AccountContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: /*#__PURE__*/_jsxDEV(Title, {\n          children: \"\\uD83D\\uDCB0 Alpaca Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        children: \"Loading account information...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AccountContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: \"\\uD83D\\uDCB0 Alpaca Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), lastUpdate && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '12px',\n            color: '#888'\n          },\n          children: [\"Last updated: \", lastUpdate.toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RefreshButton, {\n        onClick: fetchAccountData,\n        disabled: loading,\n        children: [loading ? '⟳' : '🔄', \" Refresh\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      children: [\"\\u274C \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this), accountData && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(AccountGrid, {\n        children: [/*#__PURE__*/_jsxDEV(AccountCard, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"Portfolio Value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ValueDisplay, {\n            children: formatCurrency(accountData.portfolio_value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(SubValue, {\n            children: \"Total account value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccountCard, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"Buying Power\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ValueDisplay, {\n            children: formatCurrency(accountData.buying_power)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(SubValue, {\n            children: \"Available for trading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccountCard, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"Cash Balance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ValueDisplay, {\n            children: formatCurrency(accountData.cash)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(SubValue, {\n            children: \"Liquid cash\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccountCard, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"Equity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ValueDisplay, {\n            children: formatCurrency(accountData.equity)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(SubValue, {\n            children: \"Current equity value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AccountDetails, {\n        children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n          children: \"Account Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Account ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: accountData.id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Account Number:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: accountData.account_number\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatusBadge, {\n            $status: accountData.status,\n            children: accountData.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Currency:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: accountData.currency\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Day Trade Count:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: accountData.day_trade_count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Crypto Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatusBadge, {\n            $status: accountData.crypto_status,\n            children: accountData.crypto_status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Pattern Day Trader:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: accountData.pattern_day_trader ? 'Yes' : 'No'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Trading Blocked:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            style: {\n              color: accountData.trading_blocked ? '#ff4976' : '#4bffb5'\n            },\n            children: accountData.trading_blocked ? 'Yes' : 'No'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Account Created:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: formatDate(accountData.created_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s(AlpacaAccount, \"JWHIfSzii5JwWMdGc153EVVAAaA=\");\n_c15 = AlpacaAccount;\nexport default AlpacaAccount;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"AccountContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"RefreshButton\");\n$RefreshReg$(_c5, \"AccountGrid\");\n$RefreshReg$(_c6, \"AccountCard\");\n$RefreshReg$(_c7, \"CardTitle\");\n$RefreshReg$(_c8, \"ValueDisplay\");\n$RefreshReg$(_c9, \"SubValue\");\n$RefreshReg$(_c0, \"StatusBadge\");\n$RefreshReg$(_c1, \"AccountDetails\");\n$RefreshReg$(_c10, \"DetailRow\");\n$RefreshReg$(_c11, \"DetailLabel\");\n$RefreshReg$(_c12, \"DetailValue\");\n$RefreshReg$(_c13, \"ErrorMessage\");\n$RefreshReg$(_c14, \"LoadingSpinner\");\n$RefreshReg$(_c15, \"AlpacaAccount\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "A<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Header", "_c2", "Title", "h3", "_c3", "RefreshButton", "button", "_c4", "Account<PERSON><PERSON>", "_c5", "AccountCard", "_c6", "CardTitle", "h4", "_c7", "ValueDisplay", "_c8", "SubValue", "_c9", "StatusBadge", "span", "props", "$status", "_c0", "AccountDetails", "_c1", "DetailRow", "_c10", "DetailLabel", "_c11", "DetailValue", "_c12", "ErrorMessage", "_c13", "LoadingSpinner", "_c14", "AlpacaAccount", "_s", "accountData", "setAccountData", "loading", "setLoading", "error", "setError", "lastUpdate", "setLastUpdate", "fetchAccountData", "response", "fetch", "result", "json", "success", "account", "Date", "timestamp", "console", "log", "id", "err", "message", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "toLocaleDateString", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "gap", "fontSize", "color", "toLocaleTimeString", "onClick", "disabled", "portfolio_value", "buying_power", "cash", "equity", "account_number", "status", "day_trade_count", "crypto_status", "pattern_day_trader", "trading_blocked", "created_at", "_c15", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/AlpacaAccount.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\nconst AccountContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n`;\n\nconst Title = styled.h3`\n  color: #4bffb5;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n`;\n\nconst RefreshButton = styled.button`\n  background: #4bffb5;\n  color: #000;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: #3de89f;\n  }\n  \n  &:disabled {\n    background: #666;\n    cursor: not-allowed;\n  }\n`;\n\nconst AccountGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 16px;\n  margin-bottom: 20px;\n`;\n\nconst AccountCard = styled.div`\n  background: #0a0a0a;\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid #333;\n`;\n\nconst CardTitle = styled.h4`\n  color: #4bffb5;\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst ValueDisplay = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  color: #fff;\n  font-family: 'Courier New', monospace;\n  margin-bottom: 8px;\n`;\n\nconst SubValue = styled.div`\n  font-size: 12px;\n  color: #888;\n`;\n\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  text-transform: uppercase;\n  background: ${props => {\n    switch (props.$status) {\n      case 'ACTIVE': return 'rgba(75, 255, 181, 0.2)';\n      case 'INACTIVE': return 'rgba(255, 167, 38, 0.2)';\n      case 'BLOCKED': return 'rgba(255, 73, 118, 0.2)';\n      default: return 'rgba(136, 136, 136, 0.2)';\n    }\n  }};\n  color: ${props => {\n    switch (props.$status) {\n      case 'ACTIVE': return '#4bffb5';\n      case 'INACTIVE': return '#ffa726';\n      case 'BLOCKED': return '#ff4976';\n      default: return '#888';\n    }\n  }};\n`;\n\nconst AccountDetails = styled.div`\n  background: #0a0a0a;\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid #333;\n`;\n\nconst DetailRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #333;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst DetailLabel = styled.span`\n  color: #888;\n  font-size: 14px;\n`;\n\nconst DetailValue = styled.span`\n  color: #fff;\n  font-weight: 600;\n  font-family: 'Courier New', monospace;\n`;\n\nconst ErrorMessage = styled.div`\n  background: rgba(255, 73, 118, 0.1);\n  border: 1px solid #ff4976;\n  border-radius: 8px;\n  padding: 16px;\n  color: #ff4976;\n  text-align: center;\n  margin-top: 16px;\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: #888;\n  font-size: 14px;\n`;\n\nconst AlpacaAccount = () => {\n  const [accountData, setAccountData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  const fetchAccountData = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('http://localhost:8000/api/alpaca/account');\n      const result = await response.json();\n      \n      if (result.success) {\n        setAccountData(result.account);\n        setLastUpdate(new Date(result.timestamp));\n        console.log('💰 Alpaca account data updated:', result.account.id);\n      } else {\n        setError(result.error || 'Failed to fetch account data');\n      }\n    } catch (err) {\n      setError('Network error: ' + err.message);\n      console.error('Error fetching Alpaca account data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAccountData();\n  }, []);\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading && !accountData) {\n    return (\n      <AccountContainer>\n        <Header>\n          <Title>💰 Alpaca Account</Title>\n        </Header>\n        <LoadingSpinner>\n          Loading account information...\n        </LoadingSpinner>\n      </AccountContainer>\n    );\n  }\n\n  return (\n    <AccountContainer>\n      <Header>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <Title>💰 Alpaca Account</Title>\n          {lastUpdate && (\n            <span style={{ fontSize: '12px', color: '#888' }}>\n              Last updated: {lastUpdate.toLocaleTimeString()}\n            </span>\n          )}\n        </div>\n        <RefreshButton onClick={fetchAccountData} disabled={loading}>\n          {loading ? '⟳' : '🔄'} Refresh\n        </RefreshButton>\n      </Header>\n\n      {error && (\n        <ErrorMessage>\n          ❌ {error}\n        </ErrorMessage>\n      )}\n\n      {accountData && (\n        <>\n          <AccountGrid>\n            <AccountCard>\n              <CardTitle>Portfolio Value</CardTitle>\n              <ValueDisplay>{formatCurrency(accountData.portfolio_value)}</ValueDisplay>\n              <SubValue>Total account value</SubValue>\n            </AccountCard>\n\n            <AccountCard>\n              <CardTitle>Buying Power</CardTitle>\n              <ValueDisplay>{formatCurrency(accountData.buying_power)}</ValueDisplay>\n              <SubValue>Available for trading</SubValue>\n            </AccountCard>\n\n            <AccountCard>\n              <CardTitle>Cash Balance</CardTitle>\n              <ValueDisplay>{formatCurrency(accountData.cash)}</ValueDisplay>\n              <SubValue>Liquid cash</SubValue>\n            </AccountCard>\n\n            <AccountCard>\n              <CardTitle>Equity</CardTitle>\n              <ValueDisplay>{formatCurrency(accountData.equity)}</ValueDisplay>\n              <SubValue>Current equity value</SubValue>\n            </AccountCard>\n          </AccountGrid>\n\n          <AccountDetails>\n            <CardTitle>Account Details</CardTitle>\n            \n            <DetailRow>\n              <DetailLabel>Account ID:</DetailLabel>\n              <DetailValue>{accountData.id}</DetailValue>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Account Number:</DetailLabel>\n              <DetailValue>{accountData.account_number}</DetailValue>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Status:</DetailLabel>\n              <StatusBadge $status={accountData.status}>{accountData.status}</StatusBadge>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Currency:</DetailLabel>\n              <DetailValue>{accountData.currency}</DetailValue>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Day Trade Count:</DetailLabel>\n              <DetailValue>{accountData.day_trade_count}</DetailValue>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Crypto Status:</DetailLabel>\n              <StatusBadge $status={accountData.crypto_status}>{accountData.crypto_status}</StatusBadge>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Pattern Day Trader:</DetailLabel>\n              <DetailValue>{accountData.pattern_day_trader ? 'Yes' : 'No'}</DetailValue>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Trading Blocked:</DetailLabel>\n              <DetailValue style={{ color: accountData.trading_blocked ? '#ff4976' : '#4bffb5' }}>\n                {accountData.trading_blocked ? 'Yes' : 'No'}\n              </DetailValue>\n            </DetailRow>\n            \n            <DetailRow>\n              <DetailLabel>Account Created:</DetailLabel>\n              <DetailValue>{formatDate(accountData.created_at)}</DetailValue>\n            </DetailRow>\n          </AccountDetails>\n        </>\n      )}\n    </AccountContainer>\n  );\n};\n\nexport default AlpacaAccount;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,gBAAgB,GAAGL,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,gBAAgB;AAQtB,MAAMG,MAAM,GAAGR,MAAM,CAACM,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,MAAM;AAOZ,MAAME,KAAK,GAAGV,MAAM,CAACW,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,aAAa,GAAGb,MAAM,CAACc,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,aAAa;AAqBnB,MAAMG,WAAW,GAAGhB,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GALID,WAAW;AAOjB,MAAME,WAAW,GAAGlB,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,WAAW;AAOjB,MAAME,SAAS,GAAGpB,MAAM,CAACqB,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,SAAS;AASf,MAAMG,YAAY,GAAGvB,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GANID,YAAY;AAQlB,MAAME,QAAQ,GAAGzB,MAAM,CAACM,GAAG;AAC3B;AACA;AACA,CAAC;AAACoB,GAAA,GAHID,QAAQ;AAKd,MAAME,WAAW,GAAG3B,MAAM,CAAC4B,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAI;EACrB,QAAQA,KAAK,CAACC,OAAO;IACnB,KAAK,QAAQ;MAAE,OAAO,yBAAyB;IAC/C,KAAK,UAAU;MAAE,OAAO,yBAAyB;IACjD,KAAK,SAAS;MAAE,OAAO,yBAAyB;IAChD;MAAS,OAAO,0BAA0B;EAC5C;AACF,CAAC;AACH,WAAWD,KAAK,IAAI;EAChB,QAAQA,KAAK,CAACC,OAAO;IACnB,KAAK,QAAQ;MAAE,OAAO,SAAS;IAC/B,KAAK,UAAU;MAAE,OAAO,SAAS;IACjC,KAAK,SAAS;MAAE,OAAO,SAAS;IAChC;MAAS,OAAO,MAAM;EACxB;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GAvBIJ,WAAW;AAyBjB,MAAMK,cAAc,GAAGhC,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GALID,cAAc;AAOpB,MAAME,SAAS,GAAGlC,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GAVID,SAAS;AAYf,MAAME,WAAW,GAAGpC,MAAM,CAAC4B,IAAI;AAC/B;AACA;AACA,CAAC;AAACS,IAAA,GAHID,WAAW;AAKjB,MAAME,WAAW,GAAGtC,MAAM,CAAC4B,IAAI;AAC/B;AACA;AACA;AACA,CAAC;AAACW,IAAA,GAJID,WAAW;AAMjB,MAAME,YAAY,GAAGxC,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GARID,YAAY;AAUlB,MAAME,cAAc,GAAG1C,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GAPID,cAAc;AASpB,MAAME,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAElD,MAAMwD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,CAAC;MACxE,MAAMC,MAAM,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAEpC,IAAID,MAAM,CAACE,OAAO,EAAE;QAClBZ,cAAc,CAACU,MAAM,CAACG,OAAO,CAAC;QAC9BP,aAAa,CAAC,IAAIQ,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAAC;QACzCC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEP,MAAM,CAACG,OAAO,CAACK,EAAE,CAAC;MACnE,CAAC,MAAM;QACLd,QAAQ,CAACM,MAAM,CAACP,KAAK,IAAI,8BAA8B,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZf,QAAQ,CAAC,iBAAiB,GAAGe,GAAG,CAACC,OAAO,CAAC;MACzCJ,OAAO,CAACb,KAAK,CAAC,qCAAqC,EAAEgB,GAAG,CAAC;IAC3D,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlD,SAAS,CAAC,MAAM;IACduD,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMc,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;EACnB,CAAC;EAED,MAAMQ,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIjB,IAAI,CAACiB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAI/B,OAAO,IAAI,CAACF,WAAW,EAAE;IAC3B,oBACE5C,OAAA,CAACG,gBAAgB;MAAA2E,QAAA,gBACf9E,OAAA,CAACM,MAAM;QAAAwE,QAAA,eACL9E,OAAA,CAACQ,KAAK;UAAAsE,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACTlF,OAAA,CAACwC,cAAc;QAAAsC,QAAA,EAAC;MAEhB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEvB;EAEA,oBACElF,OAAA,CAACG,gBAAgB;IAAA2E,QAAA,gBACf9E,OAAA,CAACM,MAAM;MAAAwE,QAAA,gBACL9E,OAAA;QAAKsE,KAAK,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAP,QAAA,gBACjE9E,OAAA,CAACQ,KAAK;UAAAsE,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC/BhC,UAAU,iBACTlD,OAAA;UAAMsE,KAAK,EAAE;YAAEgB,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,GAAC,gBAClC,EAAC5B,UAAU,CAACsC,kBAAkB,CAAC,CAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNlF,OAAA,CAACW,aAAa;QAAC8E,OAAO,EAAErC,gBAAiB;QAACsC,QAAQ,EAAE5C,OAAQ;QAAAgC,QAAA,GACzDhC,OAAO,GAAG,GAAG,GAAG,IAAI,EAAC,UACxB;MAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAERlC,KAAK,iBACJhD,OAAA,CAACsC,YAAY;MAAAwC,QAAA,GAAC,SACV,EAAC9B,KAAK;IAAA;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACf,EAEAtC,WAAW,iBACV5C,OAAA,CAAAE,SAAA;MAAA4E,QAAA,gBACE9E,OAAA,CAACc,WAAW;QAAAgE,QAAA,gBACV9E,OAAA,CAACgB,WAAW;UAAA8D,QAAA,gBACV9E,OAAA,CAACkB,SAAS;YAAA4D,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACtClF,OAAA,CAACqB,YAAY;YAAAyD,QAAA,EAAEZ,cAAc,CAACtB,WAAW,CAAC+C,eAAe;UAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC1ElF,OAAA,CAACuB,QAAQ;YAAAuD,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEdlF,OAAA,CAACgB,WAAW;UAAA8D,QAAA,gBACV9E,OAAA,CAACkB,SAAS;YAAA4D,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACnClF,OAAA,CAACqB,YAAY;YAAAyD,QAAA,EAAEZ,cAAc,CAACtB,WAAW,CAACgD,YAAY;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eACvElF,OAAA,CAACuB,QAAQ;YAAAuD,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAEdlF,OAAA,CAACgB,WAAW;UAAA8D,QAAA,gBACV9E,OAAA,CAACkB,SAAS;YAAA4D,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACnClF,OAAA,CAACqB,YAAY;YAAAyD,QAAA,EAAEZ,cAAc,CAACtB,WAAW,CAACiD,IAAI;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC/DlF,OAAA,CAACuB,QAAQ;YAAAuD,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEdlF,OAAA,CAACgB,WAAW;UAAA8D,QAAA,gBACV9E,OAAA,CAACkB,SAAS;YAAA4D,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC7BlF,OAAA,CAACqB,YAAY;YAAAyD,QAAA,EAAEZ,cAAc,CAACtB,WAAW,CAACkD,MAAM;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eACjElF,OAAA,CAACuB,QAAQ;YAAAuD,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEdlF,OAAA,CAAC8B,cAAc;QAAAgD,QAAA,gBACb9E,OAAA,CAACkB,SAAS;UAAA4D,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEtClF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtClF,OAAA,CAACoC,WAAW;YAAA0C,QAAA,EAAElC,WAAW,CAACmB;UAAE;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1ClF,OAAA,CAACoC,WAAW;YAAA0C,QAAA,EAAElC,WAAW,CAACmD;UAAc;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClClF,OAAA,CAACyB,WAAW;YAACG,OAAO,EAAEgB,WAAW,CAACoD,MAAO;YAAAlB,QAAA,EAAElC,WAAW,CAACoD;UAAM;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpClF,OAAA,CAACoC,WAAW;YAAA0C,QAAA,EAAElC,WAAW,CAAC2B;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3ClF,OAAA,CAACoC,WAAW;YAAA0C,QAAA,EAAElC,WAAW,CAACqD;UAAe;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzClF,OAAA,CAACyB,WAAW;YAACG,OAAO,EAAEgB,WAAW,CAACsD,aAAc;YAAApB,QAAA,EAAElC,WAAW,CAACsD;UAAa;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9ClF,OAAA,CAACoC,WAAW;YAAA0C,QAAA,EAAElC,WAAW,CAACuD,kBAAkB,GAAG,KAAK,GAAG;UAAI;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3ClF,OAAA,CAACoC,WAAW;YAACkC,KAAK,EAAE;cAAEiB,KAAK,EAAE3C,WAAW,CAACwD,eAAe,GAAG,SAAS,GAAG;YAAU,CAAE;YAAAtB,QAAA,EAChFlC,WAAW,CAACwD,eAAe,GAAG,KAAK,GAAG;UAAI;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEZlF,OAAA,CAACgC,SAAS;UAAA8C,QAAA,gBACR9E,OAAA,CAACkC,WAAW;YAAA4C,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3ClF,OAAA,CAACoC,WAAW;YAAA0C,QAAA,EAAEH,UAAU,CAAC/B,WAAW,CAACyD,UAAU;UAAC;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,eACjB,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAEvB,CAAC;AAACvC,EAAA,CApKID,aAAa;AAAA4D,IAAA,GAAb5D,aAAa;AAsKnB,eAAeA,aAAa;AAAC,IAAArC,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA6D,IAAA;AAAAC,YAAA,CAAAlG,EAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}