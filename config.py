"""Configuration management for AutoTradz AI."""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings."""
    
    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True)
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    secret_key: str = Field(env="SECRET_KEY")
    
    # Alpaca API Configuration
    alpaca_api_key: str = Field(default="PKTB6CYEZ1O3HU9FS9CJ", env="ALPACA_API_KEY")  # PKTEST... (paper) or PK... (live)
    alpaca_api_secret: str = Field(default="VZWqUAscwKknTayYuWy3cK5oulmg7Rl0qsbnZrfY", env="ALPACA_API_SECRET")  # Secret key
    alpaca_paper_trading: bool = Field(default=True, env="ALPACA_PAPER_TRADING")

    # Legacy Coinbase Configuration (for backward compatibility)
    coinbase_api_key: str = Field(default="", env="COINBASE_API_KEY")
    coinbase_api_secret: str = Field(default="", env="COINBASE_API_SECRET")
    coinbase_sandbox: bool = Field(default=True, env="COINBASE_SANDBOX")
    
    # Database Configuration
    database_url: str = Field(env="DATABASE_URL")
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Trading Configuration
    trading_pair: str = Field(default="XRP/USD", env="TRADING_PAIR")  # Alpaca format: XRP/USD
    initial_capital: float = Field(default=10000.0, env="INITIAL_CAPITAL")
    max_position_size: float = Field(default=0.1, env="MAX_POSITION_SIZE")
    risk_per_trade: float = Field(default=0.02, env="RISK_PER_TRADE")
    
    # WebSocket Configuration
    ws_reconnect_delay: int = Field(default=5, env="WS_RECONNECT_DELAY")
    ws_max_reconnect_attempts: int = Field(default=10, env="WS_MAX_RECONNECT_ATTEMPTS")
    
    # Monitoring
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
