import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const TrackerContainer = styled.div`
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`;

const Title = styled.h3`
  color: #4bffb5;
  margin: 0 0 20px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &::before {
    content: '📊';
    font-size: 20px;
  }
`;

const PositionGrid = styled.div`
  display: grid;
  gap: 12px;
`;

const PositionCard = styled.div`
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid ${props => {
    switch(props.side) {
      case 'long': return '#4bffb5';
      case 'short': return '#ff4976';
      default: return '#333';
    }
  }};
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${props => {
      switch(props.side) {
        case 'long': return '#4bffb5';
        case 'short': return '#ff4976';
        default: return '#ffa726';
      }
    }};
  }
`;

const PositionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const Symbol = styled.div`
  font-weight: bold;
  color: #fff;
  font-size: 16px;
`;

const Side = styled.div`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  background: ${props => {
    switch(props.side) {
      case 'long': return 'rgba(75, 255, 181, 0.2)';
      case 'short': return 'rgba(255, 73, 118, 0.2)';
      default: return 'rgba(255, 167, 38, 0.2)';
    }
  }};
  color: ${props => {
    switch(props.side) {
      case 'long': return '#4bffb5';
      case 'short': return '#ff4976';
      default: return '#ffa726';
    }
  }};
`;

const PositionDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
`;

const DetailItem = styled.div`
  .label {
    font-size: 11px;
    color: #888;
    margin-bottom: 2px;
  }
  
  .value {
    font-size: 14px;
    color: #fff;
    font-weight: bold;
  }
`;

const PnLIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  background: ${props => props.pnl >= 0 ? 'rgba(75, 255, 181, 0.1)' : 'rgba(255, 73, 118, 0.1)'};
  border: 1px solid ${props => props.pnl >= 0 ? 'rgba(75, 255, 181, 0.3)' : 'rgba(255, 73, 118, 0.3)'};
  
  .pnl-value {
    font-weight: bold;
    color: ${props => props.pnl >= 0 ? '#4bffb5' : '#ff4976'};
  }
  
  .pnl-percentage {
    font-size: 12px;
    color: ${props => props.pnl >= 0 ? '#4bffb5' : '#ff4976'};
  }
`;

const AIAttribution = styled.div`
  margin-top: 12px;
  padding: 8px;
  background: rgba(75, 255, 181, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(75, 255, 181, 0.2);
  
  .ai-label {
    font-size: 10px;
    color: #4bffb5;
    font-weight: bold;
    margin-bottom: 4px;
  }
  
  .ai-strategy {
    font-size: 11px;
    color: #ccc;
    margin-bottom: 2px;
  }
  
  .ai-confidence {
    font-size: 10px;
    color: #888;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #888;
  
  .icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .message {
    font-size: 14px;
    line-height: 1.4;
  }
`;

const AIPositionTracker = () => {
  const [positions, setPositions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPositions = async () => {
      try {
        const response = await fetch('/portfolio');
        const data = await response.json();
        
        if (data && data.positions) {
          setPositions(data.positions);
        } else {
          // Mock positions for demo
          setPositions([
            {
              symbol: 'BTC/USD',
              side: 'long',
              size: 0.001,
              entry_price: 67234.50,
              current_price: 67456.20,
              pnl: 0.22,
              pnl_percentage: 0.33,
              ai_strategy: 'Technical Analysis',
              ai_confidence: 0.78,
              ai_reasoning: 'RSI oversold with bullish divergence',
              timestamp: new Date().toISOString()
            },
            {
              symbol: 'ETH/USD',
              side: 'long',
              size: 0.05,
              entry_price: 3456.78,
              current_price: 3423.45,
              pnl: -1.67,
              pnl_percentage: -0.96,
              ai_strategy: 'Machine Learning',
              ai_confidence: 0.65,
              ai_reasoning: 'ML model predicted upward movement',
              timestamp: new Date(Date.now() - 3600000).toISOString()
            }
          ]);
        }
      } catch (error) {
        console.error('Failed to fetch positions:', error);
        // Mock positions for demo
        setPositions([
          {
            symbol: 'BTC/USD',
            side: 'long',
            size: 0.001,
            entry_price: 67234.50,
            current_price: 67456.20,
            pnl: 0.22,
            pnl_percentage: 0.33,
            ai_strategy: 'Technical Analysis',
            ai_confidence: 0.78,
            ai_reasoning: 'RSI oversold with bullish divergence',
            timestamp: new Date().toISOString()
          }
        ]);
      }
      setLoading(false);
    };

    fetchPositions();
    const interval = setInterval(fetchPositions, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(value);
  };

  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <TrackerContainer>
        <Title>AI Position Tracker</Title>
        <div style={{ textAlign: 'center', padding: '40px', color: '#888' }}>
          Loading positions...
        </div>
      </TrackerContainer>
    );
  }

  return (
    <TrackerContainer>
      <Title>AI Position Tracker</Title>
      
      {positions.length === 0 ? (
        <EmptyState>
          <div className="icon">📊</div>
          <div className="message">
            No active positions.<br />
            Start trading to see AI-driven positions here.
          </div>
        </EmptyState>
      ) : (
        <PositionGrid>
          {positions.map((position, index) => (
            <PositionCard key={index} side={position.side}>
              <PositionHeader>
                <Symbol>{position.symbol}</Symbol>
                <Side side={position.side}>{position.side.toUpperCase()}</Side>
              </PositionHeader>
              
              <PositionDetails>
                <DetailItem>
                  <div className="label">Size</div>
                  <div className="value">{position.size}</div>
                </DetailItem>
                <DetailItem>
                  <div className="label">Entry Price</div>
                  <div className="value">{formatCurrency(position.entry_price)}</div>
                </DetailItem>
                <DetailItem>
                  <div className="label">Current Price</div>
                  <div className="value">{formatCurrency(position.current_price)}</div>
                </DetailItem>
                <DetailItem>
                  <div className="label">Opened</div>
                  <div className="value">{new Date(position.timestamp).toLocaleTimeString()}</div>
                </DetailItem>
              </PositionDetails>
              
              <PnLIndicator pnl={position.pnl}>
                <div className="pnl-value">
                  {position.pnl >= 0 ? '+' : ''}{formatCurrency(position.pnl)}
                </div>
                <div className="pnl-percentage">
                  ({formatPercentage(position.pnl_percentage)})
                </div>
              </PnLIndicator>
              
              <AIAttribution>
                <div className="ai-label">🧠 AI Attribution</div>
                <div className="ai-strategy">Strategy: {position.ai_strategy}</div>
                <div className="ai-confidence">
                  Confidence: {(position.ai_confidence * 100).toFixed(0)}% | {position.ai_reasoning}
                </div>
              </AIAttribution>
            </PositionCard>
          ))}
        </PositionGrid>
      )}
    </TrackerContainer>
  );
};

export default AIPositionTracker;
