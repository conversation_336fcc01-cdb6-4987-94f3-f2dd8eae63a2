import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const DashboardContainer = styled.div`
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  padding: 24px;
  margin: 16px 0;
  border: 1px solid #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`;

const Title = styled.h2`
  color: #4bffb5;
  font-size: 24px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  &::before {
    content: '🧠';
    font-size: 28px;
  }
`;

const ThoughtStream = styled.div`
  max-height: 600px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #4bffb5;
    border-radius: 3px;
  }
`;

const ThoughtBubble = styled.div`
  background: ${props => {
    switch(props.thoughtType) {
      case 'market_analysis': return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
      case 'signal_generation': return 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
      case 'risk_analysis': return 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)';
      case 'final_decision': return 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
      case 'trade_execution': return 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';
      case 'consensus_analysis': return 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)';
      case 'adaptation': return 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)';
      default: return 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)';
    }
  }};
  color: ${props => ['risk_analysis', 'adaptation'].includes(props.thoughtType) ? '#333' : '#fff'};
  padding: 12px 16px;
  border-radius: 12px;
  margin-bottom: 12px;
  position: relative;
  animation: slideIn 0.3s ease-out;
  
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const ThoughtHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  opacity: 0.9;
`;

const ComponentTag = styled.span`
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: bold;
`;

const ConfidenceBar = styled.div`
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  
  &::after {
    content: '';
    display: block;
    width: ${props => props.confidence * 100}%;
    height: 100%;
    background: ${props => props.confidence > 0.7 ? '#4bffb5' : props.confidence > 0.4 ? '#ffa726' : '#ff4976'};
    transition: width 0.3s ease;
  }
`;

const ThoughtContent = styled.div`
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 8px;
`;

const ThoughtData = styled.div`
  font-size: 11px;
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.2);
  padding: 6px 8px;
  border-radius: 6px;
  margin-top: 8px;
`;

const CurrentAnalysis = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 20px;
`;

const AnalysisCard = styled.div`
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #333;
`;

const CardTitle = styled.h3`
  color: #4bffb5;
  font-size: 16px;
  margin-bottom: 12px;
`;

const MetricRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  
  .label {
    color: #888;
  }
  
  .value {
    color: #fff;
    font-weight: bold;
  }
`;

const RefreshButton = styled.button`
  background: linear-gradient(135deg, #4bffb5 0%, #4facfe 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  margin-left: auto;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(75, 255, 181, 0.3);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AIThinkingDashboard = () => {
  const [thoughts, setThoughts] = useState([]);
  const [currentAnalysis, setCurrentAnalysis] = useState(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  const fetchAIThoughts = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/decision-logs');
      const data = await response.json();

      if (data.decision_logs && data.decision_logs.length > 0) {
        // Flatten all thoughts from recent decision logs
        const allThoughts = [];
        data.decision_logs.forEach(log => {
          log.thoughts.forEach(thought => {
            allThoughts.push({
              ...thought,
              logTimestamp: log.timestamp
            });
          });
        });

        // Sort by timestamp (newest first)
        allThoughts.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        setThoughts(allThoughts.slice(0, 20)); // Keep last 20 thoughts
      }
    } catch (error) {
      console.error('Failed to fetch AI thoughts:', error);
      // For demo purposes, show mock data when API is not available
      setThoughts(generateMockThoughts());
    }
    setLoading(false);
  };

  const generateMockThoughts = () => {
    const mockThoughts = [
      {
        timestamp: new Date().toISOString(),
        component: "RegimeDetector",
        thought_type: "market_analysis",
        content: "Market regime detected as TRENDING_UP with 0.85 confidence. This suggests trending market conditions with strong bullish momentum.",
        confidence: 0.85,
        data: { regime: "TRENDING_UP", regime_confidence: 0.85, market_type: "trending" }
      },
      {
        timestamp: new Date(Date.now() - 5000).toISOString(),
        component: "Strategy_Technical",
        thought_type: "signal_generation",
        content: "Strategy Technical: BUY signal with 0.78 confidence. RSI oversold condition with bullish divergence detected.",
        confidence: 0.78,
        data: { signal: "BUY", confidence: 0.78, price: 67234.50, risk_score: 0.3 }
      },
      {
        timestamp: new Date(Date.now() - 10000).toISOString(),
        component: "Strategy_ML",
        thought_type: "signal_generation",
        content: "Strategy ML: BUY signal with 0.72 confidence. Machine learning model predicts upward price movement based on recent patterns.",
        confidence: 0.72,
        data: { signal: "BUY", confidence: 0.72, price: 67234.50, risk_score: 0.25 }
      },
      {
        timestamp: new Date(Date.now() - 15000).toISOString(),
        component: "StrategyEnsemble",
        thought_type: "consensus_analysis",
        content: "Strategy consensus: 3 BUY, 0 SELL, 1 HOLD signals. Bullish consensus emerging with strong agreement across strategies.",
        confidence: 0.75,
        data: { buy_count: 3, sell_count: 0, hold_count: 1, total_strategies: 4, avg_confidence: 0.75 }
      },
      {
        timestamp: new Date(Date.now() - 20000).toISOString(),
        component: "RiskManager",
        thought_type: "risk_analysis",
        content: "Market volatility is 0.0234 (annualized). Recent performance shows 7/10 profitable trades. Risk parameters updated accordingly.",
        confidence: 0.9,
        data: { market_volatility: 0.0234, recent_win_rate: 0.7, portfolio_value: 100000, available_cash: 25000 }
      },
      {
        timestamp: new Date(Date.now() - 25000).toISOString(),
        component: "DecisionEngine",
        thought_type: "final_decision",
        content: "Final decision: BUY with 0.76 confidence. Combined from 4 strategies, filtered for market regime TRENDING_UP.",
        confidence: 0.76,
        data: { final_signal: "BUY", final_confidence: 0.76, final_price: 67234.50, risk_score: 0.28, position_size: 0.001 }
      },
      {
        timestamp: new Date(Date.now() - 30000).toISOString(),
        component: "RiskManager",
        thought_type: "position_sizing",
        content: "Position size calculated: 0.001 BTC. This represents 0.67% of portfolio value at current price levels.",
        confidence: 0.9,
        data: { position_size: 0.001, position_value: 67.23, portfolio_percentage: 0.67, price: 67234.50 }
      },
      {
        timestamp: new Date(Date.now() - 35000).toISOString(),
        component: "TradeExecutor",
        thought_type: "trade_execution",
        content: "Trade executed: BUY 0.001 BTC at $67234.50. Total value: $67.23",
        confidence: 0.76,
        data: { action: "BUY", size: 0.001, price: 67234.50, total_value: 67.23, strategy: "Technical" }
      }
    ];
    return mockThoughts;
  };

  const fetchCurrentAnalysis = async () => {
    try {
      const response = await fetch('/api/ai/current-analysis');
      const data = await response.json();
      setCurrentAnalysis(data);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch current analysis:', error);
      // For demo purposes, show mock data when API is not available
      setCurrentAnalysis({
        timestamp: new Date().toISOString(),
        market_data: {
          current_price: 67234.50,
          pair: "BTC/USD",
          data_points: 500,
          volume: 1234567
        },
        final_decision: {
          signal: "BUY",
          confidence: 0.76,
          reasoning: "Strong bullish consensus across multiple strategies with favorable market regime detection."
        },
        execution_plan: {
          signal: "BUY",
          confidence: 0.76,
          position_size: 0.001,
          portfolio_impact: 0.67,
          strategy_weights: {
            "Technical": 0.35,
            "ML": 0.25,
            "Neural": 0.20,
            "Momentum": 0.20
          },
          market_regime: "TRENDING_UP",
          risk_score: 0.28
        }
      });
      setLastUpdate(new Date());
    }
  };

  useEffect(() => {
    fetchAIThoughts();
    fetchCurrentAnalysis();
    
    // Auto-refresh every 5 seconds
    const interval = setInterval(() => {
      fetchAIThoughts();
      fetchCurrentAnalysis();
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const formatThoughtType = (type) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <DashboardContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title>AI Thinking Process</Title>
        <RefreshButton onClick={() => { fetchAIThoughts(); fetchCurrentAnalysis(); }} disabled={loading}>
          {loading ? '🔄' : '↻'} Refresh
        </RefreshButton>
      </div>
      
      {lastUpdate && (
        <div style={{ color: '#888', fontSize: '12px', marginBottom: '16px' }}>
          Last updated: {lastUpdate.toLocaleTimeString()}
        </div>
      )}

      <ThoughtStream>
        {thoughts.length === 0 ? (
          <div style={{ textAlign: 'center', color: '#888', padding: '40px' }}>
            {loading ? 'Loading AI thoughts...' : 'No AI thoughts available yet. Start trading to see AI decision-making process.'}
          </div>
        ) : (
          thoughts.map((thought, index) => (
            <ThoughtBubble key={index} thoughtType={thought.thought_type}>
              <ThoughtHeader>
                <ComponentTag>{thought.component}</ComponentTag>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span>{formatThoughtType(thought.thought_type)}</span>
                  <ConfidenceBar confidence={thought.confidence} />
                  <span>{formatTimestamp(thought.timestamp)}</span>
                </div>
              </ThoughtHeader>
              
              <ThoughtContent>{thought.content}</ThoughtContent>
              
              {thought.data && Object.keys(thought.data).length > 0 && (
                <ThoughtData>
                  {Object.entries(thought.data).slice(0, 3).map(([key, value]) => (
                    <div key={key}>
                      <strong>{key}:</strong> {typeof value === 'object' ? JSON.stringify(value).slice(0, 50) + '...' : String(value).slice(0, 50)}
                    </div>
                  ))}
                </ThoughtData>
              )}
            </ThoughtBubble>
          ))
        )}
      </ThoughtStream>

      {currentAnalysis && (
        <CurrentAnalysis>
          <AnalysisCard>
            <CardTitle>Current Market Analysis</CardTitle>
            {currentAnalysis.market_data && (
              <>
                <MetricRow>
                  <span className="label">Current Price:</span>
                  <span className="value">${currentAnalysis.market_data.current_price?.toFixed(6) || 'N/A'}</span>
                </MetricRow>
                <MetricRow>
                  <span className="label">Trading Pair:</span>
                  <span className="value">{currentAnalysis.market_data.pair || 'N/A'}</span>
                </MetricRow>
                <MetricRow>
                  <span className="label">Data Points:</span>
                  <span className="value">{currentAnalysis.market_data.data_points || 'N/A'}</span>
                </MetricRow>
              </>
            )}
          </AnalysisCard>

          <AnalysisCard>
            <CardTitle>Final Decision</CardTitle>
            {currentAnalysis.final_decision ? (
              <>
                <MetricRow>
                  <span className="label">Signal:</span>
                  <span className="value" style={{ 
                    color: currentAnalysis.final_decision.signal === 'BUY' ? '#4bffb5' : 
                          currentAnalysis.final_decision.signal === 'SELL' ? '#ff4976' : '#ffa726' 
                  }}>
                    {currentAnalysis.final_decision.signal}
                  </span>
                </MetricRow>
                <MetricRow>
                  <span className="label">Confidence:</span>
                  <span className="value">{(currentAnalysis.final_decision.confidence * 100).toFixed(1)}%</span>
                </MetricRow>
                <div style={{ marginTop: '12px', fontSize: '12px', color: '#ccc' }}>
                  {currentAnalysis.final_decision.reasoning}
                </div>
              </>
            ) : (
              <div style={{ color: '#888' }}>No recent decision available</div>
            )}
          </AnalysisCard>
        </CurrentAnalysis>
      )}
    </DashboardContainer>
  );
};

export default AIThinkingDashboard;
