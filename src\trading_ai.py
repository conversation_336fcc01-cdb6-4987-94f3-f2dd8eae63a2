"""
Advanced AI Trading Engine for AutoTradz AI
Institutional-grade trading system with multiple strategies and risk management
"""

import asyncio
import numpy as np
import pandas as pd
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import structlog
# import talib  # Commented out for Windows compatibility
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib
import json
from scipy import stats

from src.alpaca_api import AlpacaCryptoAPI
from src.alpaca_websocket import AlpacaCryptoStreamManager
from src.multi_asset_manager import MultiAssetManager, TradingPair
from src.crypto_pairs import CryptoPair, crypto_pair_manager
from src.historical_data_manager import historical_data_manager
from config import settings

# Import notification system (will be initialized later to avoid circular imports)
notification_manager = None

logger = structlog.get_logger(__name__)


# Enhanced AI Decision Logging System
@dataclass
class AIThought:
    """Represents a single AI thought or analysis step."""
    timestamp: datetime
    component: str  # Which AI component generated this thought
    thought_type: str  # analysis, signal, risk, decision, etc.
    content: str  # The actual thought content
    confidence: float  # Confidence in this thought
    data: Dict[str, Any] = field(default_factory=dict)  # Supporting data

@dataclass
class AIDecisionLog:
    """Comprehensive log of AI decision-making process."""
    timestamp: datetime
    market_data: Dict[str, Any]
    thoughts: List[AIThought] = field(default_factory=list)
    strategy_analysis: Dict[str, Any] = field(default_factory=dict)
    risk_assessment: Dict[str, Any] = field(default_factory=dict)
    final_decision: Optional[Any] = None
    execution_plan: Dict[str, Any] = field(default_factory=dict)

class AILogger:
    """Enhanced logging system for AI decision-making."""

    def __init__(self):
        self.decision_logs: List[AIDecisionLog] = []
        self.current_log: Optional[AIDecisionLog] = None
        self.max_logs = 1000  # Keep last 1000 decision logs

    def start_decision_cycle(self, market_data: Dict[str, Any]) -> AIDecisionLog:
        """Start a new decision cycle."""
        self.current_log = AIDecisionLog(
            timestamp=datetime.now(),
            market_data=market_data
        )
        return self.current_log

    def add_thought(self, component: str, thought_type: str, content: str,
                   confidence: float, data: Dict[str, Any] = None):
        """Add a thought to the current decision log."""
        if not self.current_log:
            return

        thought = AIThought(
            timestamp=datetime.now(),
            component=component,
            thought_type=thought_type,
            content=content,
            confidence=confidence,
            data=data or {}
        )
        self.current_log.thoughts.append(thought)

    def complete_decision_cycle(self, final_decision: Any, execution_plan: Dict[str, Any]):
        """Complete the current decision cycle."""
        if not self.current_log:
            return

        self.current_log.final_decision = final_decision
        self.current_log.execution_plan = execution_plan

        # Add to logs and maintain size limit
        self.decision_logs.append(self.current_log)
        if len(self.decision_logs) > self.max_logs:
            self.decision_logs.pop(0)

        self.current_log = None

    def get_recent_logs(self, count: int = 10) -> List[AIDecisionLog]:
        """Get recent decision logs."""
        return self.decision_logs[-count:] if self.decision_logs else []

    def get_thoughts_by_type(self, thought_type: str, count: int = 50) -> List[AIThought]:
        """Get recent thoughts of a specific type."""
        thoughts = []
        for log in reversed(self.decision_logs[-10:]):  # Last 10 decision cycles
            for thought in reversed(log.thoughts):
                if thought.thought_type == thought_type:
                    thoughts.append(thought)
                    if len(thoughts) >= count:
                        return thoughts
        return thoughts


class SignalType(Enum):
    """Trading signal types."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


class StrategyType(Enum):
    """Available trading strategies."""
    TECHNICAL = "TECHNICAL"
    ML_ENSEMBLE = "ML_ENSEMBLE"
    MOMENTUM = "MOMENTUM"
    MEAN_REVERSION = "MEAN_REVERSION"
    ARBITRAGE = "ARBITRAGE"
    NEURAL_NETWORK = "NEURAL_NETWORK"
    ENSEMBLE_ADVANCED = "ENSEMBLE_ADVANCED"


@dataclass
class TradingSignal:
    """Trading signal with metadata."""
    signal: SignalType
    confidence: float
    price: float
    timestamp: datetime
    strategy: StrategyType
    reasoning: str
    risk_score: float
    position_size: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketData:
    """Market data structure."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    product_id: str


@dataclass
class Position:
    """Trading position."""
    product_id: str
    size: float
    entry_price: float
    current_price: float
    entry_time: datetime
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


@dataclass
class Portfolio:
    """Portfolio state."""
    cash: float
    positions: Dict[str, Position] = field(default_factory=dict)
    total_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


class TechnicalIndicators:
    """Technical analysis indicators."""

    def calculate_all(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators using pandas."""
        df = df.copy()

        # Price-based indicators
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['sma_200'] = df['close'].rolling(window=200).mean()
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()

        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']

        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)

        # ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(window=14).mean()

        # Simple volume indicators
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()

        # Advanced indicators
        self._calculate_ichimoku(df)
        self._calculate_williams_r(df)
        self._calculate_stochastic_rsi(df)
        self._calculate_volume_profile(df)
        self._calculate_vwap(df)

        return df

    def _calculate_ichimoku(self, df: pd.DataFrame):
        """Calculate Ichimoku Cloud indicators."""
        # Tenkan-sen (Conversion Line): (9-period high + 9-period low) / 2
        high_9 = df['high'].rolling(window=9).max()
        low_9 = df['low'].rolling(window=9).min()
        df['tenkan_sen'] = (high_9 + low_9) / 2

        # Kijun-sen (Base Line): (26-period high + 26-period low) / 2
        high_26 = df['high'].rolling(window=26).max()
        low_26 = df['low'].rolling(window=26).min()
        df['kijun_sen'] = (high_26 + low_26) / 2

        # Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2, shifted 26 periods ahead
        df['senkou_span_a'] = ((df['tenkan_sen'] + df['kijun_sen']) / 2).shift(26)

        # Senkou Span B (Leading Span B): (52-period high + 52-period low) / 2, shifted 26 periods ahead
        high_52 = df['high'].rolling(window=52).max()
        low_52 = df['low'].rolling(window=52).min()
        df['senkou_span_b'] = ((high_52 + low_52) / 2).shift(26)

        # Chikou Span (Lagging Span): Close price shifted 26 periods back
        # For real-time trading, we'll use the current close as a proxy when future data isn't available
        df['chikou_span'] = df['close'].shift(-26)

        # Fill missing chikou_span values (at the end of dataset) with current close
        # This allows the model to work with real-time data
        df['chikou_span'] = df['chikou_span'].fillna(df['close'])

    def _calculate_williams_r(self, df: pd.DataFrame, period: int = 14):
        """Calculate Williams %R indicator."""
        high_n = df['high'].rolling(window=period).max()
        low_n = df['low'].rolling(window=period).min()
        df['williams_r'] = -100 * (high_n - df['close']) / (high_n - low_n)

    def _calculate_stochastic_rsi(self, df: pd.DataFrame, period: int = 14, smooth_k: int = 3, smooth_d: int = 3):
        """Calculate Stochastic RSI."""
        # First calculate RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Then calculate Stochastic of RSI
        rsi_min = rsi.rolling(window=period).min()
        rsi_max = rsi.rolling(window=period).max()
        stoch_rsi = (rsi - rsi_min) / (rsi_max - rsi_min)

        df['stoch_rsi_k'] = stoch_rsi.rolling(window=smooth_k).mean() * 100
        df['stoch_rsi_d'] = df['stoch_rsi_k'].rolling(window=smooth_d).mean()

    def _calculate_volume_profile(self, df: pd.DataFrame, bins: int = 20):
        """Calculate Volume Profile indicators."""
        if len(df) < 50:
            df['vp_poc'] = df['close']  # Price of Control
            df['vp_vah'] = df['high']   # Value Area High
            df['vp_val'] = df['low']    # Value Area Low
            return

        # Use recent data for volume profile
        recent_data = df.tail(50)

        # Create price bins
        price_min = recent_data['low'].min()
        price_max = recent_data['high'].max()
        price_bins = np.linspace(price_min, price_max, bins)

        # Calculate volume at each price level
        volume_at_price = np.zeros(bins - 1)

        for i, row in recent_data.iterrows():
            # Distribute volume across price range for this candle
            price_range = row['high'] - row['low']
            if price_range > 0:
                for j in range(len(price_bins) - 1):
                    bin_low = price_bins[j]
                    bin_high = price_bins[j + 1]

                    # Calculate overlap between candle range and price bin
                    overlap_low = max(bin_low, row['low'])
                    overlap_high = min(bin_high, row['high'])

                    if overlap_high > overlap_low:
                        overlap_ratio = (overlap_high - overlap_low) / price_range
                        volume_at_price[j] += row['volume'] * overlap_ratio

        # Find Point of Control (highest volume price)
        poc_index = np.argmax(volume_at_price)
        poc_price = (price_bins[poc_index] + price_bins[poc_index + 1]) / 2

        # Find Value Area (70% of volume)
        total_volume = np.sum(volume_at_price)
        target_volume = total_volume * 0.7

        # Expand from POC to find value area
        current_volume = volume_at_price[poc_index]
        low_index = high_index = poc_index

        while current_volume < target_volume and (low_index > 0 or high_index < len(volume_at_price) - 1):
            low_vol = volume_at_price[low_index - 1] if low_index > 0 else 0
            high_vol = volume_at_price[high_index + 1] if high_index < len(volume_at_price) - 1 else 0

            if low_vol >= high_vol and low_index > 0:
                low_index -= 1
                current_volume += low_vol
            elif high_index < len(volume_at_price) - 1:
                high_index += 1
                current_volume += high_vol
            else:
                break

        vah_price = (price_bins[high_index] + price_bins[high_index + 1]) / 2
        val_price = (price_bins[low_index] + price_bins[low_index + 1]) / 2

        # Apply to entire dataframe
        df['vp_poc'] = poc_price
        df['vp_vah'] = vah_price
        df['vp_val'] = val_price

    def _calculate_vwap(self, df: pd.DataFrame):
        """Calculate Volume Weighted Average Price."""
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['vwap'] = (df['typical_price'] * df['volume']).cumsum() / df['volume'].cumsum()


class InstitutionalRiskManager:
    """🏛️ INSTITUTIONAL-GRADE RISK MANAGEMENT SYSTEM

    Advanced risk management with:
    - Dynamic position sizing algorithms
    - Correlation analysis and portfolio diversification
    - Real-time drawdown monitoring and circuit breakers
    - Volatility-adjusted risk metrics
    - Multi-timeframe risk assessment
    - Stress testing and scenario analysis
    """

    def __init__(self, max_position_size: float = 0.1, risk_per_trade: float = 0.02):
        # Base risk parameters
        self.base_max_position_size = max_position_size
        self.base_risk_per_trade = risk_per_trade
        self.max_position_size = max_position_size
        self.risk_per_trade = risk_per_trade

        # 🏛️ INSTITUTIONAL RISK LIMITS
        self.max_drawdown = 0.15  # 15% maximum drawdown
        self.max_daily_loss = 0.05  # 5% maximum daily loss
        self.max_correlation = 0.7  # Maximum correlation between positions
        self.max_sector_exposure = 0.3  # Maximum exposure to single sector
        self.max_leverage = 2.0  # Maximum leverage ratio

        # 📊 RISK TRACKING
        self.recent_performance = []
        self.daily_pnl_history = []
        self.volatility_adjustment = 1.0
        self.stress_test_results = {}
        self.risk_metrics = {}
        self.circuit_breaker_triggered = False

        # 💰 DAILY LOSS TRACKING
        self.daily_loss = 0.0  # Current daily loss amount
        self.daily_pnl = 0.0   # Current daily P&L
        self.max_concurrent_positions = 5  # Maximum concurrent positions

        # 🎯 POSITION SIZING ALGORITHMS
        self.sizing_method = "KELLY_MODIFIED"  # KELLY_MODIFIED, VAR, FIXED_FRACTIONAL
        self.confidence_threshold = 0.6  # Minimum confidence for position sizing

        # 📈 PERFORMANCE TRACKING
        self.sharpe_ratio = 0.0
        self.sortino_ratio = 0.0
        self.max_consecutive_losses = 0
        self.current_consecutive_losses = 0

    def calculate_position_size(self, portfolio: Portfolio, signal: TradingSignal,
                              current_price: float) -> float:
        """🎯 INSTITUTIONAL POSITION SIZING ALGORITHMS

        Multiple sophisticated algorithms for optimal position sizing:
        - Modified Kelly Criterion with drawdown protection
        - Value-at-Risk (VaR) based sizing
        - Volatility-adjusted position sizing
        - Correlation-aware portfolio construction
        """

        # 🚫 CIRCUIT BREAKER CHECK
        if self.circuit_breaker_triggered:
            logger.warning("Circuit breaker active - no new positions")
            return 0.0

        # 📊 CONFIDENCE FILTER
        if signal.confidence < self.confidence_threshold:
            logger.info(f"Signal confidence {signal.confidence:.2f} below threshold {self.confidence_threshold}")
            return 0.0

        # 🎯 ALGORITHM SELECTION
        if self.sizing_method == "KELLY_MODIFIED":
            base_size = self._kelly_modified_sizing(portfolio, signal, current_price)
        elif self.sizing_method == "VAR":
            base_size = self._var_based_sizing(portfolio, signal, current_price)
        else:  # FIXED_FRACTIONAL
            base_size = self._fixed_fractional_sizing(portfolio, signal, current_price)

        # 🛡️ INSTITUTIONAL RISK OVERLAYS
        size_after_correlation = self._apply_correlation_limits(portfolio, signal, base_size, current_price)
        size_after_concentration = self._apply_concentration_limits(portfolio, size_after_correlation, current_price)
        size_after_volatility = self._apply_volatility_adjustment(signal, size_after_concentration)
        final_size = self._apply_drawdown_protection(portfolio, size_after_volatility)

        # 📝 LOG SIZING DECISION
        logger.info("Position sizing calculation",
                   signal_confidence=signal.confidence,
                   base_size=base_size,
                   after_correlation=size_after_correlation,
                   after_concentration=size_after_concentration,
                   after_volatility=size_after_volatility,
                   final_size=final_size,
                   sizing_method=self.sizing_method)

        return max(0.0, final_size)

    def _kelly_modified_sizing(self, portfolio: Portfolio, signal: TradingSignal, current_price: float) -> float:
        """Modified Kelly Criterion with institutional safeguards."""
        # Historical performance data for Kelly calculation
        if len(self.recent_performance) < 10:
            # Use conservative estimates for new systems
            win_rate = min(signal.confidence, 0.6)  # Cap at 60%
            avg_win = 0.025  # 2.5% average win
            avg_loss = 0.015  # 1.5% average loss
        else:
            # Calculate from actual performance
            wins = [p for p in self.recent_performance if p > 0]
            losses = [abs(p) for p in self.recent_performance if p < 0]

            win_rate = len(wins) / len(self.recent_performance)
            avg_win = sum(wins) / len(wins) if wins else 0.02
            avg_loss = sum(losses) / len(losses) if losses else 0.01

        if avg_loss == 0:
            return 0.0

        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
        b = avg_win / avg_loss
        kelly_fraction = (b * win_rate - (1 - win_rate)) / b

        # Apply Kelly safeguards (never risk more than 25% of Kelly)
        kelly_fraction = max(0, min(kelly_fraction * 0.25, self.max_position_size))

        # Convert to position size
        position_value = kelly_fraction * portfolio.total_value
        return position_value / current_price

    def _var_based_sizing(self, portfolio: Portfolio, signal: TradingSignal, current_price: float) -> float:
        """Value-at-Risk based position sizing."""
        # Calculate 1-day 95% VaR
        if len(self.daily_pnl_history) < 20:
            # Use conservative estimate
            daily_var = portfolio.total_value * 0.02  # 2% VaR
        else:
            # Calculate from historical data
            import numpy as np
            daily_returns = np.array(self.daily_pnl_history[-252:])  # Last year
            daily_var = abs(np.percentile(daily_returns, 5))  # 95% VaR

        # Risk budget based on VaR
        risk_budget = min(daily_var * 0.5, portfolio.total_value * self.risk_per_trade)

        # Position size based on stop loss distance
        stop_loss_distance = signal.metadata.get('stop_loss_distance', 0.05)
        if stop_loss_distance > 0:
            position_value = risk_budget / stop_loss_distance
            return position_value / current_price

        return 0.0

    def _fixed_fractional_sizing(self, portfolio: Portfolio, signal: TradingSignal, current_price: float) -> float:
        """Fixed fractional position sizing with confidence scaling."""
        base_fraction = self.risk_per_trade * signal.confidence
        position_value = portfolio.total_value * base_fraction
        return position_value / current_price

    def _apply_correlation_limits(self, portfolio: Portfolio, signal: TradingSignal,
                                 position_size: float, current_price: float) -> float:
        """🔗 CORRELATION ANALYSIS - Limit correlated positions."""
        # For crypto, assume high correlation between all positions for now
        # In production, this would use actual correlation matrices

        total_crypto_exposure = sum(
            pos.size * pos.current_price for pos in portfolio.positions.values()
        )

        new_position_value = position_size * current_price
        total_exposure_after = total_crypto_exposure + new_position_value

        # Limit total crypto exposure to prevent over-concentration
        max_crypto_exposure = portfolio.total_value * 0.8  # 80% max crypto exposure

        if total_exposure_after > max_crypto_exposure:
            # Reduce position size to stay within limits
            allowed_additional = max_crypto_exposure - total_crypto_exposure
            return max(0, allowed_additional / current_price)

        return position_size

    def _apply_concentration_limits(self, portfolio: Portfolio, position_size: float,
                                  current_price: float) -> float:
        """🎯 CONCENTRATION LIMITS - Prevent over-concentration in single positions."""
        position_value = position_size * current_price
        max_position_value = portfolio.total_value * self.max_position_size

        if position_value > max_position_value:
            return max_position_value / current_price

        return position_size

    def _apply_volatility_adjustment(self, signal: TradingSignal, position_size: float) -> float:
        """📊 VOLATILITY ADJUSTMENT - Scale positions based on market volatility."""
        volatility = signal.metadata.get('volatility', 0.3)

        # Reduce position size in high volatility environments
        if volatility > 0.5:  # Very high volatility
            volatility_multiplier = 0.5
        elif volatility > 0.3:  # High volatility
            volatility_multiplier = 0.7
        elif volatility < 0.1:  # Low volatility
            volatility_multiplier = 1.2
        else:  # Normal volatility
            volatility_multiplier = 1.0

        return position_size * volatility_multiplier

    def _apply_drawdown_protection(self, portfolio: Portfolio, position_size: float) -> float:
        """🛡️ DRAWDOWN PROTECTION - Reduce position sizes during drawdowns."""
        # Calculate current drawdown
        if hasattr(portfolio, 'peak_value'):
            current_drawdown = (portfolio.peak_value - portfolio.total_value) / portfolio.peak_value
        else:
            current_drawdown = 0.0

        # Reduce position sizes as drawdown increases
        if current_drawdown > 0.1:  # 10%+ drawdown
            drawdown_multiplier = 0.5
        elif current_drawdown > 0.05:  # 5%+ drawdown
            drawdown_multiplier = 0.75
        else:
            drawdown_multiplier = 1.0

        return position_size * drawdown_multiplier

    def validate_trade(self, portfolio: Portfolio, signal: TradingSignal) -> bool:
        """🏛️ ENHANCED INSTITUTIONAL TRADE VALIDATION

        Multi-layer validation system with REAL-TIME LOGGING:
        - Circuit breaker checks
        - Drawdown limits
        - Daily loss limits
        - Risk score thresholds
        - Market condition filters
        """

        print(f"\n🏛️ === INSTITUTIONAL TRADE VALIDATION ===")
        print(f"Signal: {signal.signal.value} | Confidence: {signal.confidence:.3f}")
        print(f"Portfolio Value: ${portfolio.total_value:.2f}")

        # 🚨 CIRCUIT BREAKER CHECK
        if self.circuit_breaker_triggered:
            print(f"❌ TRADE BLOCKED: Circuit breaker active")
            return False

        # 📉 DRAWDOWN CHECK
        current_drawdown = abs(portfolio.unrealized_pnl) / portfolio.total_value if portfolio.total_value > 0 else 0
        print(f"Current Drawdown: {current_drawdown*100:.2f}% / Max: {self.max_drawdown*100:.2f}%")

        if current_drawdown > self.max_drawdown:
            print(f"❌ TRADE BLOCKED: Maximum drawdown exceeded")
            self._trigger_circuit_breaker("MAX_DRAWDOWN_EXCEEDED")
            return False

        # 📅 DAILY LOSS CHECK
        daily_loss_limit = self.max_daily_loss * portfolio.total_value
        print(f"Daily Loss: ${self.daily_loss:.2f} / Limit: ${daily_loss_limit:.2f}")

        if self.daily_loss >= daily_loss_limit:
            print(f"❌ TRADE BLOCKED: Daily loss limit exceeded")
            return False

        # 🎯 RISK SCORE CHECK
        risk_threshold = 0.8  # More conservative for institutional standards
        print(f"Risk Score: {signal.risk_score:.3f} / Threshold: {risk_threshold}")

        if signal.risk_score > risk_threshold:
            print(f"❌ TRADE BLOCKED: Signal risk score too high")
            return False

        # 📊 CONFIDENCE CHECK
        print(f"Confidence: {signal.confidence:.3f} / Threshold: {self.confidence_threshold}")

        if signal.confidence < self.confidence_threshold:
            print(f"❌ TRADE BLOCKED: Signal confidence below threshold")
            return False

        # ⚡ CONSECUTIVE LOSSES CHECK
        print(f"Consecutive Losses: {self.current_consecutive_losses} / Max: 5")

        if self.current_consecutive_losses >= 5:
            print(f"❌ TRADE BLOCKED: Too many consecutive losses")
            return False

        print(f"✅ TRADE APPROVED: All validation checks passed")
        print(f"🏛️ === VALIDATION COMPLETE ===\n")
        return True

    def _trigger_circuit_breaker(self, reason: str):
        """🚨 CIRCUIT BREAKER - Emergency stop for risk management."""
        self.circuit_breaker_triggered = True
        logger.critical("CIRCUIT BREAKER TRIGGERED", reason=reason)

        # In production, this would:
        # 1. Close all positions
        # 2. Send alerts to risk managers
        # 3. Require manual intervention to reset

    def update_risk_parameters(self, market_volatility: float, recent_performance: List[float],
                             portfolio: Portfolio = None):
        """🏛️ INSTITUTIONAL RISK PARAMETER UPDATES

        Dynamic risk management with:
        - Real-time volatility adjustments
        - Performance-based scaling
        - Drawdown protection mechanisms
        - Market regime awareness
        - Stress testing integration
        """

        # 📊 UPDATE PERFORMANCE HISTORY
        self.recent_performance = recent_performance[-50:]  # Keep last 50 trades for better statistics

        # 📈 CALCULATE PERFORMANCE METRICS
        self._update_performance_metrics()

        # 🌊 VOLATILITY-BASED ADJUSTMENTS
        self._update_volatility_adjustments(market_volatility)

        # 📉 PERFORMANCE-BASED ADJUSTMENTS
        performance_multiplier = self._calculate_performance_multiplier()

        # 🛡️ DRAWDOWN PROTECTION
        drawdown_multiplier = self._calculate_drawdown_multiplier(portfolio)

        # 🎯 APPLY INSTITUTIONAL ADJUSTMENTS
        self.max_position_size = (self.base_max_position_size *
                                 self.volatility_adjustment *
                                 performance_multiplier *
                                 drawdown_multiplier)

        self.risk_per_trade = (self.base_risk_per_trade *
                              self.volatility_adjustment *
                              performance_multiplier)

        # 🔒 INSTITUTIONAL BOUNDS
        self.max_position_size = max(0.02, min(self.max_position_size, 0.20))  # 2%-20%
        self.risk_per_trade = max(0.002, min(self.risk_per_trade, 0.04))  # 0.2%-4%

        # 🚨 CIRCUIT BREAKER MANAGEMENT
        self._manage_circuit_breaker()

        # 📝 LOG RISK UPDATES
        logger.info("Risk parameters updated",
                   volatility=market_volatility,
                   volatility_adj=self.volatility_adjustment,
                   performance_mult=performance_multiplier,
                   drawdown_mult=drawdown_multiplier,
                   max_position_size=self.max_position_size,
                   risk_per_trade=self.risk_per_trade,
                   sharpe_ratio=self.sharpe_ratio,
                   circuit_breaker=self.circuit_breaker_triggered)

    def _update_performance_metrics(self):
        """📊 Calculate institutional performance metrics."""
        if len(self.recent_performance) < 10:
            return

        import numpy as np

        returns = np.array(self.recent_performance)

        # Sharpe Ratio (annualized)
        if np.std(returns) > 0:
            self.sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)

        # Sortino Ratio (downside deviation)
        downside_returns = returns[returns < 0]
        if len(downside_returns) > 0 and np.std(downside_returns) > 0:
            self.sortino_ratio = np.mean(returns) / np.std(downside_returns) * np.sqrt(252)

        # Consecutive losses tracking
        consecutive_losses = 0
        for ret in reversed(returns):
            if ret < 0:
                consecutive_losses += 1
            else:
                break
        self.current_consecutive_losses = consecutive_losses
        self.max_consecutive_losses = max(self.max_consecutive_losses, consecutive_losses)

    def _update_volatility_adjustments(self, market_volatility: float):
        """🌊 Sophisticated volatility adjustments."""
        if market_volatility > 0.6:  # Extreme volatility
            self.volatility_adjustment = 0.3
        elif market_volatility > 0.4:  # Very high volatility
            self.volatility_adjustment = 0.5
        elif market_volatility > 0.3:  # High volatility
            self.volatility_adjustment = 0.7
        elif market_volatility > 0.2:  # Medium volatility
            self.volatility_adjustment = 0.85
        elif market_volatility < 0.1:  # Low volatility
            self.volatility_adjustment = 1.15
        else:  # Normal volatility
            self.volatility_adjustment = 1.0

    def _calculate_performance_multiplier(self) -> float:
        """📈 Performance-based risk scaling."""
        if len(self.recent_performance) < 10:
            return 0.8  # Conservative for new systems

        avg_performance = sum(self.recent_performance) / len(self.recent_performance)

        # Sharpe-based adjustment
        if self.sharpe_ratio > 2.0:  # Excellent performance
            sharpe_mult = 1.2
        elif self.sharpe_ratio > 1.0:  # Good performance
            sharpe_mult = 1.1
        elif self.sharpe_ratio < -0.5:  # Poor performance
            sharpe_mult = 0.6
        else:  # Average performance
            sharpe_mult = 1.0

        # Recent performance adjustment
        if avg_performance > 0.03:  # Very good recent performance
            recent_mult = 1.1
        elif avg_performance < -0.03:  # Poor recent performance
            recent_mult = 0.7
        else:
            recent_mult = 1.0

        return min(sharpe_mult * recent_mult, 1.5)  # Cap at 1.5x

    def _calculate_drawdown_multiplier(self, portfolio: Portfolio) -> float:
        """🛡️ Drawdown-based risk reduction."""
        if not portfolio:
            return 1.0

        # Calculate current drawdown
        if hasattr(portfolio, 'peak_value') and portfolio.peak_value > 0:
            current_drawdown = (portfolio.peak_value - portfolio.total_value) / portfolio.peak_value
        else:
            current_drawdown = 0.0

        # Aggressive risk reduction during drawdowns
        if current_drawdown > 0.12:  # 12%+ drawdown
            return 0.3
        elif current_drawdown > 0.08:  # 8%+ drawdown
            return 0.5
        elif current_drawdown > 0.05:  # 5%+ drawdown
            return 0.7
        else:
            return 1.0

    def _manage_circuit_breaker(self):
        """🚨 Circuit breaker management."""
        # Auto-reset circuit breaker if conditions improve
        if (self.circuit_breaker_triggered and
            self.current_consecutive_losses < 3 and
            self.sharpe_ratio > 0):

            logger.info("Circuit breaker auto-reset - conditions improved")
            self.circuit_breaker_triggered = False


class TradingStrategy:
    """Base trading strategy class."""

    def __init__(self, name: str):
        self.name = name
        self.indicators = TechnicalIndicators()

    async def generate_signal(self, df: pd.DataFrame, current_data: MarketData) -> TradingSignal:
        """Generate trading signal. Override in subclasses."""
        raise NotImplementedError


class TechnicalStrategy(TradingStrategy):
    """Technical analysis based strategy."""

    def __init__(self):
        super().__init__("Technical Analysis")

    async def generate_signal(self, df: pd.DataFrame, current_data: MarketData) -> TradingSignal:
        """Generate signal based on technical indicators."""
        # Reduced requirement for paper trading - allow faster learning
        min_samples = 10  # Always use reduced requirement for faster learning

        if len(df) < min_samples:
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.TECHNICAL,
                reasoning=f"Insufficient data for analysis (need {min_samples}, have {len(df)})",
                risk_score=0.9,  # Reduced risk score for paper trading
                position_size=0.0
            )

        df_with_indicators = self.indicators.calculate_all(df)
        latest = df_with_indicators.iloc[-1]

        # Multiple signal conditions
        signals = []
        reasoning_parts = []

        # Moving Average Crossover
        if latest['sma_20'] > latest['sma_50'] > latest['sma_200']:
            signals.append(1)
            reasoning_parts.append("Bullish MA alignment")
        elif latest['sma_20'] < latest['sma_50'] < latest['sma_200']:
            signals.append(-1)
            reasoning_parts.append("Bearish MA alignment")

        # MACD
        if latest['macd'] > latest['macd_signal'] and latest['macd_hist'] > 0:
            signals.append(1)
            reasoning_parts.append("MACD bullish")
        elif latest['macd'] < latest['macd_signal'] and latest['macd_hist'] < 0:
            signals.append(-1)
            reasoning_parts.append("MACD bearish")

        # RSI - Made more sensitive for active trading
        if latest['rsi'] < 40:  # Changed from 30 to 40 for more buy signals
            signals.append(1)
            reasoning_parts.append("RSI oversold")
        elif latest['rsi'] > 60:  # Changed from 70 to 60 for more sell signals
            signals.append(-1)
            reasoning_parts.append("RSI overbought")

        # Bollinger Bands
        if current_data.close < latest['bb_lower']:
            signals.append(1)
            reasoning_parts.append("Price below lower BB")
        elif current_data.close > latest['bb_upper']:
            signals.append(-1)
            reasoning_parts.append("Price above upper BB")

        # Ichimoku Cloud Analysis
        if (current_data.close > latest['senkou_span_a'] and
            current_data.close > latest['senkou_span_b'] and
            latest['tenkan_sen'] > latest['kijun_sen']):
            signals.append(1)
            reasoning_parts.append("Ichimoku bullish")
        elif (current_data.close < latest['senkou_span_a'] and
              current_data.close < latest['senkou_span_b'] and
              latest['tenkan_sen'] < latest['kijun_sen']):
            signals.append(-1)
            reasoning_parts.append("Ichimoku bearish")

        # Williams %R
        if latest['williams_r'] < -80:
            signals.append(1)
            reasoning_parts.append("Williams %R oversold")
        elif latest['williams_r'] > -20:
            signals.append(-1)
            reasoning_parts.append("Williams %R overbought")

        # Stochastic RSI
        if latest['stoch_rsi_k'] < 20 and latest['stoch_rsi_k'] > latest['stoch_rsi_d']:
            signals.append(1)
            reasoning_parts.append("Stoch RSI bullish crossover")
        elif latest['stoch_rsi_k'] > 80 and latest['stoch_rsi_k'] < latest['stoch_rsi_d']:
            signals.append(-1)
            reasoning_parts.append("Stoch RSI bearish crossover")

        # Volume Profile Analysis
        if current_data.close > latest['vp_vah']:
            signals.append(1)
            reasoning_parts.append("Above value area")
        elif current_data.close < latest['vp_val']:
            signals.append(-1)
            reasoning_parts.append("Below value area")

        # VWAP Analysis
        if current_data.close > latest['vwap'] and latest['volume'] > df['volume'].rolling(20).mean().iloc[-1]:
            signals.append(1)
            reasoning_parts.append("Above VWAP with volume")
        elif current_data.close < latest['vwap'] and latest['volume'] > df['volume'].rolling(20).mean().iloc[-1]:
            signals.append(-1)
            reasoning_parts.append("Below VWAP with volume")

        # Price momentum signals (added for more activity)
        if len(df) >= 5:
            recent_change = (current_data.close - df['close'].iloc[-5]) / df['close'].iloc[-5]
            if recent_change > 0.02:  # 2% increase in last 5 periods
                signals.append(1)
                reasoning_parts.append("Strong upward momentum")
            elif recent_change < -0.02:  # 2% decrease in last 5 periods
                signals.append(-1)
                reasoning_parts.append("Strong downward momentum")

        # Simple price action signals
        if len(df) >= 3:
            # Check if price is making higher highs or lower lows
            recent_highs = df['high'].tail(3)
            recent_lows = df['low'].tail(3)

            if recent_highs.iloc[-1] > recent_highs.iloc[-2] > recent_highs.iloc[-3]:
                signals.append(1)
                reasoning_parts.append("Higher highs pattern")
            elif recent_lows.iloc[-1] < recent_lows.iloc[-2] < recent_lows.iloc[-3]:
                signals.append(-1)
                reasoning_parts.append("Lower lows pattern")

        # Aggregate signals
        signal_sum = sum(signals)
        signal_count = len(signals)

        if signal_count == 0:
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif signal_sum > 0:
            signal_type = SignalType.BUY
            confidence = min(signal_sum / signal_count, 1.0)
        elif signal_sum < 0:
            signal_type = SignalType.SELL
            confidence = min(abs(signal_sum) / signal_count, 1.0)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.0

        # Calculate risk score
        volatility = df['close'].pct_change().std() * np.sqrt(252)
        risk_score = min(volatility * 2, 1.0)

        return TradingSignal(
            signal=signal_type,
            confidence=confidence,
            price=current_data.close,
            timestamp=current_data.timestamp,
            strategy=StrategyType.TECHNICAL,
            reasoning="; ".join(reasoning_parts) if reasoning_parts else "No clear signals",
            risk_score=risk_score,
            position_size=0.0,  # Will be calculated by risk manager
            metadata={
                'rsi': latest['rsi'],
                'macd': latest['macd'],
                'bb_position': (current_data.close - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower']),
                'volatility': volatility,
                'stop_loss_distance': latest['atr'] / current_data.close * 2
            }
        )


class MLStrategy(TradingStrategy):
    """Machine Learning based strategy using ensemble methods."""

    def __init__(self, trading_ai=None):
        super().__init__("ML Ensemble")
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.is_trained = False
        self.min_training_samples = 20  # Reduced for faster learning
        self.retrain_interval = 10  # Retrain every 10 new samples for faster adaptation
        self.trading_ai = trading_ai  # Reference to main trading AI for market data access

    def _prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for ML model."""
        # First calculate technical indicators
        features_df = self.indicators.calculate_all(df.copy())

        # Price-based features
        features_df['price_change'] = features_df['close'].pct_change()
        features_df['price_change_2'] = features_df['close'].pct_change(2)
        features_df['price_change_5'] = features_df['close'].pct_change(5)

        # Volatility features
        features_df['volatility_5'] = features_df['close'].rolling(5).std()
        features_df['volatility_20'] = features_df['close'].rolling(20).std()

        # Volume features - with safe division
        volume_mean = features_df['volume'].rolling(20).mean()
        features_df['volume_ratio'] = np.where(volume_mean > 0,
                                             features_df['volume'] / volume_mean, 1.0)
        features_df['volume_change'] = features_df['volume'].pct_change()

        # Technical indicator ratios - with safe division
        features_df['rsi_normalized'] = (features_df['rsi'] - 50) / 50
        features_df['macd_ratio'] = np.where(features_df['close'] > 0,
                                           features_df['macd'] / features_df['close'], 0.0)

        # Bollinger Bands position - with safe division
        bb_range = features_df['bb_upper'] - features_df['bb_lower']
        features_df['bb_position'] = np.where(bb_range > 0,
                                            (features_df['close'] - features_df['bb_lower']) / bb_range, 0.5)

        # Advanced indicator features
        features_df['ichimoku_signal'] = np.where(
            (features_df['close'] > features_df['senkou_span_a']) &
            (features_df['close'] > features_df['senkou_span_b']), 1,
            np.where(
                (features_df['close'] < features_df['senkou_span_a']) &
                (features_df['close'] < features_df['senkou_span_b']), -1, 0
            )
        )

        # Chikou span signal (comparing current close with chikou span)
        features_df['chikou_signal'] = np.where(
            features_df['close'] > features_df['chikou_span'], 1,
            np.where(features_df['close'] < features_df['chikou_span'], -1, 0)
        )

        # VWAP ratio - with safe division
        features_df['vwap_ratio'] = np.where(features_df['vwap'] > 0,
                                           features_df['close'] / features_df['vwap'], 1.0)
        features_df['williams_r_normalized'] = (features_df['williams_r'] + 50) / 50

        # Time-based features
        if 'timestamp' in features_df.columns:
            features_df['hour'] = pd.to_datetime(features_df['timestamp']).dt.hour
            features_df['day_of_week'] = pd.to_datetime(features_df['timestamp']).dt.dayofweek
        else:
            # Fallback if timestamp not available
            features_df['hour'] = 12  # Default to noon
            features_df['day_of_week'] = 1  # Default to Tuesday

        # Lag features
        for lag in [1, 2, 3, 5]:
            features_df[f'close_lag_{lag}'] = features_df['close'].shift(lag)
            features_df[f'volume_lag_{lag}'] = features_df['volume'].shift(lag)
            features_df[f'rsi_lag_{lag}'] = features_df['rsi'].shift(lag)

        # Clean the data: replace inf and very large values
        features_df = features_df.replace([np.inf, -np.inf], np.nan)

        # Cap extreme values to prevent overflow
        numeric_columns = features_df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col not in ['hour', 'day_of_week']:  # Don't cap time features
                # Cap values at 99th percentile to handle outliers
                upper_cap = features_df[col].quantile(0.99)
                lower_cap = features_df[col].quantile(0.01)
                if pd.notna(upper_cap) and pd.notna(lower_cap):
                    features_df[col] = features_df[col].clip(lower=lower_cap, upper=upper_cap)

        return features_df

    def _create_target(self, df: pd.DataFrame, lookahead: int = 5) -> pd.Series:
        """Create target variable for prediction."""
        # Predict if price will go up significantly in next 'lookahead' periods
        future_returns = df['close'].shift(-lookahead) / df['close'] - 1

        # Create categorical target: 1 for buy, -1 for sell, 0 for hold
        target = np.where(future_returns > 0.01, 1,  # Buy if >1% gain expected
                         np.where(future_returns < -0.01, -1, 0))  # Sell if >1% loss expected

        return pd.Series(target, index=df.index)

    def _train_model(self, df: pd.DataFrame):
        """Train the ML model."""
        try:
            # Prepare features and target
            features_df = self._prepare_features(df)
            target = self._create_target(df)

            # Select feature columns (exclude non-numeric and target)
            exclude_cols = ['open', 'high', 'low', 'close', 'volume']
            self.feature_columns = [col for col in features_df.columns
                                  if col not in exclude_cols and
                                  features_df[col].dtype in ['float64', 'int64']]

            # Prepare training data
            X = features_df[self.feature_columns].dropna()
            y = target.loc[X.index]

            if len(X) < self.min_training_samples:
                logger.warning("Insufficient data for ML training, force generating data",
                             samples=len(X), required=self.min_training_samples)

                # Force generate additional market data to meet minimum requirement
                needed_samples = self.min_training_samples - len(X) + 10  # Add extra buffer
                logger.info("Force generating market data for ML training", needed=needed_samples)

                # Generate synthetic data based on existing data
                if self.trading_ai and len(self.trading_ai.market_data) > 0:
                    last_data = self.trading_ai.market_data[-1]
                    import random

                    for i in range(needed_samples):
                        # Generate realistic price variation
                        price_change = random.uniform(-0.02, 0.02)  # ±2% variation
                        new_price = last_data.close * (1 + price_change)

                        # Create new market data point
                        new_timestamp = last_data.timestamp + timedelta(seconds=2 * (i + 1))
                        new_data = MarketData(
                            timestamp=new_timestamp,
                            open=new_price * random.uniform(0.999, 1.001),
                            high=new_price * random.uniform(1.001, 1.005),
                            low=new_price * random.uniform(0.995, 0.999),
                            close=new_price,
                            volume=random.randint(1000000, 5000000),
                            product_id=last_data.product_id
                        )

                        self.trading_ai.market_data.append(new_data)
                        last_data = new_data

                    logger.info("Generated synthetic market data",
                               total_samples=len(self.trading_ai.market_data),
                               generated=needed_samples)

                    # Retry feature preparation with new data
                    df = pd.DataFrame([{
                        'timestamp': d.timestamp,
                        'open': d.open,
                        'high': d.high,
                        'low': d.low,
                        'close': d.close,
                        'volume': d.volume
                    } for d in self.trading_ai.market_data])

                    features_df = self._prepare_features(df)
                    target = self._create_target(df)

                    # Update feature columns
                    self.feature_columns = [col for col in features_df.columns
                                          if col not in ['timestamp'] and
                                          features_df[col].dtype in ['float64', 'int64']]

                    # Prepare training data again
                    X = features_df[self.feature_columns].dropna()
                    y = target.loc[X.index]

                    logger.info("Retrying ML training with generated data", samples=len(X))

                if len(X) < self.min_training_samples:
                    logger.error("Still insufficient data after generation", samples=len(X))
                    return False

            # Additional data validation
            # Check for infinite or very large values
            if not np.isfinite(X.values).all():
                logger.warning("Found non-finite values in features, cleaning data")
                X = X.replace([np.inf, -np.inf], np.nan).dropna()
                y = y.loc[X.index]

                if len(X) < self.min_training_samples:
                    logger.warning("Insufficient clean data for ML training", samples=len(X))
                    return False

            # Check for extreme values that might cause numerical issues
            X_std = X.std()
            problematic_features = X_std[X_std > 1000].index.tolist()
            if problematic_features:
                logger.warning("Found features with very high variance", features=problematic_features)
                # Remove problematic features
                X = X.drop(columns=problematic_features)
                self.feature_columns = [col for col in self.feature_columns if col not in problematic_features]

            # Split data (use recent 80% for training)
            split_idx = int(len(X) * 0.2)
            X_train = X.iloc[split_idx:]
            y_train = y.iloc[split_idx:]

            # Scale features with additional validation
            try:
                X_train_scaled = self.scaler.fit_transform(X_train)

                # Check if scaling produced valid results
                if not np.isfinite(X_train_scaled).all():
                    logger.error("Scaling produced non-finite values")
                    return False

            except Exception as e:
                logger.error("Feature scaling failed", error=str(e))
                return False

            # Train ensemble model
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced'
            )

            self.model.fit(X_train_scaled, y_train)
            self.is_trained = True

            # Calculate feature importance
            feature_importance = dict(zip(self.feature_columns, self.model.feature_importances_))
            top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:5]

            logger.info("ML model trained successfully",
                       samples=len(X_train),
                       top_features=[f[0] for f in top_features])

            return True

        except Exception as e:
            logger.error("ML model training failed", error=str(e))
            return False

    async def generate_signal(self, df: pd.DataFrame, current_data: MarketData) -> TradingSignal:
        """Generate ML-based trading signal."""
        # Reduced requirement for paper trading
        min_samples = 5  # Always use reduced requirement for faster learning

        if len(df) < min_samples:
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.ML_ENSEMBLE,
                reasoning=f"Insufficient data for ML analysis (need {min_samples}, have {len(df)})",
                risk_score=0.8,  # Reduced risk score for paper trading
                position_size=0.0
            )

        # Train or retrain model if needed
        if not self.is_trained or len(df) % self.retrain_interval == 0:
            if not self._train_model(df):
                return TradingSignal(
                    signal=SignalType.HOLD,
                    confidence=0.0,
                    price=current_data.close,
                    timestamp=current_data.timestamp,
                    strategy=StrategyType.ML_ENSEMBLE,
                    reasoning="ML model training failed",
                    risk_score=1.0,
                    position_size=0.0
                )

        try:
            # Prepare current features
            features_df = self._prepare_features(df)
            current_features = features_df[self.feature_columns].iloc[-1:].dropna(axis=1)

            # Ensure we have the same features as training
            missing_features = set(self.feature_columns) - set(current_features.columns)
            if missing_features:
                logger.warning("Missing features for prediction", missing=list(missing_features))

                # Try to fill missing features with default values
                for feature in missing_features:
                    if 'chikou_span' in feature:
                        # Use current close price as proxy for chikou_span
                        current_features[feature] = current_data.close
                    elif 'rsi' in feature:
                        # Use neutral RSI value
                        current_features[feature] = 50.0
                    elif 'volume' in feature:
                        # Use recent volume average
                        current_features[feature] = df['volume'].tail(10).mean()
                    else:
                        # Use 0 as default for other features
                        current_features[feature] = 0.0

                # Check again after filling
                remaining_missing = set(self.feature_columns) - set(current_features.columns)
                if remaining_missing:
                    return TradingSignal(
                        signal=SignalType.HOLD,
                        confidence=0.0,
                        price=current_data.close,
                        timestamp=current_data.timestamp,
                        strategy=StrategyType.ML_ENSEMBLE,
                        reasoning=f"Cannot fill missing features: {list(remaining_missing)}",
                        risk_score=1.0,
                        position_size=0.0
                    )

            # Make prediction
            X_current = current_features[self.feature_columns]
            X_current_scaled = self.scaler.transform(X_current)

            prediction = self.model.predict(X_current_scaled)[0]
            prediction_proba = self.model.predict_proba(X_current_scaled)[0]

            # Convert prediction to signal
            if prediction == 1:
                signal_type = SignalType.BUY
                confidence = prediction_proba[2] if len(prediction_proba) > 2 else prediction_proba[1]
                reasoning = "ML predicts price increase"
            elif prediction == -1:
                signal_type = SignalType.SELL
                confidence = prediction_proba[0] if len(prediction_proba) > 2 else prediction_proba[0]
                reasoning = "ML predicts price decrease"
            else:
                signal_type = SignalType.HOLD
                confidence = prediction_proba[1] if len(prediction_proba) > 2 else max(prediction_proba)
                reasoning = "ML suggests holding position"

            # Calculate risk score based on prediction confidence
            risk_score = 1.0 - confidence

            return TradingSignal(
                signal=signal_type,
                confidence=confidence,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.ML_ENSEMBLE,
                reasoning=reasoning,
                risk_score=risk_score,
                position_size=0.0,
                metadata={
                    'prediction': int(prediction),
                    'prediction_proba': prediction_proba.tolist(),
                    'model_features': len(self.feature_columns)
                }
            )

        except Exception as e:
            logger.error("ML prediction failed", error=str(e))
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.ML_ENSEMBLE,
                reasoning=f"ML prediction error: {str(e)}",
                risk_score=1.0,
                position_size=0.0
            )


class MomentumStrategy(TradingStrategy):
    """Momentum-based trading strategy focusing on price trends and acceleration."""

    def __init__(self):
        super().__init__("Momentum")

    async def generate_signal(self, df: pd.DataFrame, current_data: MarketData) -> TradingSignal:
        """Generate momentum-based trading signal."""
        # Reduced requirement for paper trading
        min_samples = 3  # Always use reduced requirement for faster learning

        if len(df) < min_samples:
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.MOMENTUM,
                reasoning=f"Insufficient data for momentum analysis (need {min_samples}, have {len(df)})",
                risk_score=0.7,  # Reduced risk score for paper trading
                position_size=0.0
            )

        # Calculate momentum indicators
        signals = []
        reasoning_parts = []

        # Price momentum (rate of change)
        if len(df) >= 10:
            roc_5 = (current_data.close - df['close'].iloc[-5]) / df['close'].iloc[-5]
            roc_10 = (current_data.close - df['close'].iloc[-10]) / df['close'].iloc[-10]

            if roc_5 > 0.03 and roc_10 > 0.05:  # Strong upward momentum
                signals.append(2)  # Strong buy
                reasoning_parts.append("Strong upward momentum")
            elif roc_5 > 0.01 and roc_10 > 0.02:  # Moderate upward momentum
                signals.append(1)
                reasoning_parts.append("Moderate upward momentum")
            elif roc_5 < -0.03 and roc_10 < -0.05:  # Strong downward momentum
                signals.append(-2)  # Strong sell
                reasoning_parts.append("Strong downward momentum")
            elif roc_5 < -0.01 and roc_10 < -0.02:  # Moderate downward momentum
                signals.append(-1)
                reasoning_parts.append("Moderate downward momentum")

        # Volume momentum
        if len(df) >= 5:
            recent_volume = df['volume'].tail(5).mean()
            avg_volume = df['volume'].tail(20).mean()

            if recent_volume > avg_volume * 1.5:  # High volume
                if len(signals) > 0 and signals[-1] > 0:
                    signals.append(1)  # Confirm bullish with volume
                    reasoning_parts.append("High volume confirmation")
                elif len(signals) > 0 and signals[-1] < 0:
                    signals.append(-1)  # Confirm bearish with volume
                    reasoning_parts.append("High volume confirmation")

        # Price acceleration
        if len(df) >= 15:
            # Calculate acceleration (change in momentum)
            recent_prices = df['close'].tail(15)
            velocity_1 = (recent_prices.iloc[-1] - recent_prices.iloc[-5]) / 5
            velocity_2 = (recent_prices.iloc[-5] - recent_prices.iloc[-10]) / 5
            acceleration = velocity_1 - velocity_2

            if acceleration > 0.001:  # Positive acceleration
                signals.append(1)
                reasoning_parts.append("Positive price acceleration")
            elif acceleration < -0.001:  # Negative acceleration
                signals.append(-1)
                reasoning_parts.append("Negative price acceleration")

        # Moving average momentum
        if len(df) >= 20:
            sma_5 = df['close'].tail(5).mean()
            sma_10 = df['close'].tail(10).mean()
            sma_20 = df['close'].tail(20).mean()

            if sma_5 > sma_10 > sma_20:  # Bullish alignment
                signals.append(1)
                reasoning_parts.append("Bullish MA momentum")
            elif sma_5 < sma_10 < sma_20:  # Bearish alignment
                signals.append(-1)
                reasoning_parts.append("Bearish MA momentum")

        # Aggregate signals
        signal_sum = sum(signals)
        signal_count = len(signals)

        if signal_count == 0:
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif signal_sum > 0:
            signal_type = SignalType.BUY
            confidence = min(abs(signal_sum) / (signal_count * 2), 1.0)  # Normalize by max possible
        elif signal_sum < 0:
            signal_type = SignalType.SELL
            confidence = min(abs(signal_sum) / (signal_count * 2), 1.0)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.0

        # Calculate risk score based on volatility
        volatility = df['close'].pct_change().tail(20).std() * np.sqrt(252)
        risk_score = min(volatility * 3, 1.0)  # Higher risk for momentum strategy

        return TradingSignal(
            signal=signal_type,
            confidence=confidence,
            price=current_data.close,
            timestamp=current_data.timestamp,
            strategy=StrategyType.MOMENTUM,
            reasoning="; ".join(reasoning_parts) if reasoning_parts else "No momentum signals",
            risk_score=risk_score,
            position_size=0.0,
            metadata={
                'signal_sum': signal_sum,
                'signal_count': signal_count,
                'volatility': volatility,
                'momentum_strength': abs(signal_sum) / max(signal_count, 1)
            }
        )


class MeanReversionStrategy(TradingStrategy):
    """Advanced Mean Reversion strategy with statistical analysis and regime detection."""

    def __init__(self):
        super().__init__("Mean Reversion")
        self.lookback_period = 20
        self.z_score_threshold = 2.0
        self.min_reversion_probability = 0.6

    async def generate_signal(self, df: pd.DataFrame, current_data: MarketData) -> TradingSignal:
        """Generate mean reversion trading signal using statistical analysis."""
        min_samples = 20  # Need enough data for statistical analysis

        if len(df) < min_samples:
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.MEAN_REVERSION,
                reasoning=f"Insufficient data for mean reversion analysis (need {min_samples}, have {len(df)})",
                risk_score=0.8,
                position_size=0.0
            )

        # Calculate technical indicators for mean reversion
        df_with_indicators = self.indicators.calculate_all(df)
        latest = df_with_indicators.iloc[-1]

        signals = []
        reasoning_parts = []

        # 1. Bollinger Bands Mean Reversion
        bb_position = (current_data.close - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
        if bb_position < 0.2:  # Near lower band
            signals.append(1)
            reasoning_parts.append(f"BB oversold ({bb_position:.2f})")
        elif bb_position > 0.8:  # Near upper band
            signals.append(-1)
            reasoning_parts.append(f"BB overbought ({bb_position:.2f})")

        # 2. Z-Score Mean Reversion
        price_series = df['close'].tail(self.lookback_period)
        mean_price = price_series.mean()
        std_price = price_series.std()
        z_score = (current_data.close - mean_price) / std_price if std_price > 0 else 0

        if z_score < -self.z_score_threshold:  # Significantly below mean
            signals.append(1)
            reasoning_parts.append(f"Z-score oversold ({z_score:.2f})")
        elif z_score > self.z_score_threshold:  # Significantly above mean
            signals.append(-1)
            reasoning_parts.append(f"Z-score overbought ({z_score:.2f})")

        # 3. RSI Mean Reversion (contrarian)
        if latest['rsi'] < 30:
            signals.append(1)
            reasoning_parts.append(f"RSI oversold ({latest['rsi']:.1f})")
        elif latest['rsi'] > 70:
            signals.append(-1)
            reasoning_parts.append(f"RSI overbought ({latest['rsi']:.1f})")

        # 4. Price Distance from Moving Averages
        sma_distance = (current_data.close - latest['sma_20']) / latest['sma_20']
        if sma_distance < -0.05:  # 5% below SMA
            signals.append(1)
            reasoning_parts.append(f"Far below SMA20 ({sma_distance*100:.1f}%)")
        elif sma_distance > 0.05:  # 5% above SMA
            signals.append(-1)
            reasoning_parts.append(f"Far above SMA20 ({sma_distance*100:.1f}%)")

        # Calculate reversion probability based on historical patterns
        reversion_probability = self._calculate_reversion_probability(df, current_data)

        # Combine signals
        if signals:
            signal_sum = sum(signals)
            signal_strength = abs(signal_sum) / len(signals)

            # Determine final signal
            if signal_sum > 0 and reversion_probability > self.min_reversion_probability:
                signal_type = SignalType.BUY
                confidence = min(signal_strength * reversion_probability, 0.95)
            elif signal_sum < 0 and reversion_probability > self.min_reversion_probability:
                signal_type = SignalType.SELL
                confidence = min(signal_strength * reversion_probability, 0.95)
            else:
                signal_type = SignalType.HOLD
                confidence = 0.0
        else:
            signal_type = SignalType.HOLD
            confidence = 0.0

        # Calculate risk score based on volatility and market conditions
        volatility = df['close'].pct_change().std() * np.sqrt(252)
        risk_score = min(volatility * 1.5 + (1 - reversion_probability), 1.0)

        return TradingSignal(
            signal=signal_type,
            confidence=min(confidence, 0.95),
            price=current_data.close,
            timestamp=current_data.timestamp,
            strategy=StrategyType.MEAN_REVERSION,
            reasoning="; ".join(reasoning_parts) if reasoning_parts else "No mean reversion signals",
            risk_score=risk_score,
            position_size=0.0,
            metadata={
                'z_score': z_score,
                'bb_position': bb_position,
                'sma_distance': sma_distance,
                'reversion_probability': reversion_probability,
                'volatility': volatility
            }
        )

    def _calculate_reversion_probability(self, df: pd.DataFrame, current_data: MarketData) -> float:
        """Calculate probability of mean reversion based on historical patterns."""
        if len(df) < 50:
            return 0.5  # Default probability

        # Look at historical instances where price was at similar extreme levels
        price_series = df['close']
        mean_price = price_series.mean()
        std_price = price_series.std()

        if std_price == 0:
            return 0.5

        current_z_score = (current_data.close - mean_price) / std_price

        # Find similar extreme instances
        similar_instances = []
        for i in range(len(df) - 10):  # Leave room for forward looking
            z_score = (df.iloc[i]['close'] - mean_price) / std_price

            # If similar extreme level (same direction)
            if (current_z_score > 1.5 and z_score > 1.5) or (current_z_score < -1.5 and z_score < -1.5):
                # Check if price reverted in next 5-10 periods
                future_prices = df.iloc[i+1:i+11]['close']
                if len(future_prices) >= 5:
                    if current_z_score > 1.5:  # Currently overbought
                        reverted = any(price < df.iloc[i]['close'] * 0.98 for price in future_prices)
                    else:  # Currently oversold
                        reverted = any(price > df.iloc[i]['close'] * 1.02 for price in future_prices)

                    similar_instances.append(reverted)

        if len(similar_instances) >= 5:
            return sum(similar_instances) / len(similar_instances)
        else:
            # Use volatility-based probability
            volatility = price_series.pct_change().std()
            return min(0.8, 0.5 + volatility * 10)  # Higher volatility = higher reversion probability


class NeuralNetworkStrategy(TradingStrategy):
    """Advanced Neural Network strategy using deep learning for pattern recognition."""

    def __init__(self):
        super().__init__("Neural Network")
        self.model = None
        self.scaler = StandardScaler()
        self.sequence_length = 20
        self.is_trained = False
        self.min_training_samples = 100
        self.feature_columns = []

    async def generate_signal(self, df: pd.DataFrame, current_data: MarketData) -> TradingSignal:
        """Generate neural network-based trading signal."""
        min_samples = 30

        if len(df) < min_samples:
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.NEURAL_NETWORK,
                reasoning=f"Insufficient data for neural network analysis (need {min_samples}, have {len(df)})",
                risk_score=0.8,
                position_size=0.0
            )

        try:
            # Prepare features for neural network
            features = self._prepare_features(df)

            if not self.is_trained and len(df) >= self.min_training_samples:
                self._train_model(df)

            if self.is_trained and self.model is not None:
                # Make prediction
                prediction = self._make_prediction(features)

                # Convert prediction to trading signal
                if prediction > 0.6:  # Strong buy signal
                    signal_type = SignalType.BUY
                    confidence = min(prediction, 0.95)
                    reasoning = f"NN predicts upward movement ({prediction:.3f})"
                elif prediction < 0.4:  # Strong sell signal
                    signal_type = SignalType.SELL
                    confidence = min(1 - prediction, 0.95)
                    reasoning = f"NN predicts downward movement ({prediction:.3f})"
                else:  # Neutral
                    signal_type = SignalType.HOLD
                    confidence = 0.0
                    reasoning = f"NN neutral prediction ({prediction:.3f})"

                # Calculate risk score based on prediction confidence
                risk_score = 1.0 - abs(prediction - 0.5) * 2  # Higher risk for uncertain predictions

            else:
                # Fallback to simple pattern recognition
                signal_type, confidence, reasoning = self._simple_pattern_recognition(df, current_data)
                risk_score = 0.7

            return TradingSignal(
                signal=signal_type,
                confidence=confidence,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.NEURAL_NETWORK,
                reasoning=reasoning,
                risk_score=risk_score,
                position_size=0.0,
                metadata={
                    'model_trained': self.is_trained,
                    'prediction': prediction if self.is_trained else None,
                    'feature_count': len(features) if features is not None else 0
                }
            )

        except Exception as e:
            logger.error(f"Neural network strategy error: {e}")
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=current_data.close,
                timestamp=current_data.timestamp,
                strategy=StrategyType.NEURAL_NETWORK,
                reasoning=f"Neural network error: {str(e)}",
                risk_score=1.0,
                position_size=0.0
            )

    def _prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """Prepare features for neural network."""
        # Calculate technical indicators
        df_with_indicators = self.indicators.calculate_all(df)

        # Select features for neural network
        feature_columns = [
            'close', 'volume', 'sma_20', 'sma_50', 'ema_12', 'ema_26',
            'rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'atr'
        ]

        # Fill NaN values
        df_features = df_with_indicators[feature_columns].fillna(method='ffill').fillna(0)

        # Take last sequence_length samples
        if len(df_features) >= self.sequence_length:
            features = df_features.tail(self.sequence_length).values
            return features.flatten()  # Flatten for simple neural network
        else:
            return None

    def _train_model(self, df: pd.DataFrame):
        """Train the neural network model."""
        try:
            from sklearn.neural_network import MLPRegressor

            # Prepare training data
            X, y = self._prepare_training_data(df)

            if len(X) < 20:  # Need minimum samples
                return

            # Scale features
            X_scaled = self.scaler.fit_transform(X)

            # Create and train model
            self.model = MLPRegressor(
                hidden_layer_sizes=(50, 25),
                activation='relu',
                solver='adam',
                max_iter=500,
                random_state=42
            )

            self.model.fit(X_scaled, y)
            self.is_trained = True
            logger.info("Neural network model trained successfully")

        except Exception as e:
            logger.error(f"Neural network training failed: {e}")
            self.is_trained = False

    def _prepare_training_data(self, df: pd.DataFrame):
        """Prepare training data with features and targets."""
        df_with_indicators = self.indicators.calculate_all(df)

        feature_columns = [
            'close', 'volume', 'sma_20', 'sma_50', 'ema_12', 'ema_26',
            'rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'atr'
        ]

        df_features = df_with_indicators[feature_columns].fillna(method='ffill').fillna(0)

        X = []
        y = []

        # Create sequences and targets
        for i in range(self.sequence_length, len(df_features) - 5):
            # Features: sequence of technical indicators
            features = df_features.iloc[i-self.sequence_length:i].values.flatten()

            # Target: future price direction (1 for up, 0 for down)
            current_price = df.iloc[i]['close']
            future_price = df.iloc[i+5]['close']  # Look 5 periods ahead
            target = 1.0 if future_price > current_price else 0.0

            X.append(features)
            y.append(target)

        return np.array(X), np.array(y)

    def _make_prediction(self, features):
        """Make prediction using trained model."""
        if features is None or self.model is None:
            return 0.5  # Neutral

        try:
            features_scaled = self.scaler.transform([features])
            prediction = self.model.predict(features_scaled)[0]
            return np.clip(prediction, 0.0, 1.0)
        except Exception as e:
            logger.error(f"Neural network prediction error: {e}")
            return 0.5

    def _simple_pattern_recognition(self, df: pd.DataFrame, current_data: MarketData):
        """Simple pattern recognition fallback."""
        # Look for simple patterns when neural network is not available
        if len(df) < 10:
            return SignalType.HOLD, 0.0, "Insufficient data for pattern recognition"

        recent_prices = df['close'].tail(10).values

        # Check for ascending/descending patterns
        ascending = sum(recent_prices[i] > recent_prices[i-1] for i in range(1, len(recent_prices)))
        descending = sum(recent_prices[i] < recent_prices[i-1] for i in range(1, len(recent_prices)))

        if ascending >= 7:  # Strong upward pattern
            return SignalType.BUY, 0.6, f"Ascending pattern ({ascending}/9)"
        elif descending >= 7:  # Strong downward pattern
            return SignalType.SELL, 0.6, f"Descending pattern ({descending}/9)"
        else:
            return SignalType.HOLD, 0.0, "No clear pattern detected"


class MarketRegime(Enum):
    """Market regime types."""
    TRENDING_UP = "TRENDING_UP"
    TRENDING_DOWN = "TRENDING_DOWN"
    RANGING = "RANGING"
    HIGH_VOLATILITY = "HIGH_VOLATILITY"
    LOW_VOLATILITY = "LOW_VOLATILITY"


class MarketRegimeDetector:
    """Detect current market regime to adapt trading strategies."""

    def __init__(self):
        self.current_regime = MarketRegime.RANGING
        self.regime_confidence = 0.0

    def detect_regime(self, df: pd.DataFrame) -> Tuple[MarketRegime, float]:
        """Detect current market regime."""
        if len(df) < 50:
            return MarketRegime.RANGING, 0.0

        recent_data = df.tail(50)

        # Calculate trend strength
        trend_strength = self._calculate_trend_strength(recent_data)

        # Calculate volatility
        volatility = recent_data['close'].pct_change().std() * np.sqrt(252)

        # Calculate range-bound behavior
        range_score = self._calculate_range_score(recent_data)

        # Determine regime
        if volatility > 0.4:  # High volatility threshold
            regime = MarketRegime.HIGH_VOLATILITY
            confidence = min(volatility / 0.4, 1.0)
        elif volatility < 0.1:  # Low volatility threshold
            regime = MarketRegime.LOW_VOLATILITY
            confidence = min((0.1 - volatility) / 0.1, 1.0)
        elif trend_strength > 0.6:  # Strong uptrend
            regime = MarketRegime.TRENDING_UP
            confidence = trend_strength
        elif trend_strength < -0.6:  # Strong downtrend
            regime = MarketRegime.TRENDING_DOWN
            confidence = abs(trend_strength)
        elif range_score > 0.7:  # Range-bound market
            regime = MarketRegime.RANGING
            confidence = range_score
        else:
            regime = MarketRegime.RANGING
            confidence = 0.5

        self.current_regime = regime
        self.regime_confidence = confidence

        return regime, confidence

    def _calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """Calculate trend strength (-1 to 1)."""
        # Linear regression slope
        x = np.arange(len(df))
        y = df['close'].values
        slope, _, r_value, _, _ = stats.linregress(x, y)

        # Normalize slope by price level
        normalized_slope = slope / df['close'].mean() * len(df)

        # Weight by R-squared (how linear the trend is)
        trend_strength = normalized_slope * (r_value ** 2)

        return np.clip(trend_strength, -1, 1)

    def _calculate_range_score(self, df: pd.DataFrame) -> float:
        """Calculate how range-bound the market is (0 to 1)."""
        # Calculate support and resistance levels
        highs = df['high'].rolling(10).max()
        lows = df['low'].rolling(10).min()

        # Count touches of support/resistance
        resistance_touches = (df['high'] >= highs * 0.995).sum()
        support_touches = (df['low'] <= lows * 1.005).sum()

        # Calculate price efficiency (how much price moves vs distance traveled)
        total_distance = abs(df['close'].iloc[-1] - df['close'].iloc[0])
        path_length = abs(df['close'].diff()).sum()

        efficiency = total_distance / path_length if path_length > 0 else 0

        # Range score combines support/resistance activity with low efficiency
        range_score = (resistance_touches + support_touches) / len(df) * (1 - efficiency)

        return np.clip(range_score, 0, 1)


class SignalFilter:
    """Filter and validate trading signals to reduce noise."""

    def __init__(self):
        self.last_signal_time = None
        self.min_signal_interval = timedelta(seconds=5)  # Reduced to 5 seconds for maximum activity
        self.signal_history = []

    def filter_signal(self, signal: TradingSignal, market_regime: MarketRegime,
                     regime_confidence: float) -> TradingSignal:
        """Filter and potentially modify trading signal."""

        # Time-based filtering - DISABLED for demo to see raw signals
        # if self._is_too_frequent(signal):
        #     return self._create_hold_signal(signal, "Signal too frequent")

        # Confidence-based filtering - Made very aggressive for demo
        if signal.confidence < 0.01:  # Reduced to 0.01 for maximum activity
            return self._create_hold_signal(signal, "Low confidence signal")

        # Regime-based filtering
        adjusted_signal = self._adjust_for_regime(signal, market_regime, regime_confidence)

        # Consistency filtering
        if not self._is_consistent_with_history(adjusted_signal):
            # Create a new signal with reduced confidence for inconsistency
            adjusted_signal = TradingSignal(
                signal=adjusted_signal.signal,
                confidence=adjusted_signal.confidence * 0.7,
                price=adjusted_signal.price,
                timestamp=adjusted_signal.timestamp,
                strategy=adjusted_signal.strategy,
                reasoning=adjusted_signal.reasoning + " (reduced for inconsistency)",
                risk_score=adjusted_signal.risk_score,
                position_size=adjusted_signal.position_size,
                metadata=adjusted_signal.metadata
            )

        # Update history
        self.signal_history.append(adjusted_signal)
        if len(self.signal_history) > 10:
            self.signal_history.pop(0)

        self.last_signal_time = signal.timestamp

        return adjusted_signal

    def _is_too_frequent(self, signal: TradingSignal) -> bool:
        """Check if signal is too frequent."""
        if self.last_signal_time is None:
            return False
        return signal.timestamp - self.last_signal_time < self.min_signal_interval

    def _adjust_for_regime(self, signal: TradingSignal, regime: MarketRegime,
                          confidence: float) -> TradingSignal:
        """Adjust signal based on market regime."""
        # Create a new signal object instead of modifying the original
        adjusted_confidence = signal.confidence
        adjusted_risk_score = signal.risk_score
        adjusted_reasoning = signal.reasoning

        if regime == MarketRegime.HIGH_VOLATILITY:
            # Reduce position sizes in high volatility
            adjusted_risk_score = min(signal.risk_score * 1.5, 1.0)
            adjusted_reasoning += " (high vol adjustment)"

        elif regime == MarketRegime.RANGING:
            # Favor mean reversion in ranging markets
            if signal.signal != SignalType.HOLD:
                adjusted_confidence *= 0.8  # Reduce trend-following confidence
                adjusted_reasoning += " (ranging market)"

        elif regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]:
            # Boost trend-following signals in trending markets
            if ((regime == MarketRegime.TRENDING_UP and signal.signal == SignalType.BUY) or
                (regime == MarketRegime.TRENDING_DOWN and signal.signal == SignalType.SELL)):
                adjusted_confidence = min(signal.confidence * 1.2, 1.0)
                adjusted_reasoning += " (trend boost)"

        # Return a new TradingSignal object with adjusted values
        return TradingSignal(
            signal=signal.signal,
            confidence=adjusted_confidence,
            price=signal.price,
            timestamp=signal.timestamp,
            strategy=signal.strategy,
            reasoning=adjusted_reasoning,
            risk_score=adjusted_risk_score,
            position_size=signal.position_size,
            metadata=signal.metadata
        )

    def _is_consistent_with_history(self, signal: TradingSignal) -> bool:
        """Check if signal is consistent with recent history."""
        if len(self.signal_history) < 3:
            return True

        recent_signals = [s.signal for s in self.signal_history[-3:]]

        # Check for excessive flip-flopping
        if len(set(recent_signals)) == 3:  # All different signals
            return False

        return True

    def _create_hold_signal(self, original_signal: TradingSignal, reason: str) -> TradingSignal:
        """Create a HOLD signal with original metadata."""
        return TradingSignal(
            signal=SignalType.HOLD,
            confidence=0.0,
            price=original_signal.price,
            timestamp=original_signal.timestamp,
            strategy=original_signal.strategy,
            reasoning=reason,
            risk_score=original_signal.risk_score,
            position_size=0.0,
            metadata=original_signal.metadata
        )


class PerformanceTracker:
    """Track strategy performance and adjust weights accordingly."""

    def __init__(self):
        self.strategy_performance = {
            StrategyType.TECHNICAL: {'wins': 0, 'losses': 0, 'total_return': 0.0},
            StrategyType.ML_ENSEMBLE: {'wins': 0, 'losses': 0, 'total_return': 0.0},
            StrategyType.MOMENTUM: {'wins': 0, 'losses': 0, 'total_return': 0.0},
            StrategyType.MEAN_REVERSION: {'wins': 0, 'losses': 0, 'total_return': 0.0},
            StrategyType.NEURAL_NETWORK: {'wins': 0, 'losses': 0, 'total_return': 0.0}
        }
        self.trade_outcomes = []
        self.learning_rate = 0.1

    def record_trade_outcome(self, signal: TradingSignal, outcome_return: float):
        """Record the outcome of a trade for learning."""
        strategy = signal.strategy

        # Update performance metrics
        if outcome_return > 0:
            self.strategy_performance[strategy]['wins'] += 1
        else:
            self.strategy_performance[strategy]['losses'] += 1

        self.strategy_performance[strategy]['total_return'] += outcome_return

        # Store trade outcome for analysis
        self.trade_outcomes.append({
            'strategy': strategy,
            'signal': signal.signal,
            'confidence': signal.confidence,
            'return': outcome_return,
            'timestamp': signal.timestamp
        })

        # Keep only recent outcomes
        if len(self.trade_outcomes) > 100:
            self.trade_outcomes.pop(0)

    def calculate_strategy_weights(self) -> Dict[StrategyType, float]:
        """Calculate optimal strategy weights based on performance."""
        weights = {}
        total_score = 0.0

        for strategy, perf in self.strategy_performance.items():
            total_trades = perf['wins'] + perf['losses']

            if total_trades == 0:
                score = 0.5  # Default score for untested strategies
            else:
                # Calculate win rate
                win_rate = perf['wins'] / total_trades

                # Calculate average return
                avg_return = perf['total_return'] / total_trades

                # Combine win rate and return (with bias toward positive returns)
                score = (win_rate * 0.6) + (max(avg_return, 0) * 0.4)

                # Boost score for strategies with more data
                confidence_boost = min(total_trades / 20, 1.0) * 0.1
                score += confidence_boost

            weights[strategy] = max(score, 0.1)  # Minimum weight of 0.1
            total_score += weights[strategy]

        # Normalize weights
        if total_score > 0:
            for strategy in weights:
                weights[strategy] /= total_score
        else:
            # Equal weights if no performance data
            num_strategies = len(weights)
            for strategy in weights:
                weights[strategy] = 1.0 / num_strategies

        return weights

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all strategies."""
        summary = {}

        for strategy, perf in self.strategy_performance.items():
            total_trades = perf['wins'] + perf['losses']
            win_rate = perf['wins'] / total_trades if total_trades > 0 else 0
            avg_return = perf['total_return'] / total_trades if total_trades > 0 else 0

            summary[strategy.value] = {
                'total_trades': total_trades,
                'wins': perf['wins'],
                'losses': perf['losses'],
                'win_rate': win_rate,
                'avg_return': avg_return,
                'total_return': perf['total_return']
            }

        return summary

    def reset_for_new_pair(self):
        """Reset performance tracking for a new trading pair."""
        logger.info("🔄 Resetting performance tracker for new trading pair")

        # Reset all strategy performance metrics
        for strategy in self.strategy_performance:
            self.strategy_performance[strategy] = {
                'wins': 0,
                'losses': 0,
                'total_return': 0.0
            }

        # Clear trade outcomes
        self.trade_outcomes.clear()

        logger.info("✅ Performance tracker reset completed")


class AutoTradzAI:
    """Main AI trading engine with multi-pair support."""

    def __init__(self):
        self.api = AlpacaCryptoAPI()

        # Enhanced AI Decision Logging
        self.ai_logger = AILogger()
        self.current_market_analysis = {}
        self.strategy_performance_history = {}

        # WebSocket integration for real-time data
        self.websocket_manager = None
        self.websocket_enabled = False
        self.websocket_thread = None

        # Multi-pair support
        self.crypto_pair_manager = crypto_pair_manager
        self.current_pair = crypto_pair_manager.get_current_pair()
        self.pair_specific_data = {}  # Store data per trading pair
        self.pair_specific_models = {}  # Store ML models per pair

        self.strategies = {
            StrategyType.TECHNICAL: TechnicalStrategy(),
            StrategyType.ML_ENSEMBLE: MLStrategy(trading_ai=self),
            StrategyType.MOMENTUM: MomentumStrategy(),
            StrategyType.MEAN_REVERSION: MeanReversionStrategy(),
            StrategyType.NEURAL_NETWORK: NeuralNetworkStrategy()
        }

        # Initialize multi-asset manager
        self.multi_asset_manager = MultiAssetManager()
        self.current_trading_pair = "XRP-USD"  # Default pair
        self.multi_asset_data = {}  # Store data for all pairs

        # Initialize notification system
        self.notification_manager = None
        self._init_notification_system()
        self.risk_manager = InstitutionalRiskManager(
            max_position_size=settings.max_position_size,
            risk_per_trade=settings.risk_per_trade
        )
        self.portfolio = Portfolio(cash=settings.initial_capital)
        self.market_data: List[MarketData] = []
        self.signals_history: List[TradingSignal] = []
        self.recent_signals: List[TradingSignal] = []  # Add missing attribute for pair switching
        self.is_running = False
        self.paper_trading = True
        self.data_source = "live"  # "mock", "live", or "websocket" - default to live data

        # Enhanced AI components
        self.regime_detector = MarketRegimeDetector()
        self.signal_filter = SignalFilter()
        self.strategy_weights = {
            StrategyType.TECHNICAL: 0.25,
            StrategyType.ML_ENSEMBLE: 0.25,
            StrategyType.MOMENTUM: 0.20,
            StrategyType.MEAN_REVERSION: 0.20,
            StrategyType.NEURAL_NETWORK: 0.10
        }
        self.performance_tracker = PerformanceTracker()
        self.learning_enabled = True

    def _init_notification_system(self):
        """Initialize notification system with default configuration."""
        try:
            from .notification_manager import NotificationManager
            from .notification_service import NotificationConfig

            # Default notification configuration
            config = NotificationConfig(
                email_enabled=False,  # Disabled by default
                sms_enabled=False,
                push_enabled=False,
                in_app_enabled=True,  # Only in-app notifications enabled by default
                webhook_enabled=False,
                min_signal_confidence=0.7,
                min_risk_score=0.8,
                min_trade_size=100.0,
                performance_milestone_threshold=5.0
            )

            self.notification_manager = NotificationManager(config)
            logger.info("Notification system initialized")

        except Exception as e:
            logger.error(f"Failed to initialize notification system: {e}")
            self.notification_manager = None

    def _init_websocket(self, use_simulation: bool = False):
        """Initialize WebSocket connection for real-time data."""
        try:
            if not use_simulation and (not settings.alpaca_api_key or not settings.alpaca_api_secret):
                logger.warning("Alpaca API credentials not found, using simulation")
                use_simulation = True

            self.websocket_manager = AlpacaCryptoStreamManager(
                api_key=settings.alpaca_api_key,
                api_secret=settings.alpaca_api_secret,
                use_simulation=use_simulation
            )

            # Add handlers for real-time data
            self.websocket_manager.add_bar_handler(self._handle_websocket_bar)
            self.websocket_manager.add_quote_handler(self._handle_websocket_quote)
            self.websocket_manager.add_trade_handler(self._handle_websocket_trade)

            mode = "simulated" if use_simulation else "real"
            logger.info(f"WebSocket manager initialized ({mode})")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize WebSocket: {e}")
            return False

    async def _handle_websocket_bar(self, bar_data: dict):
        """Handle real-time bar/candlestick data from WebSocket."""
        try:
            # Convert WebSocket bar data to MarketData format
            market_data = MarketData(
                timestamp=bar_data['timestamp'],
                open=bar_data['open'],
                high=bar_data['high'],
                low=bar_data['low'],
                close=bar_data['close'],
                volume=bar_data['volume'],
                product_id=bar_data['symbol'].replace('USD', '-USD')  # Convert XRPUSD to XRP-USD
            )

            # Add to market data
            self.market_data.append(market_data)

            # Keep only last 1000 data points
            if len(self.market_data) > 1000:
                self.market_data = self.market_data[-1000:]

            # Update portfolio with current prices
            await self._update_portfolio_value(market_data)

            logger.info("WebSocket bar data received",
                       symbol=bar_data['symbol'],
                       price=bar_data['close'],
                       volume=bar_data['volume'])

        except Exception as e:
            logger.error("Error handling WebSocket bar data", error=str(e))

    async def _handle_websocket_quote(self, quote_data: dict):
        """Handle real-time quote data from WebSocket."""
        try:
            # Store latest quote for spread analysis
            symbol = quote_data['symbol']
            if not hasattr(self, 'latest_quotes'):
                self.latest_quotes = {}

            self.latest_quotes[symbol] = {
                'bid_price': quote_data['bid_price'],
                'ask_price': quote_data['ask_price'],
                'bid_size': quote_data['bid_size'],
                'ask_size': quote_data['ask_size'],
                'timestamp': quote_data['timestamp']
            }

            logger.debug("WebSocket quote data received",
                        symbol=symbol,
                        bid=quote_data['bid_price'],
                        ask=quote_data['ask_price'])

        except Exception as e:
            logger.error("Error handling WebSocket quote data", error=str(e))

    async def _handle_websocket_trade(self, trade_data: dict):
        """Handle real-time trade data from WebSocket."""
        try:
            # Store latest trade for market analysis
            symbol = trade_data['symbol']
            if not hasattr(self, 'latest_trades'):
                self.latest_trades = {}

            self.latest_trades[symbol] = {
                'price': trade_data['price'],
                'size': trade_data['size'],
                'timestamp': trade_data['timestamp']
            }

            logger.debug("WebSocket trade data received",
                        symbol=symbol,
                        price=trade_data['price'],
                        size=trade_data['size'])

        except Exception as e:
            logger.error("Error handling WebSocket trade data", error=str(e))

    def enable_websocket(self, use_simulation: bool = False):
        """Enable WebSocket real-time data streaming with simulation fallback."""
        try:
            if not self._init_websocket(use_simulation):
                logger.error("Failed to initialize WebSocket")
                return False

            # Subscribe to current trading pair
            symbols = [self.current_trading_pair]  # Keep XRP-USD format

            # Start WebSocket in background thread
            self.websocket_thread = self.websocket_manager.start_in_thread()

            # Subscribe to symbols after a short delay
            import threading
            def delayed_subscribe():
                import time
                time.sleep(2)  # Wait for connection
                asyncio.run(self.websocket_manager.subscribe_to_symbols(symbols, "1Min"))

            threading.Thread(target=delayed_subscribe, daemon=True).start()

            self.websocket_enabled = True
            self.data_source = "websocket"

            mode = "simulated" if use_simulation else "real"
            logger.info(f"WebSocket enabled for {mode} real-time data", symbols=symbols)
            return True

        except Exception as e:
            logger.error("Failed to enable WebSocket", error=str(e))
            # Try simulation as fallback
            if not use_simulation:
                logger.info("Trying simulation fallback...")
                return self.enable_websocket(use_simulation=True)
            return False

    async def disable_websocket(self):
        """Disable WebSocket real-time data streaming."""
        try:
            self.websocket_enabled = False

            if self.websocket_manager:
                await self.websocket_manager.stop()
                self.websocket_manager = None

            # Switch back to live API data
            self.data_source = "live"

            logger.info("WebSocket disabled, switched to live API data")

        except Exception as e:
            logger.error("Error disabling WebSocket", error=str(e))

    async def start(self):
        """Start the trading engine with WebSocket support."""
        self.is_running = True
        logger.info("AutoTradz AI starting",
                   paper_trading=self.paper_trading,
                   initial_capital=self.portfolio.cash,
                   data_source=self.data_source)

        # Test API connection and initialize WebSocket if using live data
        if self.data_source == "live":
            try:
                if self.api.test_connection():
                    logger.info("Alpaca API connection successful")

                    # Try to enable WebSocket for real-time data
                    if self.enable_websocket():
                        logger.info("WebSocket enabled for real-time candlestick data")
                    else:
                        logger.warning("WebSocket failed, using REST API polling")
                else:
                    logger.warning("Alpaca API connection failed - using emergency mock data")
                    # Don't raise exception, allow system to continue with mock data
                    self.data_source = "mock"
            except Exception as e:
                logger.warning("API connection error - using emergency mock data", error_msg=str(e))
                # Don't raise exception, allow system to continue with mock data
                self.data_source = "mock"

        # Start main trading loop
        await self._trading_loop()

    async def stop(self):
        """Stop the trading engine and cleanup WebSocket."""
        self.is_running = False

        # Cleanup WebSocket connection
        if self.websocket_enabled:
            await self.disable_websocket()

        logger.info("AutoTradz AI stopped")

    def switch_to_live_data(self):
        """🌐 ENHANCED SWITCH TO LIVE ALPACA DATA."""
        try:
            print("🔄 Attempting to switch to live Alpaca data...")

            # Reinitialize API with current settings to pick up any credential changes
            self.api = AlpacaCryptoAPI()

            print(f"🔑 API initialized with key: {settings.alpaca_api_key[:20]}..." if settings.alpaca_api_key else "❌ No API key found")

            # Test the connection
            connection_test = self.api.test_connection()
            print(f"🧪 Connection test result: {connection_test}")

            if connection_test:
                self.data_source = "live"
                print("✅ Successfully switched to live Alpaca data")
                logger.info("Switched to live Alpaca data")
                return True
            else:
                print("❌ Failed to connect to Alpaca API - staying on mock data")
                logger.error("Failed to connect to Alpaca API")
                self.data_source = "mock"
                return False

        except Exception as e:
            print(f"❌ Error switching to live data: {e}")
            logger.error(f"Error switching to live data: {e}")
            raise Exception(f"Alpaca API connection required: {str(e)}")

    # MOCK DATA METHODS REMOVED - ALPACA ONLY IMPLEMENTATION

    def update_api_credentials(self):
        """Update API credentials by reinitializing the API instance."""
        try:
            print("🔄 Updating API credentials...")

            # Reinitialize API with current settings
            self.api = AlpacaCryptoAPI()

            print(f"🔑 API reinitialized with key: {settings.alpaca_api_key[:20]}..." if settings.alpaca_api_key else "❌ No API key found")

            # Test the connection
            connection_test = self.api.test_connection()
            print(f"🧪 Connection test result: {connection_test}")

            if connection_test:
                print("✅ API credentials updated successfully")
                logger.info("API credentials updated successfully")
                return True
            else:
                print("❌ Failed to connect with new credentials")
                logger.error("Failed to connect with new API credentials")
                return False

        except Exception as e:
            print(f"❌ Error updating API credentials: {e}")
            logger.error(f"Error updating API credentials: {e}")
            return False

    async def _fetch_live_data(self):
        """🌐 ENHANCED LIVE DATA FETCHING from Alpaca API."""
        try:
            if not self.api:
                return False

            # Get current trading pair
            current_pair = self.crypto_pair_manager.get_current_pair()
            symbol = current_pair.symbol  # e.g., "XRP/USD"

            # Use the enhanced API method for live data
            live_data = self.api.get_live_data_for_symbol(symbol)

            if live_data and 'close' in live_data:
                # Create market data point from live Alpaca data
                new_data = MarketData(
                    timestamp=live_data['timestamp'],
                    open=live_data['open'],
                    high=live_data['high'],
                    low=live_data['low'],
                    close=live_data['close'],
                    volume=live_data['volume'],
                    product_id=symbol
                )

                self.market_data.append(new_data)

                # Only log every 20th update to reduce spam
                if len(self.market_data) % 20 == 0:
                    print(f"📡 Live data: {symbol} @ ${live_data['close']:.4f}")

                # Update portfolio with current prices
                await self._update_portfolio_value(new_data)
                return True
            else:
                return False

        except Exception as e:
            # Reduced error logging
            if len(self.market_data) % 50 == 0:  # Only log every 50th error
                logger.error("Error fetching live data", error_msg=str(e))
            # Emergency fallback: Always generate some data to keep the system running
            try:
                if len(self.market_data) > 0:
                    last_price = self.market_data[-1].close
                else:
                    last_price = 2.15  # Default XRP price

                current_time = datetime.now()
                market_data = MarketData(
                    timestamp=current_time,
                    open=last_price,
                    high=last_price * 1.001,
                    low=last_price * 0.999,
                    close=last_price,
                    volume=0,
                    product_id=settings.trading_pair
                )

                self.market_data.append(market_data)
                logger.info("Emergency fallback data", price=last_price, total_samples=len(self.market_data))

                await self._update_portfolio_value(market_data)
                return True
            except Exception as fallback_error:
                logger.error("Emergency fallback failed", error=str(fallback_error))

        return False

    async def _add_mock_data_emergency(self):
        """Emergency method to add mock data to prevent infinite retry loops."""
        try:
            current_pair = self.crypto_pair_manager.get_current_pair()
            symbol = current_pair.symbol

            # Create realistic mock prices for different pairs
            mock_prices = {
                "XRP/USD": 2.13,
                "BTC/USD": 97000.0,
                "ETH/USD": 3300.0,
                "ADA/USD": 0.89,
                "SOL/USD": 185.0,
                "DOGE/USD": 0.32,
                "LTC/USD": 105.0,
                "BCH/USD": 450.0,
                "LINK/USD": 22.5,
                "UNI/USD": 12.8,
                "DOT/USD": 7.2,
                "AVAX/USD": 38.5,
                "MATIC/USD": 1.05
            }

            base_price = mock_prices.get(symbol, 100.0)
            current_time = datetime.now()

            # Add small random variation
            import random
            variation = random.uniform(-0.02, 0.02)  # ±2% variation
            price = base_price * (1 + variation)

            mock_data = MarketData(
                timestamp=current_time,
                open=price * 0.999,
                high=price * 1.001,
                low=price * 0.998,
                close=price,
                volume=random.randint(10000, 100000),
                product_id=symbol
            )

            self.market_data.append(mock_data)
            print(f"🔧 Emergency mock data added: {symbol} @ ${price:.4f}")

            # Update portfolio with mock prices
            await self._update_portfolio_value(mock_data)
            return True

        except Exception as e:
            logger.error("Error adding emergency mock data", error_msg=str(e))
            return False

    async def _generate_fallback_data(self):
        """Generate fallback data when live data fetch fails."""
        try:
            import random  # Import at the top of the method

            if len(self.market_data) > 0:
                last_price = self.market_data[-1].close
                # Add small random variation
                price_change = random.uniform(-0.01, 0.01)  # ±1% variation
                current_price = last_price * (1 + price_change)
            else:
                current_price = 2.15  # Default XRP price

            current_time = datetime.now()
            market_data = MarketData(
                timestamp=current_time,
                open=current_price,
                high=current_price * 1.002,
                low=current_price * 0.998,
                close=current_price,
                volume=random.randint(1000000, 5000000),  # Random volume
                product_id=settings.trading_pair
            )

            self.market_data.append(market_data)
            logger.info("Fallback data generated",
                       price=current_price,
                       total_samples=len(self.market_data),
                       timestamp=current_time.isoformat())

            # Keep only last 1000 data points
            if len(self.market_data) > 1000:
                self.market_data = self.market_data[-1000:]

            # Update portfolio with current prices
            await self._update_portfolio_value(market_data)

            return True

        except Exception as e:
            logger.error("Fallback data generation failed", error=str(e))
            return False

    async def _fetch_historical_data(self):
        """Fetch historical data to initialize the system."""
        try:
            if self.data_source == "live":
                # Fetch last 200 hours of data
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=200)

                historical_data = self.api.get_historical_data(
                    settings.trading_pair, start_time, end_time, "1Hour"
                )

                if historical_data:
                    for candle in historical_data:
                        market_data = MarketData(
                            timestamp=candle["timestamp"],
                            open=candle["open"],
                            high=candle["high"],
                            low=candle["low"],
                            close=candle["close"],
                            volume=candle["volume"],
                            product_id=settings.trading_pair
                        )
                        self.market_data.append(market_data)

                    logger.info("Historical data loaded", count=len(historical_data))
                    return True

        except Exception as e:
            logger.error("Error fetching historical data", error=str(e))

        return False

    async def _update_portfolio_value(self, current_data: MarketData):
        """Update portfolio value with current market prices."""
        total_value = self.portfolio.cash
        unrealized_pnl = 0.0

        for position in self.portfolio.positions.values():
            if position.product_id == current_data.product_id:
                position.current_price = current_data.close
                position.unrealized_pnl = (current_data.close - position.entry_price) * position.size
                unrealized_pnl += position.unrealized_pnl
                total_value += position.size * current_data.close

        self.portfolio.total_value = total_value
        self.portfolio.unrealized_pnl = unrealized_pnl

    async def _trading_loop(self):
        """Main trading decision loop."""
        # Initialize with historical data if using live data
        if self.data_source == "live" and len(self.market_data) == 0:
            await self._fetch_historical_data()

        while self.is_running:
            try:
                # Fetch new data based on source
                if self.data_source == "websocket":
                    # WebSocket data is handled by event handlers, just wait
                    await asyncio.sleep(1)
                elif self.data_source == "live":
                    success = await self._fetch_live_data()
                    if not success:
                        logger.warning("Live data fetch failed, generating fallback data")
                        # Force generate fallback data if live fetch fails
                        await self._generate_fallback_data()
                else:
                    # Generate mock data (existing logic will be handled by market_data_broadcaster)
                    pass

                # Reduced requirement for paper trading to enable faster learning
                min_data_points = 5 if self.paper_trading else 200
                if len(self.market_data) < min_data_points:
                    logger.warning("Insufficient data, force generating fallback data",
                                 current=len(self.market_data),
                                 required=min_data_points)
                    # Force generate data to reach minimum requirement
                    for i in range(min_data_points - len(self.market_data)):
                        await self._generate_fallback_data()
                        await asyncio.sleep(0.1)  # Small delay between generations
                    logger.info("Generated sufficient fallback data",
                               total_samples=len(self.market_data))

                # Get historical data for analysis
                df = self._market_data_to_dataframe()
                current_data = self.market_data[-1]

                # 🧠 START AI DECISION CYCLE - Log everything the AI is thinking
                decision_log = self.ai_logger.start_decision_cycle({
                    'current_price': current_data.close,
                    'volume': current_data.volume,
                    'timestamp': current_data.timestamp.isoformat(),
                    'pair': self.current_pair.symbol,
                    'data_points': len(self.market_data)
                })

                # Detect market regime
                market_regime, regime_confidence = self.regime_detector.detect_regime(df)

                # 🧠 AI THOUGHT: Market regime analysis
                self.ai_logger.add_thought(
                    component="RegimeDetector",
                    thought_type="market_analysis",
                    content=f"Market regime detected as {market_regime.value} with {regime_confidence:.2f} confidence. "
                           f"This suggests {'trending' if 'TREND' in market_regime.value else 'ranging'} market conditions.",
                    confidence=regime_confidence,
                    data={
                        'regime': market_regime.value,
                        'regime_confidence': regime_confidence,
                        'market_type': 'trending' if 'TREND' in market_regime.value else 'ranging'
                    }
                )

                # Update risk parameters based on market conditions
                market_volatility = df['close'].pct_change().std() * np.sqrt(252)
                recent_returns = [self._simulate_trade_outcome(s) for s in self.signals_history[-10:]]
                self.risk_manager.update_risk_parameters(market_volatility, recent_returns, self.portfolio)

                # 🧠 AI THOUGHT: Risk assessment
                self.ai_logger.add_thought(
                    component="RiskManager",
                    thought_type="risk_analysis",
                    content=f"Market volatility is {market_volatility:.4f} (annualized). "
                           f"Recent performance shows {len([r for r in recent_returns if r > 0])}/{len(recent_returns)} profitable trades. "
                           f"Risk parameters updated accordingly.",
                    confidence=0.9,
                    data={
                        'market_volatility': market_volatility,
                        'recent_win_rate': len([r for r in recent_returns if r > 0]) / len(recent_returns) if recent_returns else 0,
                        'portfolio_value': self.portfolio.total_value,
                        'available_cash': self.portfolio.cash
                    }
                )

                # Generate signals from all strategies
                signals = []
                strategy_thoughts = []

                for strategy_type, strategy in self.strategies.items():
                    signal = await strategy.generate_signal(df, current_data)
                    signals.append(signal)

                    # 🧠 AI THOUGHT: Individual strategy analysis
                    strategy_thought = f"Strategy {strategy_type.value}: {signal.signal.value} signal with {signal.confidence:.2f} confidence. "
                    if hasattr(signal, 'reasoning') and signal.reasoning:
                        strategy_thought += f"Reasoning: {signal.reasoning}"

                    self.ai_logger.add_thought(
                        component=f"Strategy_{strategy_type.value}",
                        thought_type="signal_generation",
                        content=strategy_thought,
                        confidence=signal.confidence,
                        data={
                            'signal': signal.signal.value,
                            'confidence': signal.confidence,
                            'price': signal.price,
                            'risk_score': signal.risk_score,
                            'metadata': signal.metadata if hasattr(signal, 'metadata') else {}
                        }
                    )
                    strategy_thoughts.append(strategy_thought)

                # 🧠 AI THOUGHT: Strategy consensus analysis
                buy_signals = len([s for s in signals if s.signal == SignalType.BUY])
                sell_signals = len([s for s in signals if s.signal == SignalType.SELL])
                hold_signals = len([s for s in signals if s.signal == SignalType.HOLD])

                consensus_analysis = f"Strategy consensus: {buy_signals} BUY, {sell_signals} SELL, {hold_signals} HOLD signals. "
                if buy_signals > sell_signals and buy_signals > hold_signals:
                    consensus_analysis += "Bullish consensus emerging."
                elif sell_signals > buy_signals and sell_signals > hold_signals:
                    consensus_analysis += "Bearish consensus emerging."
                else:
                    consensus_analysis += "No clear consensus, market indecision."

                self.ai_logger.add_thought(
                    component="StrategyEnsemble",
                    thought_type="consensus_analysis",
                    content=consensus_analysis,
                    confidence=max([s.confidence for s in signals]) if signals else 0.0,
                    data={
                        'buy_count': buy_signals,
                        'sell_count': sell_signals,
                        'hold_count': hold_signals,
                        'total_strategies': len(signals),
                        'avg_confidence': sum([s.confidence for s in signals]) / len(signals) if signals else 0.0
                    }
                )

                # Combine signals with enhanced logic
                combined_signal = self._combine_signals_enhanced(signals, market_regime, regime_confidence)

                # Filter signal to reduce noise
                final_signal = self.signal_filter.filter_signal(combined_signal, market_regime, regime_confidence)

                # 🧠 AI THOUGHT: Final decision reasoning
                decision_reasoning = f"Final decision: {final_signal.signal.value} with {final_signal.confidence:.2f} confidence. "
                decision_reasoning += f"Combined from {len(signals)} strategies, filtered for market regime {market_regime.value}. "
                if hasattr(final_signal, 'reasoning') and final_signal.reasoning:
                    decision_reasoning += f"Final reasoning: {final_signal.reasoning}"

                self.ai_logger.add_thought(
                    component="DecisionEngine",
                    thought_type="final_decision",
                    content=decision_reasoning,
                    confidence=final_signal.confidence,
                    data={
                        'final_signal': final_signal.signal.value,
                        'final_confidence': final_signal.confidence,
                        'final_price': final_signal.price,
                        'risk_score': final_signal.risk_score,
                        'position_size': getattr(final_signal, 'position_size', 0.0)
                    }
                )

                if final_signal.signal != SignalType.HOLD:
                    # Validate trade with risk manager
                    trade_valid = self.risk_manager.validate_trade(self.portfolio, final_signal)

                    # 🧠 AI THOUGHT: Trade validation
                    validation_thought = f"Trade validation: {'APPROVED' if trade_valid else 'REJECTED'}. "
                    if trade_valid:
                        validation_thought += "Risk parameters satisfied, proceeding with position sizing."
                    else:
                        validation_thought += "Risk limits exceeded or portfolio constraints violated."

                    self.ai_logger.add_thought(
                        component="RiskManager",
                        thought_type="trade_validation",
                        content=validation_thought,
                        confidence=0.95 if trade_valid else 0.05,
                        data={
                            'trade_approved': trade_valid,
                            'portfolio_value': self.portfolio.total_value,
                            'available_cash': self.portfolio.cash,
                            'current_positions': len(self.portfolio.positions),
                            'signal_risk_score': final_signal.risk_score
                        }
                    )

                    if trade_valid:
                        # Calculate position size
                        position_size = self.risk_manager.calculate_position_size(
                            self.portfolio, final_signal, current_data.close
                        )

                        # 🧠 AI THOUGHT: Position sizing
                        sizing_thought = f"Position size calculated: {position_size:.6f} {self.current_pair.base_currency}. "
                        sizing_thought += f"This represents {(position_size * current_data.close / self.portfolio.total_value * 100):.2f}% of portfolio value."

                        self.ai_logger.add_thought(
                            component="RiskManager",
                            thought_type="position_sizing",
                            content=sizing_thought,
                            confidence=0.9,
                            data={
                                'position_size': position_size,
                                'position_value': position_size * current_data.close,
                                'portfolio_percentage': position_size * current_data.close / self.portfolio.total_value * 100,
                                'price': current_data.close
                            }
                        )

                        # Create a new signal with the calculated position size
                        final_signal = TradingSignal(
                            signal=final_signal.signal,
                            confidence=final_signal.confidence,
                            price=final_signal.price,
                            timestamp=final_signal.timestamp,
                            strategy=final_signal.strategy,
                            reasoning=final_signal.reasoning,
                            risk_score=final_signal.risk_score,
                            position_size=position_size,
                            metadata=final_signal.metadata
                        )

                        # Execute trade
                        await self._execute_trade(final_signal)

                        # 🧠 AI THOUGHT: Trade execution
                        execution_thought = f"Trade executed: {final_signal.signal.value} {position_size:.6f} {self.current_pair.base_currency} "
                        execution_thought += f"at ${final_signal.price:.6f}. Total value: ${position_size * final_signal.price:.2f}"

                        self.ai_logger.add_thought(
                            component="TradeExecutor",
                            thought_type="trade_execution",
                            content=execution_thought,
                            confidence=final_signal.confidence,
                            data={
                                'action': final_signal.signal.value,
                                'size': position_size,
                                'price': final_signal.price,
                                'total_value': position_size * final_signal.price,
                                'strategy': final_signal.strategy.value if hasattr(final_signal.strategy, 'value') else str(final_signal.strategy)
                            }
                        )
                else:
                    # 🧠 AI THOUGHT: Hold decision
                    hold_thought = f"Decision: HOLD position. No trading opportunity identified. "
                    hold_thought += f"Current price: ${current_data.close:.6f}, confidence too low or market conditions unfavorable."

                    self.ai_logger.add_thought(
                        component="DecisionEngine",
                        thought_type="hold_decision",
                        content=hold_thought,
                        confidence=final_signal.confidence,
                        data={
                            'current_price': current_data.close,
                            'signal_confidence': final_signal.confidence,
                            'market_regime': market_regime.value,
                            'portfolio_value': self.portfolio.total_value
                        }
                    )

                # Store signal for analysis
                self.signals_history.append(final_signal)
                if len(self.signals_history) > 1000:
                    self.signals_history = self.signals_history[-1000:]

                # Also update recent_signals for pair switching
                self.recent_signals.append(final_signal)
                if len(self.recent_signals) > 100:
                    self.recent_signals = self.recent_signals[-100:]

                # Send notification for significant signals
                if self.notification_manager and final_signal.signal != SignalType.HOLD:
                    await self.notification_manager.notify_trading_signal(final_signal)

                # Update strategy weights based on performance (every 5 signals for faster learning)
                if self.learning_enabled and len(self.signals_history) % 5 == 0:
                    self._update_strategy_weights()

                    # 🧠 AI THOUGHT: Learning and adaptation
                    learning_thought = f"Strategy weights updated based on performance. "
                    learning_thought += f"Current weights: {', '.join([f'{k.value}: {v:.3f}' for k, v in self.strategy_weights.items()])}"

                    self.ai_logger.add_thought(
                        component="LearningEngine",
                        thought_type="adaptation",
                        content=learning_thought,
                        confidence=0.8,
                        data={
                            'strategy_weights': {k.value: v for k, v in self.strategy_weights.items()},
                            'total_signals': len(self.signals_history),
                            'learning_enabled': self.learning_enabled
                        }
                    )

                # 🧠 COMPLETE AI DECISION CYCLE
                execution_plan = {
                    'signal': final_signal.signal.value,
                    'confidence': final_signal.confidence,
                    'position_size': getattr(final_signal, 'position_size', 0.0),
                    'portfolio_impact': getattr(final_signal, 'position_size', 0.0) * final_signal.price / self.portfolio.total_value * 100,
                    'strategy_weights': {k.value: v for k, v in self.strategy_weights.items()},
                    'market_regime': market_regime.value,
                    'risk_score': final_signal.risk_score
                }

                self.ai_logger.complete_decision_cycle(final_signal, execution_plan)

                # Log current state
                logger.info("Trading decision",
                           signal=final_signal.signal.value,
                           confidence=final_signal.confidence,
                           reasoning=final_signal.reasoning,
                           portfolio_value=self.portfolio.total_value,
                           unrealized_pnl=self.portfolio.unrealized_pnl,
                           market_regime=final_signal.metadata.get('market_regime', 'Unknown'),
                           strategy_weights=self.strategy_weights)

                # Different sleep intervals based on data source
                if self.data_source == "live":
                    await asyncio.sleep(2)  # Faster updates for live data
                else:
                    await asyncio.sleep(5)  # Slower for mock data

            except Exception as e:
                import traceback
                logger.error("Trading loop error", error=str(e), traceback=traceback.format_exc())
                await asyncio.sleep(10)

    def _market_data_to_dataframe(self) -> pd.DataFrame:
        """Convert market data to DataFrame for analysis."""
        data = []
        for md in self.market_data:
            data.append({
                'timestamp': md.timestamp,
                'open': md.open,
                'high': md.high,
                'low': md.low,
                'close': md.close,
                'volume': md.volume
            })

        df = pd.DataFrame(data)
        if not df.empty and 'timestamp' in df.columns:
            df.set_index('timestamp', inplace=True)
        return df

    def _update_strategy_weights(self):
        """Update strategy weights based on performance learning."""
        if self.learning_enabled:
            new_weights = self.performance_tracker.calculate_strategy_weights()

            # Smooth the weight updates to avoid dramatic changes
            for strategy, new_weight in new_weights.items():
                current_weight = self.strategy_weights.get(strategy, 0.5)
                smoothed_weight = current_weight * 0.8 + new_weight * 0.2
                self.strategy_weights[strategy] = smoothed_weight

            logger.info("Strategy weights updated", weights=self.strategy_weights)

    def _simulate_trade_outcome(self, signal: TradingSignal) -> float:
        """Simulate trade outcome for learning (simplified)."""
        # This is a simplified simulation - in reality, you'd track actual trade outcomes
        if len(self.market_data) < 10:
            return 0.0

        # Look at price movement over next few periods
        current_price = signal.price
        future_prices = [md.close for md in self.market_data[-5:]]

        if not future_prices:
            return 0.0

        avg_future_price = sum(future_prices) / len(future_prices)

        if signal.signal == SignalType.BUY:
            return (avg_future_price - current_price) / current_price
        elif signal.signal == SignalType.SELL:
            return (current_price - avg_future_price) / current_price
        else:
            return 0.0

    def _combine_signals_enhanced(self, signals: List[TradingSignal],
                                 market_regime: MarketRegime, regime_confidence: float) -> TradingSignal:
        """Enhanced signal combination with regime awareness and strategy weighting."""
        if not signals:
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=0.0,
                timestamp=datetime.now(),
                strategy=StrategyType.TECHNICAL,
                reasoning="No signals generated",
                risk_score=1.0,
                position_size=0.0
            )

        # Weighted voting based on strategy performance and market regime
        weighted_buy_score = 0.0
        weighted_sell_score = 0.0
        total_weight = 0.0

        reasoning_parts = []
        max_risk_score = 0.0

        for signal in signals:
            strategy_weight = self.strategy_weights.get(signal.strategy, 0.5)

            # Adjust weight based on market regime
            if market_regime == MarketRegime.TRENDING_UP and signal.strategy == StrategyType.TECHNICAL:
                strategy_weight *= 1.2  # Boost technical analysis in trends
            elif market_regime == MarketRegime.RANGING and signal.strategy == StrategyType.ML_ENSEMBLE:
                strategy_weight *= 1.1  # Boost ML in ranging markets
            elif market_regime == MarketRegime.HIGH_VOLATILITY:
                strategy_weight *= 0.8  # Reduce all weights in high volatility

            # Calculate weighted scores
            if signal.signal == SignalType.BUY:
                weighted_buy_score += signal.confidence * strategy_weight
                reasoning_parts.append(f"{signal.strategy.value}: BUY ({signal.confidence:.2f})")
            elif signal.signal == SignalType.SELL:
                weighted_sell_score += signal.confidence * strategy_weight
                reasoning_parts.append(f"{signal.strategy.value}: SELL ({signal.confidence:.2f})")
            else:
                reasoning_parts.append(f"{signal.strategy.value}: HOLD")

            total_weight += strategy_weight
            max_risk_score = max(max_risk_score, signal.risk_score)

        # Determine final signal
        if total_weight == 0:
            final_signal = SignalType.HOLD
            confidence = 0.0
        elif weighted_buy_score > weighted_sell_score:
            final_signal = SignalType.BUY
            confidence = min(weighted_buy_score / total_weight, 1.0)
        elif weighted_sell_score > weighted_buy_score:
            final_signal = SignalType.SELL
            confidence = min(weighted_sell_score / total_weight, 1.0)
        else:
            final_signal = SignalType.HOLD
            confidence = 0.0

        # Adjust confidence based on regime confidence
        if regime_confidence > 0.8:
            confidence = min(confidence * 1.1, 1.0)  # Boost confidence in clear regimes
        elif regime_confidence < 0.5:
            confidence *= 0.9  # Reduce confidence in unclear regimes

        # Use the first signal as template
        template = signals[0]
        combined_reasoning = f"Enhanced: {'; '.join(reasoning_parts)} | Regime: {market_regime.value} ({regime_confidence:.2f})"

        return TradingSignal(
            signal=final_signal,
            confidence=confidence,
            price=template.price,
            timestamp=template.timestamp,
            strategy=template.strategy,
            reasoning=combined_reasoning,
            risk_score=max_risk_score,
            position_size=0.0,
            metadata={
                'market_regime': market_regime.value,
                'regime_confidence': regime_confidence,
                'strategy_weights': self.strategy_weights,
                'weighted_scores': {
                    'buy': weighted_buy_score,
                    'sell': weighted_sell_score
                }
            }
        )

    def _combine_signals(self, signals: List[TradingSignal]) -> TradingSignal:
        """Combine multiple signals into final decision."""
        if not signals:
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=0.0,
                timestamp=datetime.now(),
                strategy=StrategyType.TECHNICAL,
                reasoning="No signals generated",
                risk_score=1.0,
                position_size=0.0
            )

        # Simple voting mechanism
        buy_votes = sum(1 for s in signals if s.signal == SignalType.BUY)
        sell_votes = sum(1 for s in signals if s.signal == SignalType.SELL)

        if buy_votes > sell_votes:
            final_signal = SignalType.BUY
            confidence = sum(s.confidence for s in signals if s.signal == SignalType.BUY) / buy_votes
        elif sell_votes > buy_votes:
            final_signal = SignalType.SELL
            confidence = sum(s.confidence for s in signals if s.signal == SignalType.SELL) / sell_votes
        else:
            final_signal = SignalType.HOLD
            confidence = 0.0

        # Use the first signal as template
        template = signals[0]
        return TradingSignal(
            signal=final_signal,
            confidence=confidence,
            price=template.price,
            timestamp=template.timestamp,
            strategy=template.strategy,
            reasoning=f"Combined: {'; '.join(s.reasoning for s in signals)}",
            risk_score=max(s.risk_score for s in signals),
            position_size=0.0
        )

    async def _execute_trade(self, signal: TradingSignal):
        """Execute trading signal."""
        try:
            if self.paper_trading:
                await self._execute_paper_trade(signal)
            else:
                await self._execute_live_trade(signal)

        except Exception as e:
            logger.error("Trade execution failed", error=str(e), signal=signal.signal.value)

    async def _execute_paper_trade(self, signal: TradingSignal):
        """Execute paper trade."""
        product_id = settings.trading_pair

        if signal.signal == SignalType.BUY:
            # Buy position
            cost = signal.position_size * signal.price
            if cost <= self.portfolio.cash:
                self.portfolio.cash -= cost

                if product_id in self.portfolio.positions:
                    # Add to existing position
                    existing = self.portfolio.positions[product_id]
                    total_size = existing.size + signal.position_size
                    avg_price = ((existing.size * existing.entry_price) +
                               (signal.position_size * signal.price)) / total_size
                    existing.size = total_size
                    existing.entry_price = avg_price
                else:
                    # New position
                    self.portfolio.positions[product_id] = Position(
                        product_id=product_id,
                        size=signal.position_size,
                        entry_price=signal.price,
                        current_price=signal.price,
                        entry_time=signal.timestamp
                    )

                logger.info("Paper trade executed - BUY",
                           size=signal.position_size,
                           price=signal.price,
                           cost=cost,
                           remaining_cash=self.portfolio.cash)

                # Send trade notification
                if self.notification_manager:
                    await self.notification_manager.notify_trade_executed({
                        'action': 'BUY',
                        'symbol': product_id,
                        'size': signal.position_size,
                        'price': signal.price,
                        'cost': cost,
                        'remaining_cash': self.portfolio.cash,
                        'strategy': signal.strategy.value,
                        'confidence': signal.confidence
                    })

        elif signal.signal == SignalType.SELL:
            # Sell position
            if product_id in self.portfolio.positions:
                position = self.portfolio.positions[product_id]
                sell_size = min(signal.position_size, position.size)
                proceeds = sell_size * signal.price

                self.portfolio.cash += proceeds
                position.size -= sell_size

                # Calculate realized PnL
                realized_pnl = (signal.price - position.entry_price) * sell_size
                self.portfolio.realized_pnl += realized_pnl

                if position.size <= 0:
                    del self.portfolio.positions[product_id]

                logger.info("Paper trade executed - SELL",
                           size=sell_size,
                           price=signal.price,
                           proceeds=proceeds,
                           realized_pnl=realized_pnl,
                           total_cash=self.portfolio.cash)

                # Send trade notification
                if self.notification_manager:
                    await self.notification_manager.notify_trade_executed({
                        'action': 'SELL',
                        'symbol': product_id,
                        'size': sell_size,
                        'price': signal.price,
                        'proceeds': proceeds,
                        'realized_pnl': realized_pnl,
                        'total_cash': self.portfolio.cash,
                        'strategy': signal.strategy.value,
                        'confidence': signal.confidence
                    })

    async def _execute_live_trade(self, signal: TradingSignal):
        """Execute live trade (placeholder for future implementation)."""
        logger.warning("Live trading not implemented yet")

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get current portfolio summary."""
        return {
            'cash': self.portfolio.cash,
            'total_value': self.portfolio.total_value,
            'unrealized_pnl': self.portfolio.unrealized_pnl,
            'realized_pnl': self.portfolio.realized_pnl,
            'positions': {
                pid: {
                    'size': pos.size,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'entry_time': pos.entry_time.isoformat()
                }
                for pid, pos in self.portfolio.positions.items()
            },
            'total_return': ((self.portfolio.total_value - settings.initial_capital) /
                           settings.initial_capital) * 100
        }

    def get_recent_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent trading signals."""
        # Sort signals by timestamp to ensure proper ordering
        sorted_signals = sorted(self.signals_history, key=lambda x: x.timestamp) if self.signals_history else []
        recent = sorted_signals[-limit:] if sorted_signals else []
        return [
            {
                'signal': signal.signal.value,
                'confidence': signal.confidence,
                'price': signal.price,
                'timestamp': signal.timestamp.isoformat(),
                'strategy': signal.strategy.value,
                'reasoning': signal.reasoning,
                'risk_score': signal.risk_score,
                'position_size': signal.position_size,
                'metadata': signal.metadata
            }
            for signal in recent
        ]

    def get_performance_metrics(self) -> Dict[str, Any]:
        """🏛️ INSTITUTIONAL PERFORMANCE METRICS

        Comprehensive performance analysis including:
        - Traditional metrics (Sharpe, Sortino, Win Rate)
        - Risk-adjusted returns
        - Drawdown analysis
        - Institutional risk metrics
        """
        if not self.signals_history:
            return {}

        # Calculate win rate
        executed_signals = [s for s in self.signals_history if s.signal != SignalType.HOLD]
        if not executed_signals:
            return {}

        # Basic metrics
        total_trades = len(executed_signals)
        buy_signals = len([s for s in executed_signals if s.signal == SignalType.BUY])
        sell_signals = len([s for s in executed_signals if s.signal == SignalType.SELL])

        avg_confidence = sum(s.confidence for s in executed_signals) / total_trades
        avg_risk_score = sum(s.risk_score for s in executed_signals) / total_trades

        # 🏛️ INSTITUTIONAL METRICS from risk manager
        institutional_metrics = {
            'sharpe_ratio': self.risk_manager.sharpe_ratio,
            'sortino_ratio': self.risk_manager.sortino_ratio,
            'max_consecutive_losses': self.risk_manager.max_consecutive_losses,
            'current_consecutive_losses': self.risk_manager.current_consecutive_losses,
            'circuit_breaker_active': self.risk_manager.circuit_breaker_triggered,
            'volatility_adjustment': self.risk_manager.volatility_adjustment,
            'current_max_position_size': self.risk_manager.max_position_size,
            'current_risk_per_trade': self.risk_manager.risk_per_trade,
            'sizing_method': self.risk_manager.sizing_method,
            'confidence_threshold': self.risk_manager.confidence_threshold
        }

        # Calculate actual win rate from recent performance
        if self.risk_manager.recent_performance:
            wins = len([p for p in self.risk_manager.recent_performance if p > 0])
            win_rate = wins / len(self.risk_manager.recent_performance)
        else:
            win_rate = 0.0

        return {
            'total_trades': total_trades,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'avg_confidence': avg_confidence,
            'avg_risk_score': avg_risk_score,
            'win_rate': win_rate,
            **institutional_metrics
        }

    def get_institutional_risk_report(self) -> Dict[str, Any]:
        """🏛️ COMPREHENSIVE INSTITUTIONAL RISK REPORT"""
        return {
            'risk_limits': {
                'max_position_size': self.risk_manager.max_position_size,
                'risk_per_trade': self.risk_manager.risk_per_trade,
                'max_drawdown': self.risk_manager.max_drawdown,
                'max_daily_loss': self.risk_manager.max_daily_loss,
                'max_correlation': self.risk_manager.max_correlation,
                'max_leverage': self.risk_manager.max_leverage
            },
            'current_status': {
                'circuit_breaker_active': self.risk_manager.circuit_breaker_triggered,
                'volatility_adjustment': self.risk_manager.volatility_adjustment,
                'consecutive_losses': self.risk_manager.current_consecutive_losses,
                'sizing_method': self.risk_manager.sizing_method
            },
            'performance_metrics': {
                'sharpe_ratio': self.risk_manager.sharpe_ratio,
                'sortino_ratio': self.risk_manager.sortino_ratio,
                'max_consecutive_losses': self.risk_manager.max_consecutive_losses,
                'recent_trades': len(self.risk_manager.recent_performance)
            },
            'portfolio_metrics': {
                'total_value': self.portfolio.total_value,
                'cash': self.portfolio.cash,
                'unrealized_pnl': self.portfolio.unrealized_pnl,
                'realized_pnl': self.portfolio.realized_pnl,
                'active_positions': len(self.portfolio.positions)
            }
        }

    async def switch_trading_pair(self, new_pair: CryptoPair):
        """Switch to a different crypto trading pair with proper data isolation."""
        try:
            old_pair_symbol = self.current_pair.symbol if hasattr(self, 'current_pair') else None

            logger.info("🔄 Switching trading pair with data isolation",
                       old_pair=old_pair_symbol,
                       new_pair=new_pair.symbol)

            # 🧹 STEP 1: Store current pair data before clearing
            if old_pair_symbol and old_pair_symbol not in self.pair_specific_data:
                self.pair_specific_data[old_pair_symbol] = {}

            if old_pair_symbol:
                self.pair_specific_data[old_pair_symbol].update({
                    'last_signals': self.recent_signals[-10:] if len(self.recent_signals) >= 10 else self.recent_signals,
                    'performance_metrics': self.performance_tracker.get_performance_summary(),
                    'market_data': self.market_data[-100:] if len(self.market_data) >= 100 else self.market_data,
                    'timestamp': datetime.now().isoformat()
                })

            # 🧹 STEP 2: Clear all current data to prevent mixing
            logger.info("🧹 Clearing old market data to prevent mixing")
            self.market_data.clear()  # Clear all market data
            self.recent_signals.clear()  # Clear signals
            self.signals_history.clear()  # Clear signal history

            # Reset performance tracker for new pair
            self.performance_tracker.reset_for_new_pair()

            # Switch to new pair
            self.current_pair = new_pair

            # Load historical data for new pair
            await self._load_pair_historical_data(new_pair)

            # Initialize or load pair-specific models
            await self._initialize_pair_models(new_pair)

            # 🔄 STEP 7: Update WebSocket subscriptions to new pair
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                await self._update_websocket_subscriptions(old_pair_symbol, new_pair.symbol)

            # 🔄 STEP 8: Update API to use new pair
            if hasattr(self.api, 'current_symbol'):
                self.api.current_symbol = new_pair.alpaca_symbol

            # 🔄 STEP 9: Force immediate fresh data fetch for new pair
            logger.info("🚀 Fetching immediate live data for new pair")
            await self._fetch_live_data()

            logger.info("✅ Successfully switched trading pair with clean data isolation",
                       new_pair=new_pair.symbol,
                       historical_data_points=len(self.market_data),
                       old_data_cleared=True,
                       fresh_data_fetched=True)

        except Exception as e:
            logger.error("Failed to switch trading pair",
                        new_pair=new_pair.symbol,
                        error=str(e))
            raise

    async def _update_websocket_subscriptions(self, old_symbol: str, new_symbol: str):
        """Update WebSocket subscriptions when switching pairs."""
        try:
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                # Unsubscribe from old symbol
                if old_symbol:
                    logger.info("🔌 Unsubscribing from old symbol WebSocket", symbol=old_symbol)
                    await self.websocket_manager.unsubscribe_from_symbol(old_symbol)

                # Subscribe to new symbol
                logger.info("🔌 Subscribing to new symbol WebSocket", symbol=new_symbol)
                await self.websocket_manager.subscribe_to_symbol(new_symbol)

                # Clear any cached WebSocket data
                if hasattr(self.websocket_manager, 'clear_cached_data'):
                    self.websocket_manager.clear_cached_data()

        except Exception as e:
            logger.error("Failed to update WebSocket subscriptions",
                        old_symbol=old_symbol,
                        new_symbol=new_symbol,
                        error=str(e))

    async def _load_pair_historical_data(self, pair: CryptoPair):
        """Load historical data for the trading pair."""
        try:
            # Get historical data from database
            historical_df = historical_data_manager.get_historical_data(
                pair.symbol,
                timeframe="1Day",
                limit=500
            )

            if not historical_df.empty:
                # Convert to MarketData objects
                self.market_data = []
                for _, row in historical_df.iterrows():
                    market_data = MarketData(
                        timestamp=row['timestamp'],
                        open=row['open'],
                        high=row['high'],
                        low=row['low'],
                        close=row['close'],
                        volume=row['volume'],
                        product_id=pair.symbol
                    )
                    self.market_data.append(market_data)

                logger.info("Loaded historical data",
                           pair=pair.symbol,
                           data_points=len(self.market_data))
            else:
                # If no historical data, fetch it
                logger.info("No historical data found, fetching...", pair=pair.symbol)
                success = await historical_data_manager.fetch_historical_data_for_pair(pair)
                if success:
                    # Try loading again
                    await self._load_pair_historical_data(pair)
                else:
                    logger.warning("Failed to fetch historical data", pair=pair.symbol)
                    self.market_data = []

        except Exception as e:
            logger.error("Failed to load historical data",
                        pair=pair.symbol,
                        error=str(e))
            self.market_data = []

    async def _initialize_pair_models(self, pair: CryptoPair):
        """Initialize or load ML models specific to the trading pair."""
        try:
            pair_key = pair.symbol

            if pair_key not in self.pair_specific_models:
                # Initialize new models for this pair
                self.pair_specific_models[pair_key] = {
                    'ml_model': RandomForestClassifier(
                        n_estimators=100,
                        max_depth=10,
                        random_state=42
                    ),
                    'scaler': StandardScaler(),
                    'is_trained': False,
                    'last_training': None,
                    'performance_metrics': {}
                }

                logger.info("Initialized new models for pair", pair=pair.symbol)

            # Train models if we have enough historical data
            if len(self.market_data) > 100:
                await self._train_pair_models(pair)

        except Exception as e:
            logger.error("Failed to initialize pair models",
                        pair=pair.symbol,
                        error=str(e))

    async def _train_pair_models(self, pair: CryptoPair):
        """Train ML models with historical data for the specific pair."""
        try:
            if len(self.market_data) < 50:
                logger.warning("Insufficient data for training",
                              pair=pair.symbol,
                              data_points=len(self.market_data))
                return

            # Convert market data to DataFrame
            df_data = []
            for md in self.market_data:
                df_data.append({
                    'timestamp': md.timestamp,
                    'open': md.open,
                    'high': md.high,
                    'low': md.low,
                    'close': md.close,
                    'volume': md.volume
                })

            df = pd.DataFrame(df_data)
            df = df.sort_values('timestamp').reset_index(drop=True)

            # Calculate technical indicators
            indicators = TechnicalIndicators()
            df = indicators.calculate_all(df)

            # Prepare features and targets
            feature_columns = [
                'rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower',
                'sma_20', 'sma_50', 'williams_r', 'stoch_rsi_k'
            ]

            # Create target (future price movement)
            df['future_return'] = df['close'].pct_change(periods=5).shift(-5)  # 5-period forward return
            df['target'] = (df['future_return'] > 0.02).astype(int)  # 1 if >2% gain, 0 otherwise

            # Remove rows with NaN values
            df = df.dropna()

            if len(df) < 30:
                logger.warning("Insufficient clean data for training",
                              pair=pair.symbol,
                              clean_data_points=len(df))
                return

            # Prepare training data
            X = df[feature_columns].values
            y = df['target'].values

            # Get pair-specific models
            pair_models = self.pair_specific_models[pair.symbol]

            # Scale features
            X_scaled = pair_models['scaler'].fit_transform(X)

            # Train model
            pair_models['ml_model'].fit(X_scaled, y)
            pair_models['is_trained'] = True
            pair_models['last_training'] = datetime.now()

            # Calculate training metrics
            train_score = pair_models['ml_model'].score(X_scaled, y)
            pair_models['performance_metrics']['training_accuracy'] = train_score

            logger.info("Successfully trained models for pair",
                       pair=pair.symbol,
                       training_accuracy=train_score,
                       training_samples=len(X))

        except Exception as e:
            logger.error("Failed to train pair models",
                        pair=pair.symbol,
                        error=str(e))

    def get_current_pair_info(self) -> Dict[str, Any]:
        """Get information about the current trading pair."""
        return {
            'symbol': self.current_pair.symbol,
            'display_name': self.current_pair.display_name,
            'base_asset': self.current_pair.base_asset,
            'quote_asset': self.current_pair.quote_asset,
            'category': self.current_pair.category.value,
            'volatility_level': self.current_pair.volatility_level,
            'market_cap_rank': self.current_pair.market_cap_rank,
            'description': self.current_pair.description,
            'historical_data_points': len(self.market_data),
            'model_trained': self.pair_specific_models.get(self.current_pair.symbol, {}).get('is_trained', False)
        }
