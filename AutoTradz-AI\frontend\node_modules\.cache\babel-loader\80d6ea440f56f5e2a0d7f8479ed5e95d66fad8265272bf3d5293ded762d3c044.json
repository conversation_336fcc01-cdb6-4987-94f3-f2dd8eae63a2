{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\UserProfile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 24px;\n  color: #fff;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n_c = ProfileContainer;\nconst ProfileHeader = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #333;\n`;\n_c2 = ProfileHeader;\nconst Avatar = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  font-weight: bold;\n  color: white;\n`;\n_c3 = Avatar;\nconst UserInfo = styled.div`\n  flex: 1;\n`;\n_c4 = UserInfo;\nconst Username = styled.h2`\n  margin: 0 0 4px 0;\n  color: #4bffb5;\n  font-size: 24px;\n`;\n_c5 = Username;\nconst UserEmail = styled.p`\n  margin: 0;\n  color: #888;\n  font-size: 14px;\n`;\n_c6 = UserEmail;\nconst Section = styled.div`\n  margin-bottom: 24px;\n`;\n_c7 = Section;\nconst SectionTitle = styled.h3`\n  color: #4bffb5;\n  font-size: 18px;\n  margin: 0 0 16px 0;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #333;\n`;\n_c8 = SectionTitle;\nconst FormGroup = styled.div`\n  margin-bottom: 16px;\n`;\n_c9 = FormGroup;\nconst Label = styled.label`\n  display: block;\n  color: #ccc;\n  font-size: 14px;\n  margin-bottom: 8px;\n  font-weight: 600;\n`;\n_c0 = Label;\nconst Input = styled.input`\n  width: 100%;\n  padding: 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 8px;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n    box-shadow: 0 0 0 2px rgba(75, 255, 181, 0.1);\n  }\n  \n  &::placeholder {\n    color: #666;\n  }\n`;\n_c1 = Input;\nconst Button = styled.button`\n  background: ${props => props.$variant === 'danger' ? '#ff4976' : '#4bffb5'};\n  color: ${props => props.$variant === 'danger' ? '#fff' : '#000'};\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-right: 12px;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c10 = Button;\nconst ApiKeySection = styled.div`\n  background: #2a2a2a;\n  border-radius: 8px;\n  padding: 16px;\n  margin-top: 16px;\n`;\n_c11 = ApiKeySection;\nconst ApiKeyDisplay = styled.div`\n  font-family: monospace;\n  font-size: 12px;\n  color: #4bffb5;\n  background: #1a1a1a;\n  padding: 8px;\n  border-radius: 4px;\n  word-break: break-all;\n  margin-bottom: 12px;\n`;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 600;\n  background: ${props => props.$status === 'connected' ? '#4bffb5' : '#ff4976'};\n  color: ${props => props.$status === 'connected' ? '#000' : '#fff'};\n`;\n_c12 = StatusBadge;\nconst UserProfile = ({\n  onClose\n}) => {\n  _s();\n  var _user$username, _user$username$charAt;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [apiKeys, setApiKeys] = useState({\n    alpaca_key: '',\n    alpaca_secret: '',\n    paper_trading: true\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const handleSaveApiKeys = async () => {\n    setLoading(true);\n    setMessage('');\n    try {\n      // Here you would save the API keys to the backend\n      // For now, we'll just simulate the save\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setMessage('API keys saved successfully!');\n    } catch (error) {\n      setMessage('Failed to save API keys');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteAccount = async () => {\n    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {\n      try {\n        // Here you would delete the account\n        await logout();\n      } catch (error) {\n        setMessage('Failed to delete account');\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ProfileContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ProfileHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : (_user$username$charAt = _user$username.charAt(0)) === null || _user$username$charAt === void 0 ? void 0 : _user$username$charAt.toUpperCase()) || 'U'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n        children: [/*#__PURE__*/_jsxDEV(Username, {\n          children: (user === null || user === void 0 ? void 0 : user.username) || 'User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserEmail, {\n          children: (user === null || user === void 0 ? void 0 : user.email) || 'No email provided'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Section, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\uD83D\\uDD11 API Configuration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          children: \"Alpaca API Key\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"text\",\n          value: apiKeys.alpaca_key,\n          onChange: e => setApiKeys({\n            ...apiKeys,\n            alpaca_key: e.target.value\n          }),\n          placeholder: \"Enter your Alpaca API key\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          children: \"Alpaca API Secret\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"password\",\n          value: apiKeys.alpaca_secret,\n          onChange: e => setApiKeys({\n            ...apiKeys,\n            alpaca_secret: e.target.value\n          }),\n          placeholder: \"Enter your Alpaca API secret\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        children: /*#__PURE__*/_jsxDEV(Label, {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: apiKeys.paper_trading,\n            onChange: e => setApiKeys({\n              ...apiKeys,\n              paper_trading: e.target.checked\n            }),\n            style: {\n              marginRight: '8px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), \"Paper Trading Mode\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSaveApiKeys,\n        disabled: loading,\n        children: loading ? 'Saving...' : 'Save API Keys'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ApiKeySection, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"API Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatusBadge, {\n            $status: \"disconnected\",\n            children: \"Disconnected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '12px',\n            color: '#888',\n            margin: 0\n          },\n          children: \"Configure your API keys to enable live trading functionality.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Section, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u2699\\uFE0F Account Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"text\",\n          value: (user === null || user === void 0 ? void 0 : user.username) || '',\n          disabled: true,\n          placeholder: \"Username cannot be changed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"email\",\n          value: (user === null || user === void 0 ? void 0 : user.email) || '',\n          placeholder: \"Update your email address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        children: \"Update Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Section, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\uD83D\\uDEA8 Danger Zone\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#888',\n          fontSize: '14px',\n          marginBottom: '16px'\n        },\n        children: \"Once you delete your account, there is no going back. Please be certain.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        $variant: \"danger\",\n        onClick: handleDeleteAccount,\n        children: \"Delete Account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '12px',\n        borderRadius: '8px',\n        background: message.includes('success') ? 'rgba(75, 255, 181, 0.1)' : 'rgba(255, 73, 118, 0.1)',\n        border: `1px solid ${message.includes('success') ? '#4bffb5' : '#ff4976'}`,\n        color: message.includes('success') ? '#4bffb5' : '#ff4976',\n        fontSize: '14px',\n        marginTop: '16px'\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"L4Odli2P9gFWLjOFM4TaultEotg=\", false, function () {\n  return [useAuth];\n});\n_c13 = UserProfile;\nexport default UserProfile;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"ProfileContainer\");\n$RefreshReg$(_c2, \"ProfileHeader\");\n$RefreshReg$(_c3, \"Avatar\");\n$RefreshReg$(_c4, \"UserInfo\");\n$RefreshReg$(_c5, \"Username\");\n$RefreshReg$(_c6, \"UserEmail\");\n$RefreshReg$(_c7, \"Section\");\n$RefreshReg$(_c8, \"SectionTitle\");\n$RefreshReg$(_c9, \"FormGroup\");\n$RefreshReg$(_c0, \"Label\");\n$RefreshReg$(_c1, \"Input\");\n$RefreshReg$(_c10, \"Button\");\n$RefreshReg$(_c11, \"ApiKeySection\");\n$RefreshReg$(_c12, \"StatusBadge\");\n$RefreshReg$(_c13, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "jsxDEV", "_jsxDEV", "ProfileContainer", "div", "_c", "ProfileHeader", "_c2", "Avatar", "_c3", "UserInfo", "_c4", "Username", "h2", "_c5", "UserEmail", "p", "_c6", "Section", "_c7", "SectionTitle", "h3", "_c8", "FormGroup", "_c9", "Label", "label", "_c0", "Input", "input", "_c1", "<PERSON><PERSON>", "button", "props", "$variant", "_c10", "ApiKeySection", "_c11", "ApiKeyDisplay", "StatusBadge", "span", "$status", "_c12", "UserProfile", "onClose", "_s", "_user$username", "_user$username$charAt", "user", "logout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpaca_key", "alpaca_secret", "paper_trading", "loading", "setLoading", "message", "setMessage", "handleSaveApiKeys", "Promise", "resolve", "setTimeout", "error", "handleDeleteAccount", "window", "confirm", "children", "username", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "onClick", "type", "value", "onChange", "e", "target", "placeholder", "checked", "style", "marginRight", "disabled", "display", "justifyContent", "alignItems", "marginBottom", "fontSize", "fontWeight", "color", "margin", "padding", "borderRadius", "background", "includes", "border", "marginTop", "_c13", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/UserProfile.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from './AuthContext';\n\nconst ProfileContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 24px;\n  color: #fff;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\nconst ProfileHeader = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #333;\n`;\n\nconst Avatar = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  font-weight: bold;\n  color: white;\n`;\n\nconst UserInfo = styled.div`\n  flex: 1;\n`;\n\nconst Username = styled.h2`\n  margin: 0 0 4px 0;\n  color: #4bffb5;\n  font-size: 24px;\n`;\n\nconst UserEmail = styled.p`\n  margin: 0;\n  color: #888;\n  font-size: 14px;\n`;\n\nconst Section = styled.div`\n  margin-bottom: 24px;\n`;\n\nconst SectionTitle = styled.h3`\n  color: #4bffb5;\n  font-size: 18px;\n  margin: 0 0 16px 0;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #333;\n`;\n\nconst FormGroup = styled.div`\n  margin-bottom: 16px;\n`;\n\nconst Label = styled.label`\n  display: block;\n  color: #ccc;\n  font-size: 14px;\n  margin-bottom: 8px;\n  font-weight: 600;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 8px;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n    box-shadow: 0 0 0 2px rgba(75, 255, 181, 0.1);\n  }\n  \n  &::placeholder {\n    color: #666;\n  }\n`;\n\nconst Button = styled.button`\n  background: ${props => props.$variant === 'danger' ? '#ff4976' : '#4bffb5'};\n  color: ${props => props.$variant === 'danger' ? '#fff' : '#000'};\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-right: 12px;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst ApiKeySection = styled.div`\n  background: #2a2a2a;\n  border-radius: 8px;\n  padding: 16px;\n  margin-top: 16px;\n`;\n\nconst ApiKeyDisplay = styled.div`\n  font-family: monospace;\n  font-size: 12px;\n  color: #4bffb5;\n  background: #1a1a1a;\n  padding: 8px;\n  border-radius: 4px;\n  word-break: break-all;\n  margin-bottom: 12px;\n`;\n\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 600;\n  background: ${props => props.$status === 'connected' ? '#4bffb5' : '#ff4976'};\n  color: ${props => props.$status === 'connected' ? '#000' : '#fff'};\n`;\n\nconst UserProfile = ({ onClose }) => {\n  const { user, logout } = useAuth();\n  const [apiKeys, setApiKeys] = useState({\n    alpaca_key: '',\n    alpaca_secret: '',\n    paper_trading: true\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const handleSaveApiKeys = async () => {\n    setLoading(true);\n    setMessage('');\n    \n    try {\n      // Here you would save the API keys to the backend\n      // For now, we'll just simulate the save\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setMessage('API keys saved successfully!');\n    } catch (error) {\n      setMessage('Failed to save API keys');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteAccount = async () => {\n    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {\n      try {\n        // Here you would delete the account\n        await logout();\n      } catch (error) {\n        setMessage('Failed to delete account');\n      }\n    }\n  };\n\n  return (\n    <ProfileContainer>\n      <ProfileHeader>\n        <Avatar>\n          {user?.username?.charAt(0)?.toUpperCase() || 'U'}\n        </Avatar>\n        <UserInfo>\n          <Username>{user?.username || 'User'}</Username>\n          <UserEmail>{user?.email || 'No email provided'}</UserEmail>\n        </UserInfo>\n        <Button onClick={onClose}>Close</Button>\n      </ProfileHeader>\n\n      <Section>\n        <SectionTitle>🔑 API Configuration</SectionTitle>\n        <FormGroup>\n          <Label>Alpaca API Key</Label>\n          <Input\n            type=\"text\"\n            value={apiKeys.alpaca_key}\n            onChange={(e) => setApiKeys({...apiKeys, alpaca_key: e.target.value})}\n            placeholder=\"Enter your Alpaca API key\"\n          />\n        </FormGroup>\n        \n        <FormGroup>\n          <Label>Alpaca API Secret</Label>\n          <Input\n            type=\"password\"\n            value={apiKeys.alpaca_secret}\n            onChange={(e) => setApiKeys({...apiKeys, alpaca_secret: e.target.value})}\n            placeholder=\"Enter your Alpaca API secret\"\n          />\n        </FormGroup>\n        \n        <FormGroup>\n          <Label>\n            <input\n              type=\"checkbox\"\n              checked={apiKeys.paper_trading}\n              onChange={(e) => setApiKeys({...apiKeys, paper_trading: e.target.checked})}\n              style={{ marginRight: '8px' }}\n            />\n            Paper Trading Mode\n          </Label>\n        </FormGroup>\n        \n        <Button onClick={handleSaveApiKeys} disabled={loading}>\n          {loading ? 'Saving...' : 'Save API Keys'}\n        </Button>\n        \n        <ApiKeySection>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>\n            <span style={{ fontSize: '14px', fontWeight: '600' }}>API Status</span>\n            <StatusBadge $status=\"disconnected\">Disconnected</StatusBadge>\n          </div>\n          <p style={{ fontSize: '12px', color: '#888', margin: 0 }}>\n            Configure your API keys to enable live trading functionality.\n          </p>\n        </ApiKeySection>\n      </Section>\n\n      <Section>\n        <SectionTitle>⚙️ Account Settings</SectionTitle>\n        <FormGroup>\n          <Label>Username</Label>\n          <Input\n            type=\"text\"\n            value={user?.username || ''}\n            disabled\n            placeholder=\"Username cannot be changed\"\n          />\n        </FormGroup>\n        \n        <FormGroup>\n          <Label>Email</Label>\n          <Input\n            type=\"email\"\n            value={user?.email || ''}\n            placeholder=\"Update your email address\"\n          />\n        </FormGroup>\n        \n        <Button>Update Profile</Button>\n      </Section>\n\n      <Section>\n        <SectionTitle>🚨 Danger Zone</SectionTitle>\n        <p style={{ color: '#888', fontSize: '14px', marginBottom: '16px' }}>\n          Once you delete your account, there is no going back. Please be certain.\n        </p>\n        <Button $variant=\"danger\" onClick={handleDeleteAccount}>\n          Delete Account\n        </Button>\n      </Section>\n\n      {message && (\n        <div style={{\n          padding: '12px',\n          borderRadius: '8px',\n          background: message.includes('success') ? 'rgba(75, 255, 181, 0.1)' : 'rgba(255, 73, 118, 0.1)',\n          border: `1px solid ${message.includes('success') ? '#4bffb5' : '#ff4976'}`,\n          color: message.includes('success') ? '#4bffb5' : '#ff4976',\n          fontSize: '14px',\n          marginTop: '16px'\n        }}>\n          {message}\n        </div>\n      )}\n    </ProfileContainer>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,gBAAgB,GAAGJ,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,gBAAgB;AAStB,MAAMG,aAAa,GAAGP,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,aAAa;AASnB,MAAME,MAAM,GAAGT,MAAM,CAACK,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAXID,MAAM;AAaZ,MAAME,QAAQ,GAAGX,MAAM,CAACK,GAAG;AAC3B;AACA,CAAC;AAACO,GAAA,GAFID,QAAQ;AAId,MAAME,QAAQ,GAAGb,MAAM,CAACc,EAAE;AAC1B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,QAAQ;AAMd,MAAMG,SAAS,GAAGhB,MAAM,CAACiB,CAAC;AAC1B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,SAAS;AAMf,MAAMG,OAAO,GAAGnB,MAAM,CAACK,GAAG;AAC1B;AACA,CAAC;AAACe,GAAA,GAFID,OAAO;AAIb,MAAME,YAAY,GAAGrB,MAAM,CAACsB,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,YAAY;AAQlB,MAAMG,SAAS,GAAGxB,MAAM,CAACK,GAAG;AAC5B;AACA,CAAC;AAACoB,GAAA,GAFID,SAAS;AAIf,MAAME,KAAK,GAAG1B,MAAM,CAAC2B,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,KAAK;AAQX,MAAMG,KAAK,GAAG7B,MAAM,CAAC8B,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAlBIF,KAAK;AAoBX,MAAMG,MAAM,GAAGhC,MAAM,CAACiC,MAAM;AAC5B,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;AAC5E,WAAWD,KAAK,IAAIA,KAAK,CAACC,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAtBIJ,MAAM;AAwBZ,MAAMK,aAAa,GAAGrC,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GALID,aAAa;AAOnB,MAAME,aAAa,GAAGvC,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMmC,WAAW,GAAGxC,MAAM,CAACyC,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA,gBAAgBP,KAAK,IAAIA,KAAK,CAACQ,OAAO,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;AAC9E,WAAWR,KAAK,IAAIA,KAAK,CAACQ,OAAO,KAAK,WAAW,GAAG,MAAM,GAAG,MAAM;AACnE,CAAC;AAACC,IAAA,GARIH,WAAW;AAUjB,MAAMI,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,qBAAA;EACnC,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGjD,OAAO,CAAC,CAAC;EAClC,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC;IACrCsD,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM6D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCH,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF;MACA;MACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDH,UAAU,CAAC,8BAA8B,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdL,UAAU,CAAC,yBAAyB,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIC,MAAM,CAACC,OAAO,CAAC,6EAA6E,CAAC,EAAE;MACjG,IAAI;QACF;QACA,MAAMjB,MAAM,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdL,UAAU,CAAC,0BAA0B,CAAC;MACxC;IACF;EACF,CAAC;EAED,oBACExD,OAAA,CAACC,gBAAgB;IAAAgE,QAAA,gBACfjE,OAAA,CAACI,aAAa;MAAA6D,QAAA,gBACZjE,OAAA,CAACM,MAAM;QAAA2D,QAAA,EACJ,CAAAnB,IAAI,aAAJA,IAAI,wBAAAF,cAAA,GAAJE,IAAI,CAAEoB,QAAQ,cAAAtB,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBuB,MAAM,CAAC,CAAC,CAAC,cAAAtB,qBAAA,uBAAzBA,qBAAA,CAA2BuB,WAAW,CAAC,CAAC,KAAI;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACTxE,OAAA,CAACQ,QAAQ;QAAAyD,QAAA,gBACPjE,OAAA,CAACU,QAAQ;UAAAuD,QAAA,EAAE,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,QAAQ,KAAI;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAC/CxE,OAAA,CAACa,SAAS;UAAAoD,QAAA,EAAE,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,KAAI;QAAmB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACXxE,OAAA,CAAC6B,MAAM;QAAC6C,OAAO,EAAEhC,OAAQ;QAAAuB,QAAA,EAAC;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAEhBxE,OAAA,CAACgB,OAAO;MAAAiD,QAAA,gBACNjE,OAAA,CAACkB,YAAY;QAAA+C,QAAA,EAAC;MAAoB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACjDxE,OAAA,CAACqB,SAAS;QAAA4C,QAAA,gBACRjE,OAAA,CAACuB,KAAK;UAAA0C,QAAA,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7BxE,OAAA,CAAC0B,KAAK;UACJiD,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE5B,OAAO,CAACE,UAAW;UAC1B2B,QAAQ,EAAGC,CAAC,IAAK7B,UAAU,CAAC;YAAC,GAAGD,OAAO;YAAEE,UAAU,EAAE4B,CAAC,CAACC,MAAM,CAACH;UAAK,CAAC,CAAE;UACtEI,WAAW,EAAC;QAA2B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZxE,OAAA,CAACqB,SAAS;QAAA4C,QAAA,gBACRjE,OAAA,CAACuB,KAAK;UAAA0C,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCxE,OAAA,CAAC0B,KAAK;UACJiD,IAAI,EAAC,UAAU;UACfC,KAAK,EAAE5B,OAAO,CAACG,aAAc;UAC7B0B,QAAQ,EAAGC,CAAC,IAAK7B,UAAU,CAAC;YAAC,GAAGD,OAAO;YAAEG,aAAa,EAAE2B,CAAC,CAACC,MAAM,CAACH;UAAK,CAAC,CAAE;UACzEI,WAAW,EAAC;QAA8B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZxE,OAAA,CAACqB,SAAS;QAAA4C,QAAA,eACRjE,OAAA,CAACuB,KAAK;UAAA0C,QAAA,gBACJjE,OAAA;YACE2E,IAAI,EAAC,UAAU;YACfM,OAAO,EAAEjC,OAAO,CAACI,aAAc;YAC/ByB,QAAQ,EAAGC,CAAC,IAAK7B,UAAU,CAAC;cAAC,GAAGD,OAAO;cAAEI,aAAa,EAAE0B,CAAC,CAACC,MAAM,CAACE;YAAO,CAAC,CAAE;YAC3EC,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAM;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,sBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEZxE,OAAA,CAAC6B,MAAM;QAAC6C,OAAO,EAAEjB,iBAAkB;QAAC2B,QAAQ,EAAE/B,OAAQ;QAAAY,QAAA,EACnDZ,OAAO,GAAG,WAAW,GAAG;MAAe;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAETxE,OAAA,CAACkC,aAAa;QAAA+B,QAAA,gBACZjE,OAAA;UAAKkF,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAvB,QAAA,gBAC1GjE,OAAA;YAAMkF,KAAK,EAAE;cAAEO,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAzB,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvExE,OAAA,CAACqC,WAAW;YAACE,OAAO,EAAC,cAAc;YAAA0B,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACNxE,OAAA;UAAGkF,KAAK,EAAE;YAAEO,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAA3B,QAAA,EAAC;QAE1D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEVxE,OAAA,CAACgB,OAAO;MAAAiD,QAAA,gBACNjE,OAAA,CAACkB,YAAY;QAAA+C,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAChDxE,OAAA,CAACqB,SAAS;QAAA4C,QAAA,gBACRjE,OAAA,CAACuB,KAAK;UAAA0C,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvBxE,OAAA,CAAC0B,KAAK;UACJiD,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,QAAQ,KAAI,EAAG;UAC5BkB,QAAQ;UACRJ,WAAW,EAAC;QAA4B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZxE,OAAA,CAACqB,SAAS;QAAA4C,QAAA,gBACRjE,OAAA,CAACuB,KAAK;UAAA0C,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpBxE,OAAA,CAAC0B,KAAK;UACJiD,IAAI,EAAC,OAAO;UACZC,KAAK,EAAE,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,KAAI,EAAG;UACzBO,WAAW,EAAC;QAA2B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZxE,OAAA,CAAC6B,MAAM;QAAAoC,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEVxE,OAAA,CAACgB,OAAO;MAAAiD,QAAA,gBACNjE,OAAA,CAACkB,YAAY;QAAA+C,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAC3CxE,OAAA;QAAGkF,KAAK,EAAE;UAAES,KAAK,EAAE,MAAM;UAAEF,QAAQ,EAAE,MAAM;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAvB,QAAA,EAAC;MAErE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJxE,OAAA,CAAC6B,MAAM;QAACG,QAAQ,EAAC,QAAQ;QAAC0C,OAAO,EAAEZ,mBAAoB;QAAAG,QAAA,EAAC;MAExD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAETjB,OAAO,iBACNvD,OAAA;MAAKkF,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAExC,OAAO,CAACyC,QAAQ,CAAC,SAAS,CAAC,GAAG,yBAAyB,GAAG,yBAAyB;QAC/FC,MAAM,EAAE,aAAa1C,OAAO,CAACyC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE;QAC1EL,KAAK,EAAEpC,OAAO,CAACyC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAS;QAC1DP,QAAQ,EAAE,MAAM;QAChBS,SAAS,EAAE;MACb,CAAE;MAAAjC,QAAA,EACCV;IAAO;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAEvB,CAAC;AAAC7B,EAAA,CApJIF,WAAW;EAAA,QACU3C,OAAO;AAAA;AAAAqG,IAAA,GAD5B1D,WAAW;AAsJjB,eAAeA,WAAW;AAAC,IAAAtC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAK,IAAA,EAAA2D,IAAA;AAAAC,YAAA,CAAAjG,EAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}