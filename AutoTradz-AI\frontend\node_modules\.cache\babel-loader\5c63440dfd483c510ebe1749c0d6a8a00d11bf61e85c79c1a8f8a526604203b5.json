{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { AuthProvider, useAuth } from './AuthContext';\nimport LoginPage from './LoginPage';\nimport CleanDashboard from './CleanDashboard';\nimport UserProfile from './UserProfile';\nimport ErrorBoundary from './ErrorBoundary';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: #0a0a0a;\n  color: #ffffff;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n\n  /* Mobile-first responsive design */\n  @media (max-width: 768px) {\n    padding: 0;\n    overflow-x: hidden;\n  }\n`;\n_c = AppContainer;\nconst Header = styled.header`\n  background: #1a1a1a;\n  padding: 16px 24px;\n  border-bottom: 1px solid #333;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  @media (max-width: 768px) {\n    padding: 12px 16px;\n    flex-direction: column;\n    gap: 12px;\n\n    & > div {\n      flex-wrap: wrap;\n      justify-content: center;\n      gap: 8px !important;\n    }\n  }\n\n  @media (max-width: 480px) {\n    padding: 8px 12px;\n\n    & > div {\n      font-size: 10px;\n    }\n  }\n`;\n_c2 = Header;\nconst Logo = styled.h1`\n  margin: 0;\n  color: #4bffb5;\n  font-size: 24px;\n  font-weight: 700;\n`;\n_c3 = Logo;\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  color: #fff;\n  font-size: 14px;\n`;\n_c4 = UserSection;\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  color: white;\n  font-size: 12px;\n`;\n_c5 = UserAvatar;\nconst ProfileButton = styled.button`\n  background: #4bffb5;\n  color: #000;\n  border: none;\n  padding: 6px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-right: 8px;\n\n  &:hover {\n    background: #3de89f;\n    transform: translateY(-1px);\n  }\n`;\n_c6 = ProfileButton;\nconst LogoutButton = styled.button`\n  background: #ff4976;\n  color: white;\n  border: none;\n  padding: 6px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #e63946;\n    transform: translateY(-1px);\n  }\n`;\n_c7 = LogoutButton;\nconst Modal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 20px;\n`;\n_c8 = Modal;\nconst StatusBadge = styled.div`\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n  background: ${props => props.$connected ? '#4bffb5' : '#ff4976'};\n  color: ${props => props.$connected ? '#000' : '#fff'};\n`;\n_c9 = StatusBadge;\nconst MainContent = styled.main`\n  padding: 24px;\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  grid-template-rows: auto auto auto auto;\n  gap: 24px;\n  max-width: 1600px;\n  margin: 0 auto;\n\n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr;\n  }\n\n  @media (max-width: 768px) {\n    padding: 16px;\n    gap: 16px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 12px;\n    gap: 12px;\n  }\n`;\nconst ChartSection = styled.section`\n  grid-column: 1;\n  grid-row: 1 / 3;\n\n  @media (max-width: 1200px) {\n    grid-column: 1;\n    grid-row: 1;\n  }\n\n  @media (max-width: 768px) {\n    display: ${props => props.mobileVisible ? 'block' : 'none'};\n    margin-bottom: 60px; /* Space for mobile navigation */\n  }\n`;\nconst SidePanel = styled.section`\n  grid-column: 2;\n  grid-row: 1;\n\n  @media (max-width: 1200px) {\n    grid-column: 1;\n    grid-row: 2;\n  }\n\n  @media (max-width: 768px) {\n    display: none; /* Hidden on mobile, content shown in modals */\n  }\n`;\nconst BottomPanels = styled.section`\n  grid-column: 1 / -1;\n  grid-row: 3;\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;\n  gap: 24px;\n\n  @media (max-width: 1400px) {\n    grid-template-columns: 1fr 1fr 1fr;\n  }\n\n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media (max-width: 900px) {\n    grid-template-columns: 1fr;\n  }\n\n  @media (max-width: 768px) {\n    display: none; /* Hidden on mobile, content shown in modals */\n  }\n`;\nconst RiskSection = styled.section`\n  grid-column: 1 / -1;\n  grid-row: 4;\n  margin-top: 24px;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24px;\n\n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr;\n  }\n\n  @media (max-width: 768px) {\n    display: none; /* Hidden on mobile, content shown in modals */\n  }\n`;\n\n// Loading component\nconst LoadingScreen = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #0a0a0a;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n\n// Main Dashboard Component (requires authentication)\n_c0 = LoadingScreen;\nfunction Dashboard() {\n  _s();\n  var _user$username, _user$username$charAt;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [wsConnected, setWsConnected] = useState(false);\n  const [showProfile, setShowProfile] = useState(false);\n\n  // Add global error handler\n  useEffect(() => {\n    const handleError = event => {\n      console.error('🚨 Global error caught:', event.error);\n    };\n    const handleUnhandledRejection = event => {\n      console.error('🚨 Unhandled promise rejection:', event.reason);\n    };\n    window.addEventListener('error', handleError);\n    window.addEventListener('unhandledrejection', handleUnhandledRejection);\n    return () => {\n      window.removeEventListener('error', handleError);\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection);\n    };\n  }, []);\n  const [marketData, setMarketData] = useState([]);\n  const [portfolio, setPortfolio] = useState({\n    total_value: 0,\n    unrealized_pnl: 0,\n    positions: []\n  });\n  const [signals, setSignals] = useState([]);\n  const [performance, setPerformance] = useState({\n    total_return: 0,\n    win_rate: 0,\n    total_trades: 0\n  });\n  const [tradingActive, setTradingActive] = useState(false);\n  const [apiStatus, setApiStatus] = useState('mock');\n  const [dataSource, setDataSource] = useState('mock');\n  const [lastDataUpdate, setLastDataUpdate] = useState(new Date());\n  useEffect(() => {\n    // Initialize WebSocket connection\n    const ws = new WebSocket('ws://localhost:8000/ws');\n    ws.onopen = () => {\n      console.log('WebSocket connected');\n      setWsConnected(true);\n\n      // Subscribe to updates\n      ws.send(JSON.stringify({\n        type: 'subscribe',\n        channels: ['market_data', 'portfolio', 'signals']\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.type === 'market_update') {\n          const {\n            data\n          } = message;\n\n          // Update market data with proper candlestick data\n          if (data.candlestick) {\n            setMarketData(prev => {\n              const newData = {\n                timestamp: data.candlestick.timestamp,\n                open: data.candlestick.open,\n                high: data.candlestick.high,\n                low: data.candlestick.low,\n                close: data.candlestick.close,\n                volume: data.candlestick.volume || 0\n              };\n              const updated = [...prev, newData];\n              return updated.slice(-1000); // Keep last 1000 points\n            });\n          }\n\n          // Update portfolio\n          if (data.portfolio) {\n            setPortfolio(data.portfolio);\n          }\n\n          // Update signals\n          if (data.recent_signals) {\n            setSignals(data.recent_signals);\n          }\n\n          // Update last data timestamp\n          setLastDataUpdate(new Date());\n          console.log('📡 WebSocket data received at', new Date().toLocaleTimeString());\n        }\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n    ws.onclose = () => {\n      console.log('WebSocket disconnected');\n      setWsConnected(false);\n    };\n    ws.onerror = error => {\n      console.error('WebSocket error:', error);\n      setWsConnected(false);\n    };\n\n    // Cleanup\n    return () => {\n      ws.close();\n    };\n  }, []);\n  useEffect(() => {\n    // Fetch initial data\n    const fetchInitialData = async () => {\n      try {\n        // Fetch market data\n        const marketResponse = await fetch('http://localhost:8000/market-data?limit=200');\n        if (marketResponse.ok) {\n          const marketDataResult = await marketResponse.json();\n          setMarketData(marketDataResult);\n        }\n\n        // Fetch portfolio\n        const portfolioResponse = await fetch('http://localhost:8000/portfolio');\n        if (portfolioResponse.ok) {\n          const portfolioResult = await portfolioResponse.json();\n          setPortfolio(portfolioResult);\n        }\n\n        // Fetch signals\n        const signalsResponse = await fetch('http://localhost:8000/signals?limit=10');\n        if (signalsResponse.ok) {\n          const signalsResult = await signalsResponse.json();\n          setSignals(signalsResult);\n        }\n\n        // Fetch performance\n        const performanceResponse = await fetch('http://localhost:8000/performance');\n        if (performanceResponse.ok) {\n          const performanceResult = await performanceResponse.json();\n          setPerformance(performanceResult);\n        }\n\n        // Fetch data source status\n        const statusResponse = await fetch('http://localhost:8000/api/data-source-status');\n        if (statusResponse.ok) {\n          const statusResult = await statusResponse.json();\n          setDataSource(statusResult.data_source);\n          setTradingActive(statusResult.trading_active);\n          setApiStatus(statusResult.api_configured ? 'connected' : 'mock');\n        }\n      } catch (error) {\n        console.error('Error fetching initial data:', error);\n      }\n    };\n    fetchInitialData();\n\n    // Set up periodic status check\n    const statusInterval = setInterval(async () => {\n      try {\n        const statusResponse = await fetch('http://localhost:8000/api/data-source-status');\n        if (statusResponse.ok) {\n          const statusResult = await statusResponse.json();\n          setDataSource(statusResult.data_source);\n          setTradingActive(statusResult.trading_active);\n        }\n      } catch (error) {\n        console.error('Error fetching status:', error);\n      }\n    }, 5000); // Check every 5 seconds\n\n    // Set up real-time data polling as fallback to WebSocket (only when WebSocket is disconnected)\n    const dataPollingInterval = setInterval(async () => {\n      try {\n        // Only poll if WebSocket is disconnected to avoid conflicts\n        if (!wsConnected) {\n          // Fetch latest market data\n          const marketResponse = await fetch('http://localhost:8000/market-data?limit=50&source=live');\n          if (marketResponse.ok) {\n            const marketDataResult = await marketResponse.json();\n            setMarketData(prev => {\n              // Only update if we have new data\n              if (Array.isArray(marketDataResult) && marketDataResult.length > 0) {\n                var _marketDataResult, _prev;\n                const latestTimestamp = (_marketDataResult = marketDataResult[marketDataResult.length - 1]) === null || _marketDataResult === void 0 ? void 0 : _marketDataResult.timestamp;\n                const currentLatest = Array.isArray(prev) && prev.length > 0 ? (_prev = prev[prev.length - 1]) === null || _prev === void 0 ? void 0 : _prev.timestamp : null;\n                if (latestTimestamp && latestTimestamp !== currentLatest) {\n                  console.log('Updating market data via polling (WebSocket disconnected)');\n                  return marketDataResult;\n                }\n              }\n              return prev || [];\n            });\n          }\n        }\n\n        // Fetch latest signals\n        const signalsResponse = await fetch('http://localhost:8000/signals?limit=10');\n        if (signalsResponse.ok) {\n          const signalsResult = await signalsResponse.json();\n          setSignals(prev => {\n            // Only update if we have new signals\n            if (Array.isArray(signalsResult) && signalsResult.length > 0) {\n              var _signalsResult$, _prev$;\n              const latestTimestamp = (_signalsResult$ = signalsResult[0]) === null || _signalsResult$ === void 0 ? void 0 : _signalsResult$.timestamp;\n              const currentLatest = Array.isArray(prev) && prev.length > 0 ? (_prev$ = prev[0]) === null || _prev$ === void 0 ? void 0 : _prev$.timestamp : null;\n              if (latestTimestamp && latestTimestamp !== currentLatest) {\n                console.log('Updating signals via polling');\n                return signalsResult;\n              }\n            }\n            return prev || [];\n          });\n        }\n\n        // Fetch portfolio\n        const portfolioResponse = await fetch('http://localhost:8000/portfolio');\n        if (portfolioResponse.ok) {\n          const portfolioResult = await portfolioResponse.json();\n          if (portfolioResult && typeof portfolioResult === 'object') {\n            setPortfolio(portfolioResult);\n          }\n        }\n      } catch (error) {\n        console.error('Error in data polling:', error);\n      }\n    }, 2000); // Poll every 2 seconds for real-time feel\n\n    return () => {\n      clearInterval(statusInterval);\n      clearInterval(dataPollingInterval);\n    };\n  }, []);\n  const handleStartTrading = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/trading/start', {\n        method: 'POST'\n      });\n      if (response.ok) {\n        setTradingActive(true);\n      }\n    } catch (error) {\n      console.error('Error starting trading:', error);\n    }\n  };\n  const handleStopTrading = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/trading/stop', {\n        method: 'POST'\n      });\n      if (response.ok) {\n        setTradingActive(false);\n      }\n    } catch (error) {\n      console.error('Error stopping trading:', error);\n    }\n  };\n  const handleApiUpdate = apiConfig => {\n    console.log('API configuration updated:', apiConfig);\n    setApiStatus('connected');\n    // Optionally refresh data after API update\n    // fetchInitialData();\n  };\n  const handleDataSourceSwitch = async newDataSource => {\n    console.log('Data source switched to:', newDataSource);\n    setDataSource(newDataSource);\n\n    // Update API status based on data source\n    if (newDataSource === 'live') {\n      setApiStatus('live');\n    } else if (newDataSource === 'mock') {\n      setApiStatus('mock');\n    }\n\n    // Force refresh of data after switching\n    try {\n      const statusResponse = await fetch('http://localhost:8000/api/data-source-status');\n      if (statusResponse.ok) {\n        const statusResult = await statusResponse.json();\n        setDataSource(statusResult.data_source);\n        setTradingActive(statusResult.trading_active);\n        setApiStatus(statusResult.api_configured && statusResult.data_source === 'live' ? 'live' : statusResult.data_source);\n      }\n    } catch (error) {\n      console.error('Error refreshing status after switch:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: \"AutoTradz AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#888',\n              fontFamily: 'monospace'\n            },\n            children: [\"Last Update: \", lastDataUpdate.toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              padding: '4px 8px',\n              borderRadius: '12px',\n              background: dataSource === 'live' ? 'rgba(75, 255, 181, 0.1)' : 'rgba(75, 181, 255, 0.1)',\n              border: `1px solid ${dataSource === 'live' ? 'rgba(75, 255, 181, 0.3)' : 'rgba(75, 181, 255, 0.3)'}`,\n              color: dataSource === 'live' ? '#4bffb5' : '#4bb5ff',\n              fontWeight: '600'\n            },\n            children: [dataSource === 'live' ? '🔴 LIVE' : '🔵 WEBSOCKET', \" DATA\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(StatusBadge, {\n            $connected: wsConnected,\n            children: wsConnected ? 'Connected' : 'Disconnected'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(UserSection, {\n            children: [/*#__PURE__*/_jsxDEV(UserAvatar, {\n              children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : (_user$username$charAt = _user$username.charAt(0)) === null || _user$username$charAt === void 0 ? void 0 : _user$username$charAt.toUpperCase()) || 'U'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.username) || 'User']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(ProfileButton, {\n              onClick: () => setShowProfile(true),\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(LogoutButton, {\n              onClick: logout,\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CleanDashboard, {\n        marketData: marketData,\n        portfolio: portfolio,\n        signals: signals,\n        performance: performance,\n        tradingActive: tradingActive,\n        dataSource: dataSource,\n        wsConnected: wsConnected,\n        onStartTrading: handleStartTrading,\n        onStopTrading: handleStopTrading,\n        onApiUpdate: handleApiUpdate,\n        onDataSourceSwitch: handleDataSourceSwitch\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 7\n      }, this), showProfile && /*#__PURE__*/_jsxDEV(Modal, {\n        onClick: () => setShowProfile(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: e => e.stopPropagation(),\n          children: /*#__PURE__*/_jsxDEV(UserProfile, {\n            onClose: () => setShowProfile(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 530,\n    columnNumber: 5\n  }, this);\n}\n\n// Main App Component with Authentication\n_s(Dashboard, \"LrchtytAjksdaqOVzc3hZUUm5Ec=\", false, function () {\n  return [useAuth];\n});\n_c1 = Dashboard;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 604,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 603,\n    columnNumber: 5\n  }, this);\n}\n\n// App Content Component (handles routing based on auth state)\n_c10 = App;\nfunction AppContent() {\n  _s2();\n  const {\n    isAuthenticated,\n    loading,\n    login\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingScreen, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading AutoTradz AI...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 615,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(LoginPage, {\n      onLogin: login\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 625,\n    columnNumber: 10\n  }, this);\n}\n_s2(AppContent, \"i1PBl1emsPipT0wN2BKLod9PZqo=\", false, function () {\n  return [useAuth];\n});\n_c11 = AppContent;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"UserSection\");\n$RefreshReg$(_c5, \"UserAvatar\");\n$RefreshReg$(_c6, \"ProfileButton\");\n$RefreshReg$(_c7, \"LogoutButton\");\n$RefreshReg$(_c8, \"Modal\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"LoadingScreen\");\n$RefreshReg$(_c1, \"Dashboard\");\n$RefreshReg$(_c10, \"App\");\n$RefreshReg$(_c11, \"AppContent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "LoginPage", "CleanDashboard", "UserProfile", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Header", "header", "_c2", "Logo", "h1", "_c3", "UserSection", "_c4", "UserAvatar", "_c5", "ProfileButton", "button", "_c6", "LogoutButton", "_c7", "Modal", "_c8", "StatusBadge", "props", "$connected", "_c9", "MainContent", "main", "ChartSection", "section", "mobileVisible", "SidePanel", "BottomPanels", "RiskSection", "LoadingScreen", "_c0", "Dashboard", "_s", "_user$username", "_user$username$charAt", "user", "logout", "wsConnected", "setWsConnected", "showProfile", "setShowProfile", "handleError", "event", "console", "error", "handleUnhandledRejection", "reason", "window", "addEventListener", "removeEventListener", "marketData", "setMarketData", "portfolio", "setPortfolio", "total_value", "unrealized_pnl", "positions", "signals", "setSignals", "performance", "setPerformance", "total_return", "win_rate", "total_trades", "tradingActive", "setTradingActive", "api<PERSON><PERSON>us", "setApiStatus", "dataSource", "setDataSource", "lastDataUpdate", "setLastDataUpdate", "Date", "ws", "WebSocket", "onopen", "log", "send", "JSON", "stringify", "type", "channels", "onmessage", "message", "parse", "data", "candlestick", "prev", "newData", "timestamp", "open", "high", "low", "close", "volume", "updated", "slice", "recent_signals", "toLocaleTimeString", "onclose", "onerror", "fetchInitialData", "marketResponse", "fetch", "ok", "marketDataResult", "json", "portfolioResponse", "portfolioResult", "signalsResponse", "signalsResult", "performanceResponse", "performanceResult", "statusResponse", "statusResult", "data_source", "trading_active", "api_configured", "statusInterval", "setInterval", "dataPollingInterval", "Array", "isArray", "length", "_marketDataResult", "_prev", "latestTimestamp", "currentLatest", "_signalsResult$", "_prev$", "clearInterval", "handleStartTrading", "response", "method", "handleStopTrading", "handleApiUpdate", "apiConfig", "handleDataSourceSwitch", "newDataSource", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "alignItems", "gap", "fontSize", "color", "fontFamily", "padding", "borderRadius", "background", "border", "fontWeight", "username", "char<PERSON>t", "toUpperCase", "onClick", "onStartTrading", "onStopTrading", "onApiUpdate", "onDataSourceSwitch", "e", "stopPropagation", "onClose", "_c1", "App", "A<PERSON><PERSON><PERSON>nt", "_c10", "_s2", "isAuthenticated", "loading", "login", "onLogin", "_c11", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { AuthProvider, useAuth } from './AuthContext';\nimport LoginPage from './LoginPage';\nimport CleanDashboard from './CleanDashboard';\nimport UserProfile from './UserProfile';\nimport ErrorBoundary from './ErrorBoundary';\nimport './App.css';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: #0a0a0a;\n  color: #ffffff;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n\n  /* Mobile-first responsive design */\n  @media (max-width: 768px) {\n    padding: 0;\n    overflow-x: hidden;\n  }\n`;\n\nconst Header = styled.header`\n  background: #1a1a1a;\n  padding: 16px 24px;\n  border-bottom: 1px solid #333;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  @media (max-width: 768px) {\n    padding: 12px 16px;\n    flex-direction: column;\n    gap: 12px;\n\n    & > div {\n      flex-wrap: wrap;\n      justify-content: center;\n      gap: 8px !important;\n    }\n  }\n\n  @media (max-width: 480px) {\n    padding: 8px 12px;\n\n    & > div {\n      font-size: 10px;\n    }\n  }\n`;\n\nconst Logo = styled.h1`\n  margin: 0;\n  color: #4bffb5;\n  font-size: 24px;\n  font-weight: 700;\n`;\n\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  color: #fff;\n  font-size: 14px;\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  color: white;\n  font-size: 12px;\n`;\n\nconst ProfileButton = styled.button`\n  background: #4bffb5;\n  color: #000;\n  border: none;\n  padding: 6px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-right: 8px;\n\n  &:hover {\n    background: #3de89f;\n    transform: translateY(-1px);\n  }\n`;\n\nconst LogoutButton = styled.button`\n  background: #ff4976;\n  color: white;\n  border: none;\n  padding: 6px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #e63946;\n    transform: translateY(-1px);\n  }\n`;\n\nconst Modal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 20px;\n`;\n\nconst StatusBadge = styled.div`\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n  background: ${props => props.$connected ? '#4bffb5' : '#ff4976'};\n  color: ${props => props.$connected ? '#000' : '#fff'};\n`;\n\nconst MainContent = styled.main`\n  padding: 24px;\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  grid-template-rows: auto auto auto auto;\n  gap: 24px;\n  max-width: 1600px;\n  margin: 0 auto;\n\n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr;\n  }\n\n  @media (max-width: 768px) {\n    padding: 16px;\n    gap: 16px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 12px;\n    gap: 12px;\n  }\n`;\n\nconst ChartSection = styled.section`\n  grid-column: 1;\n  grid-row: 1 / 3;\n\n  @media (max-width: 1200px) {\n    grid-column: 1;\n    grid-row: 1;\n  }\n\n  @media (max-width: 768px) {\n    display: ${props => props.mobileVisible ? 'block' : 'none'};\n    margin-bottom: 60px; /* Space for mobile navigation */\n  }\n`;\n\nconst SidePanel = styled.section`\n  grid-column: 2;\n  grid-row: 1;\n\n  @media (max-width: 1200px) {\n    grid-column: 1;\n    grid-row: 2;\n  }\n\n  @media (max-width: 768px) {\n    display: none; /* Hidden on mobile, content shown in modals */\n  }\n`;\n\nconst BottomPanels = styled.section`\n  grid-column: 1 / -1;\n  grid-row: 3;\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;\n  gap: 24px;\n\n  @media (max-width: 1400px) {\n    grid-template-columns: 1fr 1fr 1fr;\n  }\n\n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media (max-width: 900px) {\n    grid-template-columns: 1fr;\n  }\n\n  @media (max-width: 768px) {\n    display: none; /* Hidden on mobile, content shown in modals */\n  }\n`;\n\nconst RiskSection = styled.section`\n  grid-column: 1 / -1;\n  grid-row: 4;\n  margin-top: 24px;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24px;\n\n  @media (max-width: 1200px) {\n    grid-template-columns: 1fr;\n  }\n\n  @media (max-width: 768px) {\n    display: none; /* Hidden on mobile, content shown in modals */\n  }\n`;\n\n// Loading component\nconst LoadingScreen = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #0a0a0a;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n\n// Main Dashboard Component (requires authentication)\nfunction Dashboard() {\n  const { user, logout } = useAuth();\n  const [wsConnected, setWsConnected] = useState(false);\n  const [showProfile, setShowProfile] = useState(false);\n\n  // Add global error handler\n  useEffect(() => {\n    const handleError = (event) => {\n      console.error('🚨 Global error caught:', event.error);\n    };\n\n    const handleUnhandledRejection = (event) => {\n      console.error('🚨 Unhandled promise rejection:', event.reason);\n    };\n\n    window.addEventListener('error', handleError);\n    window.addEventListener('unhandledrejection', handleUnhandledRejection);\n\n    return () => {\n      window.removeEventListener('error', handleError);\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection);\n    };\n  }, []);\n  const [marketData, setMarketData] = useState([]);\n  const [portfolio, setPortfolio] = useState({ total_value: 0, unrealized_pnl: 0, positions: [] });\n  const [signals, setSignals] = useState([]);\n  const [performance, setPerformance] = useState({ total_return: 0, win_rate: 0, total_trades: 0 });\n  const [tradingActive, setTradingActive] = useState(false);\n  const [apiStatus, setApiStatus] = useState('mock');\n  const [dataSource, setDataSource] = useState('mock');\n  const [lastDataUpdate, setLastDataUpdate] = useState(new Date());\n\n\n  useEffect(() => {\n    // Initialize WebSocket connection\n    const ws = new WebSocket('ws://localhost:8000/ws');\n    \n    ws.onopen = () => {\n      console.log('WebSocket connected');\n      setWsConnected(true);\n      \n      // Subscribe to updates\n      ws.send(JSON.stringify({\n        type: 'subscribe',\n        channels: ['market_data', 'portfolio', 'signals']\n      }));\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.type === 'market_update') {\n          const { data } = message;\n\n          // Update market data with proper candlestick data\n        if (data.candlestick) {\n          setMarketData(prev => {\n            const newData = {\n              timestamp: data.candlestick.timestamp,\n              open: data.candlestick.open,\n              high: data.candlestick.high,\n              low: data.candlestick.low,\n              close: data.candlestick.close,\n              volume: data.candlestick.volume || 0\n            };\n\n            const updated = [...prev, newData];\n            return updated.slice(-1000); // Keep last 1000 points\n          });\n        }\n        \n        // Update portfolio\n        if (data.portfolio) {\n          setPortfolio(data.portfolio);\n        }\n        \n          // Update signals\n          if (data.recent_signals) {\n            setSignals(data.recent_signals);\n          }\n\n          // Update last data timestamp\n          setLastDataUpdate(new Date());\n          console.log('📡 WebSocket data received at', new Date().toLocaleTimeString());\n        }\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket disconnected');\n      setWsConnected(false);\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket error:', error);\n      setWsConnected(false);\n    };\n    \n    // Cleanup\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  useEffect(() => {\n    // Fetch initial data\n    const fetchInitialData = async () => {\n      try {\n        // Fetch market data\n        const marketResponse = await fetch('http://localhost:8000/market-data?limit=200');\n        if (marketResponse.ok) {\n          const marketDataResult = await marketResponse.json();\n          setMarketData(marketDataResult);\n        }\n\n        // Fetch portfolio\n        const portfolioResponse = await fetch('http://localhost:8000/portfolio');\n        if (portfolioResponse.ok) {\n          const portfolioResult = await portfolioResponse.json();\n          setPortfolio(portfolioResult);\n        }\n\n        // Fetch signals\n        const signalsResponse = await fetch('http://localhost:8000/signals?limit=10');\n        if (signalsResponse.ok) {\n          const signalsResult = await signalsResponse.json();\n          setSignals(signalsResult);\n        }\n\n        // Fetch performance\n        const performanceResponse = await fetch('http://localhost:8000/performance');\n        if (performanceResponse.ok) {\n          const performanceResult = await performanceResponse.json();\n          setPerformance(performanceResult);\n        }\n\n        // Fetch data source status\n        const statusResponse = await fetch('http://localhost:8000/api/data-source-status');\n        if (statusResponse.ok) {\n          const statusResult = await statusResponse.json();\n          setDataSource(statusResult.data_source);\n          setTradingActive(statusResult.trading_active);\n          setApiStatus(statusResult.api_configured ? 'connected' : 'mock');\n        }\n\n      } catch (error) {\n        console.error('Error fetching initial data:', error);\n      }\n    };\n    \n    fetchInitialData();\n\n    // Set up periodic status check\n    const statusInterval = setInterval(async () => {\n      try {\n        const statusResponse = await fetch('http://localhost:8000/api/data-source-status');\n        if (statusResponse.ok) {\n          const statusResult = await statusResponse.json();\n          setDataSource(statusResult.data_source);\n          setTradingActive(statusResult.trading_active);\n        }\n      } catch (error) {\n        console.error('Error fetching status:', error);\n      }\n    }, 5000); // Check every 5 seconds\n\n    // Set up real-time data polling as fallback to WebSocket (only when WebSocket is disconnected)\n    const dataPollingInterval = setInterval(async () => {\n      try {\n        // Only poll if WebSocket is disconnected to avoid conflicts\n        if (!wsConnected) {\n          // Fetch latest market data\n          const marketResponse = await fetch('http://localhost:8000/market-data?limit=50&source=live');\n          if (marketResponse.ok) {\n            const marketDataResult = await marketResponse.json();\n            setMarketData(prev => {\n              // Only update if we have new data\n              if (Array.isArray(marketDataResult) && marketDataResult.length > 0) {\n                const latestTimestamp = marketDataResult[marketDataResult.length - 1]?.timestamp;\n                const currentLatest = Array.isArray(prev) && prev.length > 0 ? prev[prev.length - 1]?.timestamp : null;\n\n                if (latestTimestamp && latestTimestamp !== currentLatest) {\n                  console.log('Updating market data via polling (WebSocket disconnected)');\n                  return marketDataResult;\n                }\n              }\n              return prev || [];\n            });\n          }\n        }\n\n        // Fetch latest signals\n        const signalsResponse = await fetch('http://localhost:8000/signals?limit=10');\n        if (signalsResponse.ok) {\n          const signalsResult = await signalsResponse.json();\n          setSignals(prev => {\n            // Only update if we have new signals\n            if (Array.isArray(signalsResult) && signalsResult.length > 0) {\n              const latestTimestamp = signalsResult[0]?.timestamp;\n              const currentLatest = Array.isArray(prev) && prev.length > 0 ? prev[0]?.timestamp : null;\n\n              if (latestTimestamp && latestTimestamp !== currentLatest) {\n                console.log('Updating signals via polling');\n                return signalsResult;\n              }\n            }\n            return prev || [];\n          });\n        }\n\n        // Fetch portfolio\n        const portfolioResponse = await fetch('http://localhost:8000/portfolio');\n        if (portfolioResponse.ok) {\n          const portfolioResult = await portfolioResponse.json();\n          if (portfolioResult && typeof portfolioResult === 'object') {\n            setPortfolio(portfolioResult);\n          }\n        }\n\n      } catch (error) {\n        console.error('Error in data polling:', error);\n      }\n    }, 2000); // Poll every 2 seconds for real-time feel\n\n    return () => {\n      clearInterval(statusInterval);\n      clearInterval(dataPollingInterval);\n    };\n  }, []);\n\n  const handleStartTrading = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/trading/start', { method: 'POST' });\n      if (response.ok) {\n        setTradingActive(true);\n      }\n    } catch (error) {\n      console.error('Error starting trading:', error);\n    }\n  };\n\n  const handleStopTrading = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/trading/stop', { method: 'POST' });\n      if (response.ok) {\n        setTradingActive(false);\n      }\n    } catch (error) {\n      console.error('Error stopping trading:', error);\n    }\n  };\n\n  const handleApiUpdate = (apiConfig) => {\n    console.log('API configuration updated:', apiConfig);\n    setApiStatus('connected');\n    // Optionally refresh data after API update\n    // fetchInitialData();\n  };\n\n  const handleDataSourceSwitch = async (newDataSource) => {\n    console.log('Data source switched to:', newDataSource);\n    setDataSource(newDataSource);\n\n    // Update API status based on data source\n    if (newDataSource === 'live') {\n      setApiStatus('live');\n    } else if (newDataSource === 'mock') {\n      setApiStatus('mock');\n    }\n\n    // Force refresh of data after switching\n    try {\n      const statusResponse = await fetch('http://localhost:8000/api/data-source-status');\n      if (statusResponse.ok) {\n        const statusResult = await statusResponse.json();\n        setDataSource(statusResult.data_source);\n        setTradingActive(statusResult.trading_active);\n        setApiStatus(statusResult.api_configured && statusResult.data_source === 'live' ? 'live' : statusResult.data_source);\n      }\n    } catch (error) {\n      console.error('Error refreshing status after switch:', error);\n    }\n  };\n\n  return (\n    <ErrorBoundary>\n      <AppContainer>\n        <Header>\n        <Logo>AutoTradz AI</Logo>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n          <div style={{\n            fontSize: '12px',\n            color: '#888',\n            fontFamily: 'monospace'\n          }}>\n            Last Update: {lastDataUpdate.toLocaleTimeString()}\n          </div>\n          <div style={{\n            fontSize: '12px',\n            padding: '4px 8px',\n            borderRadius: '12px',\n            background: dataSource === 'live' ? 'rgba(75, 255, 181, 0.1)' : 'rgba(75, 181, 255, 0.1)',\n            border: `1px solid ${dataSource === 'live' ? 'rgba(75, 255, 181, 0.3)' : 'rgba(75, 181, 255, 0.3)'}`,\n            color: dataSource === 'live' ? '#4bffb5' : '#4bb5ff',\n            fontWeight: '600'\n          }}>\n            {dataSource === 'live' ? '🔴 LIVE' : '🔵 WEBSOCKET'} DATA\n          </div>\n          <StatusBadge $connected={wsConnected}>\n            {wsConnected ? 'Connected' : 'Disconnected'}\n          </StatusBadge>\n\n          {/* User Section */}\n          <UserSection>\n            <UserAvatar>\n              {user?.username?.charAt(0)?.toUpperCase() || 'U'}\n            </UserAvatar>\n            <span>Welcome, {user?.username || 'User'}</span>\n            <ProfileButton onClick={() => setShowProfile(true)}>\n              Profile\n            </ProfileButton>\n            <LogoutButton onClick={logout}>\n              Logout\n            </LogoutButton>\n          </UserSection>\n        </div>\n      </Header>\n      \n      <CleanDashboard\n        marketData={marketData}\n        portfolio={portfolio}\n        signals={signals}\n        performance={performance}\n        tradingActive={tradingActive}\n        dataSource={dataSource}\n        wsConnected={wsConnected}\n        onStartTrading={handleStartTrading}\n        onStopTrading={handleStopTrading}\n        onApiUpdate={handleApiUpdate}\n        onDataSourceSwitch={handleDataSourceSwitch}\n      />\n\n      {/* Profile Modal */}\n      {showProfile && (\n        <Modal onClick={() => setShowProfile(false)}>\n          <div onClick={(e) => e.stopPropagation()}>\n            <UserProfile onClose={() => setShowProfile(false)} />\n          </div>\n        </Modal>\n      )}\n      </AppContainer>\n    </ErrorBoundary>\n  );\n}\n\n// Main App Component with Authentication\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\n// App Content Component (handles routing based on auth state)\nfunction AppContent() {\n  const { isAuthenticated, loading, login } = useAuth();\n\n  if (loading) {\n    return (\n      <LoadingScreen>\n        <div>Loading AutoTradz AI...</div>\n      </LoadingScreen>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return <LoginPage onLogin={login} />;\n  }\n\n  return <Dashboard />;\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,YAAY,EAAEC,OAAO,QAAQ,eAAe;AACrD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,YAAY,GAAGT,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAXIF,YAAY;AAalB,MAAMG,MAAM,GAAGZ,MAAM,CAACa,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,MAAM;AA6BZ,MAAMG,IAAI,GAAGf,MAAM,CAACgB,EAAE;AACtB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,IAAI;AAOV,MAAMG,WAAW,GAAGlB,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GANID,WAAW;AAQjB,MAAME,UAAU,GAAGpB,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAXID,UAAU;AAahB,MAAME,aAAa,GAAGtB,MAAM,CAACuB,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,aAAa;AAiBnB,MAAMG,YAAY,GAAGzB,MAAM,CAACuB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAdID,YAAY;AAgBlB,MAAME,KAAK,GAAG3B,MAAM,CAACU,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GAZID,KAAK;AAcX,MAAME,WAAW,GAAG7B,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA,gBAAgBoB,KAAK,IAAIA,KAAK,CAACC,UAAU,GAAG,SAAS,GAAG,SAAS;AACjE,WAAWD,KAAK,IAAIA,KAAK,CAACC,UAAU,GAAG,MAAM,GAAG,MAAM;AACtD,CAAC;AAACC,GAAA,GAPIH,WAAW;AASjB,MAAMI,WAAW,GAAGjC,MAAM,CAACkC,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGnC,MAAM,CAACoC,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeN,KAAK,IAAIA,KAAK,CAACO,aAAa,GAAG,OAAO,GAAG,MAAM;AAC9D;AACA;AACA,CAAC;AAED,MAAMC,SAAS,GAAGtC,MAAM,CAACoC,OAAO;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMG,YAAY,GAAGvC,MAAM,CAACoC,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMI,WAAW,GAAGxC,MAAM,CAACoC,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMK,aAAa,GAAGzC,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAgC,GAAA,GAVMD,aAAa;AAWnB,SAASE,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,qBAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAG9C,OAAO,CAAC,CAAC;EAClC,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsD,WAAW,GAAIC,KAAK,IAAK;MAC7BC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEF,KAAK,CAACE,KAAK,CAAC;IACvD,CAAC;IAED,MAAMC,wBAAwB,GAAIH,KAAK,IAAK;MAC1CC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEF,KAAK,CAACI,MAAM,CAAC;IAChE,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEP,WAAW,CAAC;IAC7CM,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAEH,wBAAwB,CAAC;IAEvE,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAER,WAAW,CAAC;MAChDM,MAAM,CAACE,mBAAmB,CAAC,oBAAoB,EAAEJ,wBAAwB,CAAC;IAC5E,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC;IAAEoE,WAAW,EAAE,CAAC;IAAEC,cAAc,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAG,CAAC,CAAC;EAChG,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC;IAAE2E,YAAY,EAAE,CAAC;IAAEC,QAAQ,EAAE,CAAC;IAAEC,YAAY,EAAE;EAAE,CAAC,CAAC;EACjG,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,MAAM,CAAC;EACpD,MAAM,CAACoF,cAAc,EAAEC,iBAAiB,CAAC,GAAGrF,QAAQ,CAAC,IAAIsF,IAAI,CAAC,CAAC,CAAC;EAGhErF,SAAS,CAAC,MAAM;IACd;IACA,MAAMsF,EAAE,GAAG,IAAIC,SAAS,CAAC,wBAAwB,CAAC;IAElDD,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBhC,OAAO,CAACiC,GAAG,CAAC,qBAAqB,CAAC;MAClCtC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACAmC,EAAE,CAACI,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,IAAI,EAAE,WAAW;QACjBC,QAAQ,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,SAAS;MAClD,CAAC,CAAC,CAAC;IACL,CAAC;IAEDR,EAAE,CAACS,SAAS,GAAIxC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMyC,OAAO,GAAGL,IAAI,CAACM,KAAK,CAAC1C,KAAK,CAAC2C,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACH,IAAI,KAAK,eAAe,EAAE;UACpC,MAAM;YAAEK;UAAK,CAAC,GAAGF,OAAO;;UAExB;UACF,IAAIE,IAAI,CAACC,WAAW,EAAE;YACpBnC,aAAa,CAACoC,IAAI,IAAI;cACpB,MAAMC,OAAO,GAAG;gBACdC,SAAS,EAAEJ,IAAI,CAACC,WAAW,CAACG,SAAS;gBACrCC,IAAI,EAAEL,IAAI,CAACC,WAAW,CAACI,IAAI;gBAC3BC,IAAI,EAAEN,IAAI,CAACC,WAAW,CAACK,IAAI;gBAC3BC,GAAG,EAAEP,IAAI,CAACC,WAAW,CAACM,GAAG;gBACzBC,KAAK,EAAER,IAAI,CAACC,WAAW,CAACO,KAAK;gBAC7BC,MAAM,EAAET,IAAI,CAACC,WAAW,CAACQ,MAAM,IAAI;cACrC,CAAC;cAED,MAAMC,OAAO,GAAG,CAAC,GAAGR,IAAI,EAAEC,OAAO,CAAC;cAClC,OAAOO,OAAO,CAACC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC;UACJ;;UAEA;UACA,IAAIX,IAAI,CAACjC,SAAS,EAAE;YAClBC,YAAY,CAACgC,IAAI,CAACjC,SAAS,CAAC;UAC9B;;UAEE;UACA,IAAIiC,IAAI,CAACY,cAAc,EAAE;YACvBvC,UAAU,CAAC2B,IAAI,CAACY,cAAc,CAAC;UACjC;;UAEA;UACA1B,iBAAiB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;UAC7B7B,OAAO,CAACiC,GAAG,CAAC,+BAA+B,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAAC0B,kBAAkB,CAAC,CAAC,CAAC;QAC/E;MACF,CAAC,CAAC,OAAOtD,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAED6B,EAAE,CAAC0B,OAAO,GAAG,MAAM;MACjBxD,OAAO,CAACiC,GAAG,CAAC,wBAAwB,CAAC;MACrCtC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IAEDmC,EAAE,CAAC2B,OAAO,GAAIxD,KAAK,IAAK;MACtBD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCN,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;;IAED;IACA,OAAO,MAAM;MACXmC,EAAE,CAACoB,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN1G,SAAS,CAAC,MAAM;IACd;IACA,MAAMkH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF;QACA,MAAMC,cAAc,GAAG,MAAMC,KAAK,CAAC,6CAA6C,CAAC;QACjF,IAAID,cAAc,CAACE,EAAE,EAAE;UACrB,MAAMC,gBAAgB,GAAG,MAAMH,cAAc,CAACI,IAAI,CAAC,CAAC;UACpDvD,aAAa,CAACsD,gBAAgB,CAAC;QACjC;;QAEA;QACA,MAAME,iBAAiB,GAAG,MAAMJ,KAAK,CAAC,iCAAiC,CAAC;QACxE,IAAII,iBAAiB,CAACH,EAAE,EAAE;UACxB,MAAMI,eAAe,GAAG,MAAMD,iBAAiB,CAACD,IAAI,CAAC,CAAC;UACtDrD,YAAY,CAACuD,eAAe,CAAC;QAC/B;;QAEA;QACA,MAAMC,eAAe,GAAG,MAAMN,KAAK,CAAC,wCAAwC,CAAC;QAC7E,IAAIM,eAAe,CAACL,EAAE,EAAE;UACtB,MAAMM,aAAa,GAAG,MAAMD,eAAe,CAACH,IAAI,CAAC,CAAC;UAClDhD,UAAU,CAACoD,aAAa,CAAC;QAC3B;;QAEA;QACA,MAAMC,mBAAmB,GAAG,MAAMR,KAAK,CAAC,mCAAmC,CAAC;QAC5E,IAAIQ,mBAAmB,CAACP,EAAE,EAAE;UAC1B,MAAMQ,iBAAiB,GAAG,MAAMD,mBAAmB,CAACL,IAAI,CAAC,CAAC;UAC1D9C,cAAc,CAACoD,iBAAiB,CAAC;QACnC;;QAEA;QACA,MAAMC,cAAc,GAAG,MAAMV,KAAK,CAAC,8CAA8C,CAAC;QAClF,IAAIU,cAAc,CAACT,EAAE,EAAE;UACrB,MAAMU,YAAY,GAAG,MAAMD,cAAc,CAACP,IAAI,CAAC,CAAC;UAChDrC,aAAa,CAAC6C,YAAY,CAACC,WAAW,CAAC;UACvClD,gBAAgB,CAACiD,YAAY,CAACE,cAAc,CAAC;UAC7CjD,YAAY,CAAC+C,YAAY,CAACG,cAAc,GAAG,WAAW,GAAG,MAAM,CAAC;QAClE;MAEF,CAAC,CAAC,OAAOzE,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDyD,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMiB,cAAc,GAAGC,WAAW,CAAC,YAAY;MAC7C,IAAI;QACF,MAAMN,cAAc,GAAG,MAAMV,KAAK,CAAC,8CAA8C,CAAC;QAClF,IAAIU,cAAc,CAACT,EAAE,EAAE;UACrB,MAAMU,YAAY,GAAG,MAAMD,cAAc,CAACP,IAAI,CAAC,CAAC;UAChDrC,aAAa,CAAC6C,YAAY,CAACC,WAAW,CAAC;UACvClD,gBAAgB,CAACiD,YAAY,CAACE,cAAc,CAAC;QAC/C;MACF,CAAC,CAAC,OAAOxE,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACA,MAAM4E,mBAAmB,GAAGD,WAAW,CAAC,YAAY;MAClD,IAAI;QACF;QACA,IAAI,CAAClF,WAAW,EAAE;UAChB;UACA,MAAMiE,cAAc,GAAG,MAAMC,KAAK,CAAC,wDAAwD,CAAC;UAC5F,IAAID,cAAc,CAACE,EAAE,EAAE;YACrB,MAAMC,gBAAgB,GAAG,MAAMH,cAAc,CAACI,IAAI,CAAC,CAAC;YACpDvD,aAAa,CAACoC,IAAI,IAAI;cACpB;cACA,IAAIkC,KAAK,CAACC,OAAO,CAACjB,gBAAgB,CAAC,IAAIA,gBAAgB,CAACkB,MAAM,GAAG,CAAC,EAAE;gBAAA,IAAAC,iBAAA,EAAAC,KAAA;gBAClE,MAAMC,eAAe,IAAAF,iBAAA,GAAGnB,gBAAgB,CAACA,gBAAgB,CAACkB,MAAM,GAAG,CAAC,CAAC,cAAAC,iBAAA,uBAA7CA,iBAAA,CAA+CnC,SAAS;gBAChF,MAAMsC,aAAa,GAAGN,KAAK,CAACC,OAAO,CAACnC,IAAI,CAAC,IAAIA,IAAI,CAACoC,MAAM,GAAG,CAAC,IAAAE,KAAA,GAAGtC,IAAI,CAACA,IAAI,CAACoC,MAAM,GAAG,CAAC,CAAC,cAAAE,KAAA,uBAArBA,KAAA,CAAuBpC,SAAS,GAAG,IAAI;gBAEtG,IAAIqC,eAAe,IAAIA,eAAe,KAAKC,aAAa,EAAE;kBACxDpF,OAAO,CAACiC,GAAG,CAAC,2DAA2D,CAAC;kBACxE,OAAO6B,gBAAgB;gBACzB;cACF;cACA,OAAOlB,IAAI,IAAI,EAAE;YACnB,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,MAAMsB,eAAe,GAAG,MAAMN,KAAK,CAAC,wCAAwC,CAAC;QAC7E,IAAIM,eAAe,CAACL,EAAE,EAAE;UACtB,MAAMM,aAAa,GAAG,MAAMD,eAAe,CAACH,IAAI,CAAC,CAAC;UAClDhD,UAAU,CAAC6B,IAAI,IAAI;YACjB;YACA,IAAIkC,KAAK,CAACC,OAAO,CAACZ,aAAa,CAAC,IAAIA,aAAa,CAACa,MAAM,GAAG,CAAC,EAAE;cAAA,IAAAK,eAAA,EAAAC,MAAA;cAC5D,MAAMH,eAAe,IAAAE,eAAA,GAAGlB,aAAa,CAAC,CAAC,CAAC,cAAAkB,eAAA,uBAAhBA,eAAA,CAAkBvC,SAAS;cACnD,MAAMsC,aAAa,GAAGN,KAAK,CAACC,OAAO,CAACnC,IAAI,CAAC,IAAIA,IAAI,CAACoC,MAAM,GAAG,CAAC,IAAAM,MAAA,GAAG1C,IAAI,CAAC,CAAC,CAAC,cAAA0C,MAAA,uBAAPA,MAAA,CAASxC,SAAS,GAAG,IAAI;cAExF,IAAIqC,eAAe,IAAIA,eAAe,KAAKC,aAAa,EAAE;gBACxDpF,OAAO,CAACiC,GAAG,CAAC,8BAA8B,CAAC;gBAC3C,OAAOkC,aAAa;cACtB;YACF;YACA,OAAOvB,IAAI,IAAI,EAAE;UACnB,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMoB,iBAAiB,GAAG,MAAMJ,KAAK,CAAC,iCAAiC,CAAC;QACxE,IAAII,iBAAiB,CAACH,EAAE,EAAE;UACxB,MAAMI,eAAe,GAAG,MAAMD,iBAAiB,CAACD,IAAI,CAAC,CAAC;UACtD,IAAIE,eAAe,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;YAC1DvD,YAAY,CAACuD,eAAe,CAAC;UAC/B;QACF;MAEF,CAAC,CAAC,OAAOhE,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAM;MACXsF,aAAa,CAACZ,cAAc,CAAC;MAC7BY,aAAa,CAACV,mBAAmB,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7B,KAAK,CAAC,qCAAqC,EAAE;QAAE8B,MAAM,EAAE;MAAO,CAAC,CAAC;MACvF,IAAID,QAAQ,CAAC5B,EAAE,EAAE;QACfvC,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM0F,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMF,QAAQ,GAAG,MAAM7B,KAAK,CAAC,oCAAoC,EAAE;QAAE8B,MAAM,EAAE;MAAO,CAAC,CAAC;MACtF,IAAID,QAAQ,CAAC5B,EAAE,EAAE;QACfvC,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM2F,eAAe,GAAIC,SAAS,IAAK;IACrC7F,OAAO,CAACiC,GAAG,CAAC,4BAA4B,EAAE4D,SAAS,CAAC;IACpDrE,YAAY,CAAC,WAAW,CAAC;IACzB;IACA;EACF,CAAC;EAED,MAAMsE,sBAAsB,GAAG,MAAOC,aAAa,IAAK;IACtD/F,OAAO,CAACiC,GAAG,CAAC,0BAA0B,EAAE8D,aAAa,CAAC;IACtDrE,aAAa,CAACqE,aAAa,CAAC;;IAE5B;IACA,IAAIA,aAAa,KAAK,MAAM,EAAE;MAC5BvE,YAAY,CAAC,MAAM,CAAC;IACtB,CAAC,MAAM,IAAIuE,aAAa,KAAK,MAAM,EAAE;MACnCvE,YAAY,CAAC,MAAM,CAAC;IACtB;;IAEA;IACA,IAAI;MACF,MAAM8C,cAAc,GAAG,MAAMV,KAAK,CAAC,8CAA8C,CAAC;MAClF,IAAIU,cAAc,CAACT,EAAE,EAAE;QACrB,MAAMU,YAAY,GAAG,MAAMD,cAAc,CAACP,IAAI,CAAC,CAAC;QAChDrC,aAAa,CAAC6C,YAAY,CAACC,WAAW,CAAC;QACvClD,gBAAgB,CAACiD,YAAY,CAACE,cAAc,CAAC;QAC7CjD,YAAY,CAAC+C,YAAY,CAACG,cAAc,IAAIH,YAAY,CAACC,WAAW,KAAK,MAAM,GAAG,MAAM,GAAGD,YAAY,CAACC,WAAW,CAAC;MACtH;IACF,CAAC,CAAC,OAAOvE,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC;EAED,oBACEhD,OAAA,CAACF,aAAa;IAAAiJ,QAAA,eACZ/I,OAAA,CAACC,YAAY;MAAA8I,QAAA,gBACX/I,OAAA,CAACI,MAAM;QAAA2I,QAAA,gBACP/I,OAAA,CAACO,IAAI;UAAAwI,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzBnJ,OAAA;UAAKoJ,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACjE/I,OAAA;YAAKoJ,KAAK,EAAE;cACVI,QAAQ,EAAE,MAAM;cAChBC,KAAK,EAAE,MAAM;cACbC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,GAAC,eACY,EAACrE,cAAc,CAAC4B,kBAAkB,CAAC,CAAC;UAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNnJ,OAAA;YAAKoJ,KAAK,EAAE;cACVI,QAAQ,EAAE,MAAM;cAChBG,OAAO,EAAE,SAAS;cAClBC,YAAY,EAAE,MAAM;cACpBC,UAAU,EAAErF,UAAU,KAAK,MAAM,GAAG,yBAAyB,GAAG,yBAAyB;cACzFsF,MAAM,EAAE,aAAatF,UAAU,KAAK,MAAM,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;cACpGiF,KAAK,EAAEjF,UAAU,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;cACpDuF,UAAU,EAAE;YACd,CAAE;YAAAhB,QAAA,GACCvE,UAAU,KAAK,MAAM,GAAG,SAAS,GAAG,cAAc,EAAC,OACtD;UAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnJ,OAAA,CAACqB,WAAW;YAACE,UAAU,EAAEkB,WAAY;YAAAsG,QAAA,EAClCtG,WAAW,GAAG,WAAW,GAAG;UAAc;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAGdnJ,OAAA,CAACU,WAAW;YAAAqI,QAAA,gBACV/I,OAAA,CAACY,UAAU;cAAAmI,QAAA,EACR,CAAAxG,IAAI,aAAJA,IAAI,wBAAAF,cAAA,GAAJE,IAAI,CAAEyH,QAAQ,cAAA3H,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgB4H,MAAM,CAAC,CAAC,CAAC,cAAA3H,qBAAA,uBAAzBA,qBAAA,CAA2B4H,WAAW,CAAC,CAAC,KAAI;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACbnJ,OAAA;cAAA+I,QAAA,GAAM,WAAS,EAAC,CAAAxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyH,QAAQ,KAAI,MAAM;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDnJ,OAAA,CAACc,aAAa;cAACqJ,OAAO,EAAEA,CAAA,KAAMvH,cAAc,CAAC,IAAI,CAAE;cAAAmG,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBnJ,OAAA,CAACiB,YAAY;cAACkJ,OAAO,EAAE3H,MAAO;cAAAuG,QAAA,EAAC;YAE/B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAETnJ,OAAA,CAACJ,cAAc;QACb0D,UAAU,EAAEA,UAAW;QACvBE,SAAS,EAAEA,SAAU;QACrBK,OAAO,EAAEA,OAAQ;QACjBE,WAAW,EAAEA,WAAY;QACzBK,aAAa,EAAEA,aAAc;QAC7BI,UAAU,EAAEA,UAAW;QACvB/B,WAAW,EAAEA,WAAY;QACzB2H,cAAc,EAAE7B,kBAAmB;QACnC8B,aAAa,EAAE3B,iBAAkB;QACjC4B,WAAW,EAAE3B,eAAgB;QAC7B4B,kBAAkB,EAAE1B;MAAuB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,EAGDxG,WAAW,iBACV3C,OAAA,CAACmB,KAAK;QAACgJ,OAAO,EAAEA,CAAA,KAAMvH,cAAc,CAAC,KAAK,CAAE;QAAAmG,QAAA,eAC1C/I,OAAA;UAAKmK,OAAO,EAAGK,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;UAAA1B,QAAA,eACvC/I,OAAA,CAACH,WAAW;YAAC6K,OAAO,EAAEA,CAAA,KAAM9H,cAAc,CAAC,KAAK;UAAE;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;;AAEA;AAAA/G,EAAA,CAtWSD,SAAS;EAAA,QACSzC,OAAO;AAAA;AAAAiL,GAAA,GADzBxI,SAAS;AAuWlB,SAASyI,GAAGA,CAAA,EAAG;EACb,oBACE5K,OAAA,CAACP,YAAY;IAAAsJ,QAAA,eACX/I,OAAA,CAAC6K,UAAU;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;;AAEA;AAAA2B,IAAA,GARSF,GAAG;AASZ,SAASC,UAAUA,CAAA,EAAG;EAAAE,GAAA;EACpB,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGxL,OAAO,CAAC,CAAC;EAErD,IAAIuL,OAAO,EAAE;IACX,oBACEjL,OAAA,CAACiC,aAAa;MAAA8G,QAAA,eACZ/I,OAAA;QAAA+I,QAAA,EAAK;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAEpB;EAEA,IAAI,CAAC6B,eAAe,EAAE;IACpB,oBAAOhL,OAAA,CAACL,SAAS;MAACwL,OAAO,EAAED;IAAM;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtC;EAEA,oBAAOnJ,OAAA,CAACmC,SAAS;IAAA6G,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACtB;AAAC4B,GAAA,CAhBQF,UAAU;EAAA,QAC2BnL,OAAO;AAAA;AAAA0L,IAAA,GAD5CP,UAAU;AAkBnB,eAAeD,GAAG;AAAC,IAAAzK,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAU,GAAA,EAAAyI,GAAA,EAAAG,IAAA,EAAAM,IAAA;AAAAC,YAAA,CAAAlL,EAAA;AAAAkL,YAAA,CAAA/K,GAAA;AAAA+K,YAAA,CAAA5K,GAAA;AAAA4K,YAAA,CAAA1K,GAAA;AAAA0K,YAAA,CAAAxK,GAAA;AAAAwK,YAAA,CAAArK,GAAA;AAAAqK,YAAA,CAAAnK,GAAA;AAAAmK,YAAA,CAAAjK,GAAA;AAAAiK,YAAA,CAAA7J,GAAA;AAAA6J,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAP,IAAA;AAAAO,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}