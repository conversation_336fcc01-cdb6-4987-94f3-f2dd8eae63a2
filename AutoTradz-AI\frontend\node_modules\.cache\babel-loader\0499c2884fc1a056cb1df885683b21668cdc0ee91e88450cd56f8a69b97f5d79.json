{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\ControlPanel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n_c = PanelContainer;\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n_c2 = PanelHeader;\nconst ControlButton = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-bottom: 12px;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.start {\n    background: #4bffb5;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #3de89f;\n      transform: translateY(-1px);\n    }\n  }\n  \n  &.stop {\n    background: #ff4976;\n    color: #fff;\n    \n    &:hover:not(:disabled) {\n      background: #e63946;\n      transform: translateY(-1px);\n    }\n  }\n  \n  &.secondary {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n      border-color: #555;\n    }\n  }\n`;\n_c3 = ControlButton;\nconst StatusCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 16px;\n  border-left: 4px solid ${props => props.$active ? '#4bffb5' : '#666'};\n`;\n_c4 = StatusCard;\nconst StatusLabel = styled.div`\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n`;\n_c5 = StatusLabel;\nconst StatusValue = styled.div`\n  color: ${props => props.$active ? '#4bffb5' : '#888'};\n  font-size: 16px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c6 = StatusValue;\nconst StatusIndicator = styled.div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${props => props.$active ? '#4bffb5' : '#666'};\n  animation: ${props => props.$active ? 'pulse 2s infinite' : 'none'};\n\n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n`;\n_c7 = StatusIndicator;\nconst ConfigSection = styled.div`\n  margin-top: 20px;\n  padding-top: 16px;\n  border-top: 1px solid #333;\n`;\n_c8 = ConfigSection;\nconst ConfigLabel = styled.label`\n  display: block;\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n`;\n_c9 = ConfigLabel;\nconst ConfigInput = styled.input`\n  width: 100%;\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 12px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n  }\n`;\n_c0 = ConfigInput;\nconst ConfigSelect = styled.select`\n  width: 100%;\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 12px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n  }\n  \n  option {\n    background: #2a2a2a;\n    color: #fff;\n  }\n`;\n_c1 = ConfigSelect;\nconst ControlPanel = ({\n  tradingActive,\n  onStart,\n  onStop\n}) => {\n  _s();\n  const [isLoading, setIsLoading] = useState(false);\n  const [configSaving, setConfigSaving] = useState(false);\n  const [fetchingData, setFetchingData] = useState(false);\n  // 🔥 FUNCTIONAL CONFIG: Load saved config from localStorage\n  const [config, setConfig] = useState(() => {\n    try {\n      const savedConfig = localStorage.getItem('autotradz_trade_config');\n      if (savedConfig) {\n        return JSON.parse(savedConfig);\n      }\n    } catch (error) {\n      console.error('Error loading saved trade config:', error);\n    }\n    return {\n      riskPerTrade: '2',\n      maxPositionSize: '10',\n      maxDrawdown: '15',\n      initialBalance: '10000',\n      strategy: 'TECHNICAL',\n      stopLoss: '5',\n      takeProfit: '10'\n    };\n  });\n  const handleStart = async () => {\n    setIsLoading(true);\n    try {\n      await onStart();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleStop = async () => {\n    setIsLoading(true);\n    try {\n      await onStop();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 🔥 FUNCTIONAL CONFIG: Save and apply configuration\n  const saveConfigToStorage = newConfig => {\n    try {\n      localStorage.setItem('autotradz_trade_config', JSON.stringify(newConfig));\n      console.log('💾 Saved trade config to localStorage');\n    } catch (error) {\n      console.error('Error saving trade config:', error);\n    }\n  };\n  const handleConfigChange = (key, value) => {\n    const newConfig = {\n      ...config,\n      [key]: value\n    };\n    setConfig(newConfig);\n    saveConfigToStorage(newConfig);\n  };\n\n  // 🔥 FUNCTIONAL CONFIG: Send configuration to backend\n  const applyConfiguration = async () => {\n    setConfigSaving(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/configure-trading', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          risk_per_trade: parseFloat(config.riskPerTrade) / 100,\n          max_position_size: parseFloat(config.maxPositionSize) / 100,\n          max_drawdown: parseFloat(config.maxDrawdown) / 100,\n          initial_balance: parseFloat(config.initialBalance),\n          stop_loss: parseFloat(config.stopLoss) / 100,\n          take_profit: parseFloat(config.takeProfit) / 100,\n          strategy: config.strategy\n        })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        alert('Trading configuration applied successfully!');\n        console.log('✅ Trading config applied:', result);\n      } else {\n        alert('Failed to apply trading configuration');\n      }\n    } catch (error) {\n      console.error('Error applying trading config:', error);\n      alert('Error applying trading configuration');\n    } finally {\n      setConfigSaving(false);\n    }\n  };\n\n  // 🚀 FETCH 5-YEAR HISTORICAL DATA\n  const fetch5YearData = async () => {\n    setFetchingData(true);\n    try {\n      console.log('🚀 Starting 5-year historical data fetch...');\n      const response = await fetch('http://localhost:8000/api/fetch-5year-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.ok) {\n        const result = await response.json();\n        alert(`✅ 5-Year Data Fetch Complete!\\n\\nSuccessful: ${result.successful}\\nFailed: ${result.failed}\\nTimeframes: ${result.timeframes.join(', ')}`);\n        console.log('✅ 5-year data fetch completed:', result);\n      } else {\n        const error = await response.json();\n        alert(`❌ Failed to fetch 5-year data: ${error.detail || 'Unknown error'}`);\n        console.error('❌ 5-year data fetch failed:', error);\n      }\n    } catch (error) {\n      console.error('Error fetching 5-year data:', error);\n      alert('❌ Error fetching 5-year historical data');\n    } finally {\n      setFetchingData(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n      children: \"Control Panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatusCard, {\n      $active: tradingActive,\n      children: [/*#__PURE__*/_jsxDEV(StatusLabel, {\n        children: \"Trading Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusValue, {\n        $active: tradingActive,\n        children: [/*#__PURE__*/_jsxDEV(StatusIndicator, {\n          $active: tradingActive\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), tradingActive ? 'Active' : 'Inactive']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatusCard, {\n      $active: true,\n      children: [/*#__PURE__*/_jsxDEV(StatusLabel, {\n        children: \"Mode\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusValue, {\n        $active: true,\n        children: [/*#__PURE__*/_jsxDEV(StatusIndicator, {\n          $active: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), \"Paper Trading\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), !tradingActive ? /*#__PURE__*/_jsxDEV(ControlButton, {\n      className: \"start\",\n      onClick: handleStart,\n      disabled: isLoading,\n      children: isLoading ? 'Starting...' : 'Start Trading'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(ControlButton, {\n      className: \"stop\",\n      onClick: handleStop,\n      disabled: isLoading,\n      children: isLoading ? 'Stopping...' : 'Stop Trading'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n      className: \"secondary\",\n      disabled: tradingActive,\n      children: \"Reset Portfolio\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n      className: \"secondary\",\n      children: \"Export Results\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n      className: \"secondary\",\n      onClick: fetch5YearData,\n      disabled: fetchingData,\n      style: {\n        background: fetchingData ? '#666' : '#1e3a8a',\n        color: '#fff',\n        border: '1px solid #3b82f6'\n      },\n      children: fetchingData ? '📥 Fetching 5-Year Data...' : '🚀 Fetch 5-Year Data'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfigSection, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#888',\n            fontSize: '14px',\n            margin: 0\n          },\n          children: \"Trading Configuration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n          className: \"secondary\",\n          onClick: applyConfiguration,\n          disabled: configSaving || tradingActive,\n          style: {\n            width: 'auto',\n            padding: '4px 8px',\n            fontSize: '11px',\n            marginBottom: 0,\n            background: '#4bffb5',\n            color: '#000'\n          },\n          children: configSaving ? 'Applying...' : '💾 Apply Config'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '8px',\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(ConfigLabel, {\n            children: \"Risk Per Trade (%)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConfigInput, {\n            type: \"number\",\n            min: \"0.1\",\n            max: \"10\",\n            step: \"0.1\",\n            value: config.riskPerTrade,\n            onChange: e => handleConfigChange('riskPerTrade', e.target.value),\n            disabled: tradingActive,\n            placeholder: \"2.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(ConfigLabel, {\n            children: \"Max Position (%)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConfigInput, {\n            type: \"number\",\n            min: \"1\",\n            max: \"50\",\n            step: \"1\",\n            value: config.maxPositionSize,\n            onChange: e => handleConfigChange('maxPositionSize', e.target.value),\n            disabled: tradingActive,\n            placeholder: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '8px',\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(ConfigLabel, {\n            children: \"Stop Loss (%)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConfigInput, {\n            type: \"number\",\n            min: \"1\",\n            max: \"20\",\n            step: \"0.5\",\n            value: config.stopLoss,\n            onChange: e => handleConfigChange('stopLoss', e.target.value),\n            disabled: tradingActive,\n            placeholder: \"5.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(ConfigLabel, {\n            children: \"Take Profit (%)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConfigInput, {\n            type: \"number\",\n            min: \"1\",\n            max: \"50\",\n            step: \"0.5\",\n            value: config.takeProfit,\n            onChange: e => handleConfigChange('takeProfit', e.target.value),\n            disabled: tradingActive,\n            placeholder: \"10.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '8px',\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(ConfigLabel, {\n            children: \"Max Drawdown (%)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConfigInput, {\n            type: \"number\",\n            min: \"5\",\n            max: \"50\",\n            step: \"1\",\n            value: config.maxDrawdown,\n            onChange: e => handleConfigChange('maxDrawdown', e.target.value),\n            disabled: tradingActive,\n            placeholder: \"15\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(ConfigLabel, {\n            children: \"Initial Balance ($)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConfigInput, {\n            type: \"number\",\n            min: \"1000\",\n            max: \"1000000\",\n            step: \"1000\",\n            value: config.initialBalance,\n            onChange: e => handleConfigChange('initialBalance', e.target.value),\n            disabled: tradingActive,\n            placeholder: \"10000\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfigLabel, {\n        children: \"Primary Strategy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfigSelect, {\n        value: config.strategy,\n        onChange: e => handleConfigChange('strategy', e.target.value),\n        disabled: tradingActive,\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"TECHNICAL\",\n          children: \"Technical Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"ML_ENSEMBLE\",\n          children: \"ML Ensemble\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"MOMENTUM\",\n          children: \"Momentum\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"MEAN_REVERSION\",\n          children: \"Mean Reversion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#666',\n          marginTop: '8px',\n          padding: '8px',\n          background: '#2a2a2a',\n          borderRadius: '4px',\n          lineHeight: '1.4'\n        },\n        children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Risk Management:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 14\n        }, this), \" Risk per trade limits position size based on account balance. Stop loss and take profit are automatically applied to all trades.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n};\n_s(ControlPanel, \"LkZVDynm7bJdxXCZsuN28nrV6LA=\");\n_c10 = ControlPanel;\nexport default ControlPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"PanelHeader\");\n$RefreshReg$(_c3, \"ControlButton\");\n$RefreshReg$(_c4, \"StatusCard\");\n$RefreshReg$(_c5, \"StatusLabel\");\n$RefreshReg$(_c6, \"StatusValue\");\n$RefreshReg$(_c7, \"StatusIndicator\");\n$RefreshReg$(_c8, \"ConfigSection\");\n$RefreshReg$(_c9, \"ConfigLabel\");\n$RefreshReg$(_c0, \"ConfigInput\");\n$RefreshReg$(_c1, \"ConfigSelect\");\n$RefreshReg$(_c10, \"ControlPanel\");", "map": {"version": 3, "names": ["React", "useState", "styled", "jsxDEV", "_jsxDEV", "PanelContainer", "div", "_c", "PanelHeader", "h3", "_c2", "ControlButton", "button", "_c3", "StatusCard", "props", "$active", "_c4", "StatusLabel", "_c5", "StatusValue", "_c6", "StatusIndicator", "_c7", "ConfigSection", "_c8", "Config<PERSON><PERSON><PERSON>", "label", "_c9", "ConfigInput", "input", "_c0", "ConfigSelect", "select", "_c1", "ControlPanel", "tradingActive", "onStart", "onStop", "_s", "isLoading", "setIsLoading", "configSaving", "setConfigSaving", "fetchingData", "setFetchingData", "config", "setConfig", "savedConfig", "localStorage", "getItem", "JSON", "parse", "error", "console", "riskPerTrade", "maxPositionSize", "maxDrawdown", "initialBalance", "strategy", "stopLoss", "takeProfit", "handleStart", "handleStop", "saveConfigToStorage", "newConfig", "setItem", "stringify", "log", "handleConfigChange", "key", "value", "applyConfiguration", "response", "fetch", "method", "headers", "body", "risk_per_trade", "parseFloat", "max_position_size", "max_drawdown", "initial_balance", "stop_loss", "take_profit", "ok", "result", "json", "alert", "fetch5YearData", "successful", "failed", "timeframes", "join", "detail", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "disabled", "style", "background", "color", "border", "display", "justifyContent", "alignItems", "marginBottom", "fontSize", "margin", "width", "padding", "gridTemplateColumns", "gap", "type", "min", "max", "step", "onChange", "e", "target", "placeholder", "marginTop", "borderRadius", "lineHeight", "_c10", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/ControlPanel.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\n\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n\nconst ControlButton = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-bottom: 12px;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.start {\n    background: #4bffb5;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #3de89f;\n      transform: translateY(-1px);\n    }\n  }\n  \n  &.stop {\n    background: #ff4976;\n    color: #fff;\n    \n    &:hover:not(:disabled) {\n      background: #e63946;\n      transform: translateY(-1px);\n    }\n  }\n  \n  &.secondary {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n      border-color: #555;\n    }\n  }\n`;\n\nconst StatusCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 16px;\n  border-left: 4px solid ${props => props.$active ? '#4bffb5' : '#666'};\n`;\n\nconst StatusLabel = styled.div`\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n`;\n\nconst StatusValue = styled.div`\n  color: ${props => props.$active ? '#4bffb5' : '#888'};\n  font-size: 16px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst StatusIndicator = styled.div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${props => props.$active ? '#4bffb5' : '#666'};\n  animation: ${props => props.$active ? 'pulse 2s infinite' : 'none'};\n\n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n`;\n\nconst ConfigSection = styled.div`\n  margin-top: 20px;\n  padding-top: 16px;\n  border-top: 1px solid #333;\n`;\n\nconst ConfigLabel = styled.label`\n  display: block;\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n`;\n\nconst ConfigInput = styled.input`\n  width: 100%;\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 12px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n  }\n`;\n\nconst ConfigSelect = styled.select`\n  width: 100%;\n  padding: 8px 12px;\n  background: #2a2a2a;\n  border: 1px solid #444;\n  border-radius: 4px;\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 12px;\n  \n  &:focus {\n    outline: none;\n    border-color: #4bffb5;\n  }\n  \n  option {\n    background: #2a2a2a;\n    color: #fff;\n  }\n`;\n\nconst ControlPanel = ({ tradingActive, onStart, onStop }) => {\n  const [isLoading, setIsLoading] = useState(false);\n  const [configSaving, setConfigSaving] = useState(false);\n  const [fetchingData, setFetchingData] = useState(false);\n  // 🔥 FUNCTIONAL CONFIG: Load saved config from localStorage\n  const [config, setConfig] = useState(() => {\n    try {\n      const savedConfig = localStorage.getItem('autotradz_trade_config');\n      if (savedConfig) {\n        return JSON.parse(savedConfig);\n      }\n    } catch (error) {\n      console.error('Error loading saved trade config:', error);\n    }\n    return {\n      riskPerTrade: '2',\n      maxPositionSize: '10',\n      maxDrawdown: '15',\n      initialBalance: '10000',\n      strategy: 'TECHNICAL',\n      stopLoss: '5',\n      takeProfit: '10'\n    };\n  });\n\n  const handleStart = async () => {\n    setIsLoading(true);\n    try {\n      await onStart();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleStop = async () => {\n    setIsLoading(true);\n    try {\n      await onStop();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 🔥 FUNCTIONAL CONFIG: Save and apply configuration\n  const saveConfigToStorage = (newConfig) => {\n    try {\n      localStorage.setItem('autotradz_trade_config', JSON.stringify(newConfig));\n      console.log('💾 Saved trade config to localStorage');\n    } catch (error) {\n      console.error('Error saving trade config:', error);\n    }\n  };\n\n  const handleConfigChange = (key, value) => {\n    const newConfig = { ...config, [key]: value };\n    setConfig(newConfig);\n    saveConfigToStorage(newConfig);\n  };\n\n  // 🔥 FUNCTIONAL CONFIG: Send configuration to backend\n  const applyConfiguration = async () => {\n    setConfigSaving(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/configure-trading', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          risk_per_trade: parseFloat(config.riskPerTrade) / 100,\n          max_position_size: parseFloat(config.maxPositionSize) / 100,\n          max_drawdown: parseFloat(config.maxDrawdown) / 100,\n          initial_balance: parseFloat(config.initialBalance),\n          stop_loss: parseFloat(config.stopLoss) / 100,\n          take_profit: parseFloat(config.takeProfit) / 100,\n          strategy: config.strategy\n        }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        alert('Trading configuration applied successfully!');\n        console.log('✅ Trading config applied:', result);\n      } else {\n        alert('Failed to apply trading configuration');\n      }\n    } catch (error) {\n      console.error('Error applying trading config:', error);\n      alert('Error applying trading configuration');\n    } finally {\n      setConfigSaving(false);\n    }\n  };\n\n  // 🚀 FETCH 5-YEAR HISTORICAL DATA\n  const fetch5YearData = async () => {\n    setFetchingData(true);\n    try {\n      console.log('🚀 Starting 5-year historical data fetch...');\n      const response = await fetch('http://localhost:8000/api/fetch-5year-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        alert(`✅ 5-Year Data Fetch Complete!\\n\\nSuccessful: ${result.successful}\\nFailed: ${result.failed}\\nTimeframes: ${result.timeframes.join(', ')}`);\n        console.log('✅ 5-year data fetch completed:', result);\n      } else {\n        const error = await response.json();\n        alert(`❌ Failed to fetch 5-year data: ${error.detail || 'Unknown error'}`);\n        console.error('❌ 5-year data fetch failed:', error);\n      }\n    } catch (error) {\n      console.error('Error fetching 5-year data:', error);\n      alert('❌ Error fetching 5-year historical data');\n    } finally {\n      setFetchingData(false);\n    }\n  };\n\n  return (\n    <PanelContainer>\n      <PanelHeader>Control Panel</PanelHeader>\n      \n      <StatusCard $active={tradingActive}>\n        <StatusLabel>Trading Status</StatusLabel>\n        <StatusValue $active={tradingActive}>\n          <StatusIndicator $active={tradingActive} />\n          {tradingActive ? 'Active' : 'Inactive'}\n        </StatusValue>\n      </StatusCard>\n\n      <StatusCard $active={true}>\n        <StatusLabel>Mode</StatusLabel>\n        <StatusValue $active={true}>\n          <StatusIndicator $active={true} />\n          Paper Trading\n        </StatusValue>\n      </StatusCard>\n      \n      {!tradingActive ? (\n        <ControlButton \n          className=\"start\" \n          onClick={handleStart}\n          disabled={isLoading}\n        >\n          {isLoading ? 'Starting...' : 'Start Trading'}\n        </ControlButton>\n      ) : (\n        <ControlButton \n          className=\"stop\" \n          onClick={handleStop}\n          disabled={isLoading}\n        >\n          {isLoading ? 'Stopping...' : 'Stop Trading'}\n        </ControlButton>\n      )}\n      \n      <ControlButton className=\"secondary\" disabled={tradingActive}>\n        Reset Portfolio\n      </ControlButton>\n\n      <ControlButton className=\"secondary\">\n        Export Results\n      </ControlButton>\n\n      <ControlButton\n        className=\"secondary\"\n        onClick={fetch5YearData}\n        disabled={fetchingData}\n        style={{\n          background: fetchingData ? '#666' : '#1e3a8a',\n          color: '#fff',\n          border: '1px solid #3b82f6'\n        }}\n      >\n        {fetchingData ? '📥 Fetching 5-Year Data...' : '🚀 Fetch 5-Year Data'}\n      </ControlButton>\n      \n      <ConfigSection>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>\n          <h4 style={{ color: '#888', fontSize: '14px', margin: 0 }}>\n            Trading Configuration\n          </h4>\n          <ControlButton\n            className=\"secondary\"\n            onClick={applyConfiguration}\n            disabled={configSaving || tradingActive}\n            style={{\n              width: 'auto',\n              padding: '4px 8px',\n              fontSize: '11px',\n              marginBottom: 0,\n              background: '#4bffb5',\n              color: '#000'\n            }}\n          >\n            {configSaving ? 'Applying...' : '💾 Apply Config'}\n          </ControlButton>\n        </div>\n\n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '12px' }}>\n          <div>\n            <ConfigLabel>Risk Per Trade (%)</ConfigLabel>\n            <ConfigInput\n              type=\"number\"\n              min=\"0.1\"\n              max=\"10\"\n              step=\"0.1\"\n              value={config.riskPerTrade}\n              onChange={(e) => handleConfigChange('riskPerTrade', e.target.value)}\n              disabled={tradingActive}\n              placeholder=\"2.0\"\n            />\n          </div>\n\n          <div>\n            <ConfigLabel>Max Position (%)</ConfigLabel>\n            <ConfigInput\n              type=\"number\"\n              min=\"1\"\n              max=\"50\"\n              step=\"1\"\n              value={config.maxPositionSize}\n              onChange={(e) => handleConfigChange('maxPositionSize', e.target.value)}\n              disabled={tradingActive}\n              placeholder=\"10\"\n            />\n          </div>\n        </div>\n\n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '12px' }}>\n          <div>\n            <ConfigLabel>Stop Loss (%)</ConfigLabel>\n            <ConfigInput\n              type=\"number\"\n              min=\"1\"\n              max=\"20\"\n              step=\"0.5\"\n              value={config.stopLoss}\n              onChange={(e) => handleConfigChange('stopLoss', e.target.value)}\n              disabled={tradingActive}\n              placeholder=\"5.0\"\n            />\n          </div>\n\n          <div>\n            <ConfigLabel>Take Profit (%)</ConfigLabel>\n            <ConfigInput\n              type=\"number\"\n              min=\"1\"\n              max=\"50\"\n              step=\"0.5\"\n              value={config.takeProfit}\n              onChange={(e) => handleConfigChange('takeProfit', e.target.value)}\n              disabled={tradingActive}\n              placeholder=\"10.0\"\n            />\n          </div>\n        </div>\n\n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '12px' }}>\n          <div>\n            <ConfigLabel>Max Drawdown (%)</ConfigLabel>\n            <ConfigInput\n              type=\"number\"\n              min=\"5\"\n              max=\"50\"\n              step=\"1\"\n              value={config.maxDrawdown}\n              onChange={(e) => handleConfigChange('maxDrawdown', e.target.value)}\n              disabled={tradingActive}\n              placeholder=\"15\"\n            />\n          </div>\n\n          <div>\n            <ConfigLabel>Initial Balance ($)</ConfigLabel>\n            <ConfigInput\n              type=\"number\"\n              min=\"1000\"\n              max=\"1000000\"\n              step=\"1000\"\n              value={config.initialBalance}\n              onChange={(e) => handleConfigChange('initialBalance', e.target.value)}\n              disabled={tradingActive}\n              placeholder=\"10000\"\n            />\n          </div>\n        </div>\n\n        <ConfigLabel>Primary Strategy</ConfigLabel>\n        <ConfigSelect\n          value={config.strategy}\n          onChange={(e) => handleConfigChange('strategy', e.target.value)}\n          disabled={tradingActive}\n        >\n          <option value=\"TECHNICAL\">Technical Analysis</option>\n          <option value=\"ML_ENSEMBLE\">ML Ensemble</option>\n          <option value=\"MOMENTUM\">Momentum</option>\n          <option value=\"MEAN_REVERSION\">Mean Reversion</option>\n        </ConfigSelect>\n\n        <div style={{\n          fontSize: '10px',\n          color: '#666',\n          marginTop: '8px',\n          padding: '8px',\n          background: '#2a2a2a',\n          borderRadius: '4px',\n          lineHeight: '1.4'\n        }}>\n          💡 <strong>Risk Management:</strong> Risk per trade limits position size based on account balance.\n          Stop loss and take profit are automatically applied to all trades.\n        </div>\n      </ConfigSection>\n    </PanelContainer>\n  );\n};\n\nexport default ControlPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,WAAW,GAAGN,MAAM,CAACO,EAAE;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,aAAa,GAAGT,MAAM,CAACU,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA9CIF,aAAa;AAgDnB,MAAMG,UAAU,GAAGZ,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA,2BAA2BS,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AACtE,CAAC;AAACC,GAAA,GANIH,UAAU;AAQhB,MAAMI,WAAW,GAAGhB,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAJID,WAAW;AAMjB,MAAME,WAAW,GAAGlB,MAAM,CAACI,GAAG;AAC9B,WAAWS,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AACtD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAPID,WAAW;AASjB,MAAME,eAAe,GAAGpB,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA,gBAAgBS,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AAC3D,eAAeD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,mBAAmB,GAAG,MAAM;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAZID,eAAe;AAcrB,MAAME,aAAa,GAAGtB,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAJID,aAAa;AAMnB,MAAME,WAAW,GAAGxB,MAAM,CAACyB,KAAK;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,WAAW;AAOjB,MAAMG,WAAW,GAAG3B,MAAM,CAAC4B,KAAK;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,WAAW;AAgBjB,MAAMG,YAAY,GAAG9B,MAAM,CAAC+B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,YAAY;AAqBlB,MAAMG,YAAY,GAAGA,CAAC;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,MAAM;IACzC,IAAI;MACF,MAAM+C,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;MAClE,IAAIF,WAAW,EAAE;QACf,OAAOG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;MAChC;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;IACA,OAAO;MACLE,YAAY,EAAE,GAAG;MACjBC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE,OAAO;MACvBC,QAAQ,EAAE,WAAW;MACrBC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE;IACd,CAAC;EACH,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BrB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMJ,OAAO,CAAC,CAAC;IACjB,CAAC,SAAS;MACRI,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMsB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BtB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMH,MAAM,CAAC,CAAC;IAChB,CAAC,SAAS;MACRG,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMuB,mBAAmB,GAAIC,SAAS,IAAK;IACzC,IAAI;MACFhB,YAAY,CAACiB,OAAO,CAAC,wBAAwB,EAAEf,IAAI,CAACgB,SAAS,CAACF,SAAS,CAAC,CAAC;MACzEX,OAAO,CAACc,GAAG,CAAC,uCAAuC,CAAC;IACtD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMgB,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzC,MAAMN,SAAS,GAAG;MAAE,GAAGnB,MAAM;MAAE,CAACwB,GAAG,GAAGC;IAAM,CAAC;IAC7CxB,SAAS,CAACkB,SAAS,CAAC;IACpBD,mBAAmB,CAACC,SAAS,CAAC;EAChC,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC7B,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6C,EAAE;QAC1EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE1B,IAAI,CAACgB,SAAS,CAAC;UACnBW,cAAc,EAAEC,UAAU,CAACjC,MAAM,CAACS,YAAY,CAAC,GAAG,GAAG;UACrDyB,iBAAiB,EAAED,UAAU,CAACjC,MAAM,CAACU,eAAe,CAAC,GAAG,GAAG;UAC3DyB,YAAY,EAAEF,UAAU,CAACjC,MAAM,CAACW,WAAW,CAAC,GAAG,GAAG;UAClDyB,eAAe,EAAEH,UAAU,CAACjC,MAAM,CAACY,cAAc,CAAC;UAClDyB,SAAS,EAAEJ,UAAU,CAACjC,MAAM,CAACc,QAAQ,CAAC,GAAG,GAAG;UAC5CwB,WAAW,EAAEL,UAAU,CAACjC,MAAM,CAACe,UAAU,CAAC,GAAG,GAAG;UAChDF,QAAQ,EAAEb,MAAM,CAACa;QACnB,CAAC;MACH,CAAC,CAAC;MAEF,IAAIc,QAAQ,CAACY,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;QACpCC,KAAK,CAAC,6CAA6C,CAAC;QACpDlC,OAAO,CAACc,GAAG,CAAC,2BAA2B,EAAEkB,MAAM,CAAC;MAClD,CAAC,MAAM;QACLE,KAAK,CAAC,uCAAuC,CAAC;MAChD;IACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDmC,KAAK,CAAC,sCAAsC,CAAC;IAC/C,CAAC,SAAS;MACR7C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM8C,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC5C,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACFS,OAAO,CAACc,GAAG,CAAC,6CAA6C,CAAC;MAC1D,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIH,QAAQ,CAACY,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;QACpCC,KAAK,CAAC,gDAAgDF,MAAM,CAACI,UAAU,aAAaJ,MAAM,CAACK,MAAM,iBAAiBL,MAAM,CAACM,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACjJvC,OAAO,CAACc,GAAG,CAAC,gCAAgC,EAAEkB,MAAM,CAAC;MACvD,CAAC,MAAM;QACL,MAAMjC,KAAK,GAAG,MAAMoB,QAAQ,CAACc,IAAI,CAAC,CAAC;QACnCC,KAAK,CAAC,kCAAkCnC,KAAK,CAACyC,MAAM,IAAI,eAAe,EAAE,CAAC;QAC1ExC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDmC,KAAK,CAAC,yCAAyC,CAAC;IAClD,CAAC,SAAS;MACR3C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACEzC,OAAA,CAACC,cAAc;IAAA0F,QAAA,gBACb3F,OAAA,CAACI,WAAW;MAAAuF,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAExC/F,OAAA,CAACU,UAAU;MAACE,OAAO,EAAEoB,aAAc;MAAA2D,QAAA,gBACjC3F,OAAA,CAACc,WAAW;QAAA6E,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzC/F,OAAA,CAACgB,WAAW;QAACJ,OAAO,EAAEoB,aAAc;QAAA2D,QAAA,gBAClC3F,OAAA,CAACkB,eAAe;UAACN,OAAO,EAAEoB;QAAc;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC1C/D,aAAa,GAAG,QAAQ,GAAG,UAAU;MAAA;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEb/F,OAAA,CAACU,UAAU;MAACE,OAAO,EAAE,IAAK;MAAA+E,QAAA,gBACxB3F,OAAA,CAACc,WAAW;QAAA6E,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC/B/F,OAAA,CAACgB,WAAW;QAACJ,OAAO,EAAE,IAAK;QAAA+E,QAAA,gBACzB3F,OAAA,CAACkB,eAAe;UAACN,OAAO,EAAE;QAAK;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iBAEpC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEZ,CAAC/D,aAAa,gBACbhC,OAAA,CAACO,aAAa;MACZyF,SAAS,EAAC,OAAO;MACjBC,OAAO,EAAEvC,WAAY;MACrBwC,QAAQ,EAAE9D,SAAU;MAAAuD,QAAA,EAEnBvD,SAAS,GAAG,aAAa,GAAG;IAAe;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAEhB/F,OAAA,CAACO,aAAa;MACZyF,SAAS,EAAC,MAAM;MAChBC,OAAO,EAAEtC,UAAW;MACpBuC,QAAQ,EAAE9D,SAAU;MAAAuD,QAAA,EAEnBvD,SAAS,GAAG,aAAa,GAAG;IAAc;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAChB,eAED/F,OAAA,CAACO,aAAa;MAACyF,SAAS,EAAC,WAAW;MAACE,QAAQ,EAAElE,aAAc;MAAA2D,QAAA,EAAC;IAE9D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,eAEhB/F,OAAA,CAACO,aAAa;MAACyF,SAAS,EAAC,WAAW;MAAAL,QAAA,EAAC;IAErC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,eAEhB/F,OAAA,CAACO,aAAa;MACZyF,SAAS,EAAC,WAAW;MACrBC,OAAO,EAAEZ,cAAe;MACxBa,QAAQ,EAAE1D,YAAa;MACvB2D,KAAK,EAAE;QACLC,UAAU,EAAE5D,YAAY,GAAG,MAAM,GAAG,SAAS;QAC7C6D,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACV,CAAE;MAAAX,QAAA,EAEDnD,YAAY,GAAG,4BAA4B,GAAG;IAAsB;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAEhB/F,OAAA,CAACoB,aAAa;MAAAuE,QAAA,gBACZ3F,OAAA;QAAKmG,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAC3G3F,OAAA;UAAImG,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAEM,QAAQ,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAAjB,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/F,OAAA,CAACO,aAAa;UACZyF,SAAS,EAAC,WAAW;UACrBC,OAAO,EAAE7B,kBAAmB;UAC5B8B,QAAQ,EAAE5D,YAAY,IAAIN,aAAc;UACxCmE,KAAK,EAAE;YACLU,KAAK,EAAE,MAAM;YACbC,OAAO,EAAE,SAAS;YAClBH,QAAQ,EAAE,MAAM;YAChBD,YAAY,EAAE,CAAC;YACfN,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,EAEDrD,YAAY,GAAG,aAAa,GAAG;QAAiB;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEN/F,OAAA;QAAKmG,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE,KAAK;UAAEN,YAAY,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAChG3F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA,CAACsB,WAAW;YAAAqE,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7C/F,OAAA,CAACyB,WAAW;YACVwF,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,KAAK;YACTC,GAAG,EAAC,IAAI;YACRC,IAAI,EAAC,KAAK;YACVjD,KAAK,EAAEzB,MAAM,CAACS,YAAa;YAC3BkE,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,cAAc,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;YACpE+B,QAAQ,EAAElE,aAAc;YACxBwF,WAAW,EAAC;UAAK;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA,CAACsB,WAAW;YAAAqE,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3C/F,OAAA,CAACyB,WAAW;YACVwF,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRC,IAAI,EAAC,GAAG;YACRjD,KAAK,EAAEzB,MAAM,CAACU,eAAgB;YAC9BiE,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,iBAAiB,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;YACvE+B,QAAQ,EAAElE,aAAc;YACxBwF,WAAW,EAAC;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/F,OAAA;QAAKmG,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE,KAAK;UAAEN,YAAY,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAChG3F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA,CAACsB,WAAW;YAAAqE,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACxC/F,OAAA,CAACyB,WAAW;YACVwF,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRC,IAAI,EAAC,KAAK;YACVjD,KAAK,EAAEzB,MAAM,CAACc,QAAS;YACvB6D,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,UAAU,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;YAChE+B,QAAQ,EAAElE,aAAc;YACxBwF,WAAW,EAAC;UAAK;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA,CAACsB,WAAW;YAAAqE,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1C/F,OAAA,CAACyB,WAAW;YACVwF,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRC,IAAI,EAAC,KAAK;YACVjD,KAAK,EAAEzB,MAAM,CAACe,UAAW;YACzB4D,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,YAAY,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;YAClE+B,QAAQ,EAAElE,aAAc;YACxBwF,WAAW,EAAC;UAAM;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/F,OAAA;QAAKmG,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE,KAAK;UAAEN,YAAY,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAChG3F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA,CAACsB,WAAW;YAAAqE,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3C/F,OAAA,CAACyB,WAAW;YACVwF,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRC,IAAI,EAAC,GAAG;YACRjD,KAAK,EAAEzB,MAAM,CAACW,WAAY;YAC1BgE,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,aAAa,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;YACnE+B,QAAQ,EAAElE,aAAc;YACxBwF,WAAW,EAAC;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA,CAACsB,WAAW;YAAAqE,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9C/F,OAAA,CAACyB,WAAW;YACVwF,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,MAAM;YACVC,GAAG,EAAC,SAAS;YACbC,IAAI,EAAC,MAAM;YACXjD,KAAK,EAAEzB,MAAM,CAACY,cAAe;YAC7B+D,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,gBAAgB,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;YACtE+B,QAAQ,EAAElE,aAAc;YACxBwF,WAAW,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/F,OAAA,CAACsB,WAAW;QAAAqE,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3C/F,OAAA,CAAC4B,YAAY;QACXuC,KAAK,EAAEzB,MAAM,CAACa,QAAS;QACvB8D,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,UAAU,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;QAChE+B,QAAQ,EAAElE,aAAc;QAAA2D,QAAA,gBAExB3F,OAAA;UAAQmE,KAAK,EAAC,WAAW;UAAAwB,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACrD/F,OAAA;UAAQmE,KAAK,EAAC,aAAa;UAAAwB,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChD/F,OAAA;UAAQmE,KAAK,EAAC,UAAU;UAAAwB,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1C/F,OAAA;UAAQmE,KAAK,EAAC,gBAAgB;UAAAwB,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAEf/F,OAAA;QAAKmG,KAAK,EAAE;UACVQ,QAAQ,EAAE,MAAM;UAChBN,KAAK,EAAE,MAAM;UACboB,SAAS,EAAE,KAAK;UAChBX,OAAO,EAAE,KAAK;UACdV,UAAU,EAAE,SAAS;UACrBsB,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAhC,QAAA,GAAC,eACE,eAAA3F,OAAA;UAAA2F,QAAA,EAAQ;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,qIAEtC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAErB,CAAC;AAAC5D,EAAA,CAjUIJ,YAAY;AAAA6F,IAAA,GAAZ7F,YAAY;AAmUlB,eAAeA,YAAY;AAAC,IAAA5B,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAA8F,IAAA;AAAAC,YAAA,CAAA1H,EAAA;AAAA0H,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}