{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\PortfolioPanel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n_c = PanelContainer;\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n_c2 = PanelHeader;\nconst MetricCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 12px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c3 = MetricCard;\nconst MetricLabel = styled.div`\n  color: #888;\n  font-size: 14px;\n`;\n_c4 = MetricLabel;\nconst MetricValue = styled.div`\n  color: ${props => props.$positive ? '#4bffb5' : props.$negative ? '#ff4976' : '#ffffff'};\n  font-size: 16px;\n  font-weight: 600;\n`;\n_c5 = MetricValue;\nconst PositionCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 8px;\n  border-left: 4px solid #4bffb5;\n`;\n_c6 = PositionCard;\nconst PositionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n`;\n_c7 = PositionHeader;\nconst PositionSymbol = styled.div`\n  font-weight: 600;\n  color: #ffffff;\n`;\n_c8 = PositionSymbol;\nconst PositionPnL = styled.div`\n  color: ${props => props.value >= 0 ? '#4bffb5' : '#ff4976'};\n  font-weight: 600;\n  font-size: 14px;\n`;\n_c9 = PositionPnL;\nconst PositionDetails = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n  font-size: 12px;\n  color: #888;\n`;\n_c0 = PositionDetails;\nconst PositionActions = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-top: 12px;\n  padding-top: 8px;\n  border-top: 1px solid #333;\n`;\n_c1 = PositionActions;\nconst ActionButton = styled.button`\n  flex: 1;\n  padding: 6px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &.close {\n    background: #ff4976;\n    color: #fff;\n\n    &:hover:not(:disabled) {\n      background: #e63946;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.partial {\n    background: #ffa726;\n    color: #000;\n\n    &:hover:not(:disabled) {\n      background: #ff9800;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.info {\n    background: #2a2a2a;\n    color: #888;\n    border: 1px solid #444;\n\n    &:hover:not(:disabled) {\n      background: #333;\n      color: #fff;\n    }\n  }\n`;\n_c10 = ActionButton;\nconst ConfirmDialog = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n`;\n_c11 = ConfirmDialog;\nconst ConfirmContent = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 24px;\n  max-width: 400px;\n  width: 90%;\n  border: 1px solid #333;\n`;\n_c12 = ConfirmContent;\nconst ConfirmTitle = styled.h3`\n  margin: 0 0 16px 0;\n  color: #ff4976;\n  font-size: 18px;\n`;\n_c13 = ConfirmTitle;\nconst ConfirmText = styled.p`\n  margin: 0 0 20px 0;\n  color: #888;\n  line-height: 1.4;\n`;\n_c14 = ConfirmText;\nconst ConfirmActions = styled.div`\n  display: flex;\n  gap: 12px;\n`;\n_c15 = ConfirmActions;\nconst ConfirmButton = styled.button`\n  flex: 1;\n  padding: 10px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &.confirm {\n    background: #ff4976;\n    color: #fff;\n\n    &:hover {\n      background: #e63946;\n    }\n  }\n\n  &.cancel {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n\n    &:hover {\n      background: #333;\n    }\n  }\n`;\n_c16 = ConfirmButton;\nconst PortfolioPanel = ({\n  portfolio\n}) => {\n  _s();\n  var _portfolio$total_valu, _portfolio$cash, _portfolio$unrealized, _portfolio$realized_p, _confirmDialog$size, _confirmDialog$estima, _confirmDialog$positi, _confirmDialog$positi2;\n  // 🔥 MANUAL POSITION MANAGEMENT: State for position management\n  const [confirmDialog, setConfirmDialog] = useState(null);\n  const [isClosing, setIsClosing] = useState(false);\n\n  // 🔥 MANUAL POSITION MANAGEMENT: Close position functionality\n  const handleClosePosition = async (symbol, size = null) => {\n    setIsClosing(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/close-position', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          symbol: symbol,\n          size: size,\n          // null for full close, number for partial close\n          reason: 'manual_close'\n        })\n      });\n      if (response.ok) {\n        var _result$pnl;\n        const result = await response.json();\n        alert(`Position ${symbol} closed successfully! P&L: $${((_result$pnl = result.pnl) === null || _result$pnl === void 0 ? void 0 : _result$pnl.toFixed(2)) || '0.00'}`);\n        console.log('✅ Position closed:', result);\n      } else {\n        const error = await response.json();\n        alert(`Failed to close position: ${error.detail || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error closing position:', error);\n      alert('Error closing position');\n    } finally {\n      setIsClosing(false);\n      setConfirmDialog(null);\n    }\n  };\n\n  // 🔥 MANUAL POSITION MANAGEMENT: Show confirmation dialog\n  const showCloseConfirmation = (symbol, position, isPartial = false) => {\n    const size = isPartial ? position.size / 2 : position.size; // Close half for partial\n    const estimatedPnL = position.unrealized_pnl || 0;\n    setConfirmDialog({\n      symbol,\n      position,\n      size,\n      isPartial,\n      estimatedPnL\n    });\n  };\n  if (!portfolio) {\n    return /*#__PURE__*/_jsxDEV(PanelContainer, {\n      children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n        children: \"Portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#888',\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: \"Loading portfolio data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this);\n  }\n  const totalReturn = portfolio.total_return || 0;\n  const hasPositions = portfolio.positions && Object.keys(portfolio.positions).length > 0;\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n      children: \"Portfolio\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n      children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n        children: \"Total Value\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        children: [\"$\", ((_portfolio$total_valu = portfolio.total_value) === null || _portfolio$total_valu === void 0 ? void 0 : _portfolio$total_valu.toFixed(2)) || '0.00']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n      children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n        children: \"Cash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        children: [\"$\", ((_portfolio$cash = portfolio.cash) === null || _portfolio$cash === void 0 ? void 0 : _portfolio$cash.toFixed(2)) || '0.00']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n      children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n        children: \"Unrealized P&L\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        $positive: portfolio.unrealized_pnl >= 0,\n        $negative: portfolio.unrealized_pnl < 0,\n        children: [\"$\", ((_portfolio$unrealized = portfolio.unrealized_pnl) === null || _portfolio$unrealized === void 0 ? void 0 : _portfolio$unrealized.toFixed(2)) || '0.00']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n      children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n        children: \"Realized P&L\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        $positive: portfolio.realized_pnl >= 0,\n        $negative: portfolio.realized_pnl < 0,\n        children: [\"$\", ((_portfolio$realized_p = portfolio.realized_pnl) === null || _portfolio$realized_p === void 0 ? void 0 : _portfolio$realized_p.toFixed(2)) || '0.00']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n      children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n        children: \"Total Return\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        $positive: totalReturn >= 0,\n        $negative: totalReturn < 0,\n        children: [totalReturn >= 0 ? '+' : '', totalReturn.toFixed(2), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), hasPositions && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: '#888',\n          fontSize: '14px',\n          marginBottom: '12px'\n        },\n        children: \"Active Positions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this), Object.entries(portfolio.positions).map(([symbol, position]) => {\n        var _position$unrealized_, _position$size, _position$entry_price, _position$current_pri, _ref;\n        return /*#__PURE__*/_jsxDEV(PositionCard, {\n          children: [/*#__PURE__*/_jsxDEV(PositionHeader, {\n            children: [/*#__PURE__*/_jsxDEV(PositionSymbol, {\n              children: symbol\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(PositionPnL, {\n              value: position.unrealized_pnl,\n              children: [\"$\", ((_position$unrealized_ = position.unrealized_pnl) === null || _position$unrealized_ === void 0 ? void 0 : _position$unrealized_.toFixed(2)) || '0.00']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PositionDetails, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Size: \", ((_position$size = position.size) === null || _position$size === void 0 ? void 0 : _position$size.toFixed(4)) || '0.0000']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Entry: $\", ((_position$entry_price = position.entry_price) === null || _position$entry_price === void 0 ? void 0 : _position$entry_price.toFixed(4)) || '0.0000']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Current: $\", ((_position$current_pri = position.current_price) === null || _position$current_pri === void 0 ? void 0 : _position$current_pri.toFixed(4)) || '0.0000']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Value: $\", ((_ref = position.size * position.current_price) === null || _ref === void 0 ? void 0 : _ref.toFixed(2)) || '0.00']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PositionActions, {\n            children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n              className: \"close\",\n              onClick: () => showCloseConfirmation(symbol, position, false),\n              disabled: isClosing,\n              children: isClosing ? 'Closing...' : '🗙 Close All'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n              className: \"partial\",\n              onClick: () => showCloseConfirmation(symbol, position, true),\n              disabled: isClosing,\n              children: \"\\uD83D\\uDCCA Close 50%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n              className: \"info\",\n              onClick: () => {\n                var _position$entry_price2, _position$current_pri2, _position$size2, _position$unrealized_2;\n                const entryTime = new Date(position.entry_time).toLocaleString();\n                const duration = Math.round((Date.now() - new Date(position.entry_time)) / (1000 * 60));\n                alert(`Position Details:\\n\\nSymbol: ${symbol}\\nEntry Time: ${entryTime}\\nDuration: ${duration} minutes\\nEntry Price: $${(_position$entry_price2 = position.entry_price) === null || _position$entry_price2 === void 0 ? void 0 : _position$entry_price2.toFixed(4)}\\nCurrent Price: $${(_position$current_pri2 = position.current_price) === null || _position$current_pri2 === void 0 ? void 0 : _position$current_pri2.toFixed(4)}\\nSize: ${(_position$size2 = position.size) === null || _position$size2 === void 0 ? void 0 : _position$size2.toFixed(4)}\\nP&L: $${(_position$unrealized_2 = position.unrealized_pnl) === null || _position$unrealized_2 === void 0 ? void 0 : _position$unrealized_2.toFixed(2)}`);\n              },\n              children: \"\\u2139\\uFE0F Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)]\n        }, symbol, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 9\n    }, this), confirmDialog && /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      children: /*#__PURE__*/_jsxDEV(ConfirmContent, {\n        children: [/*#__PURE__*/_jsxDEV(ConfirmTitle, {\n          children: confirmDialog.isPartial ? 'Close Partial Position' : 'Close Position'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ConfirmText, {\n          children: [\"Are you sure you want to \", confirmDialog.isPartial ? 'partially close' : 'close', \" your \", confirmDialog.symbol, \" position?\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Position Details:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 49\n          }, this), \"Size to close: \", (_confirmDialog$size = confirmDialog.size) === null || _confirmDialog$size === void 0 ? void 0 : _confirmDialog$size.toFixed(4), \" \", confirmDialog.symbol, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 85\n          }, this), \"Current P&L: \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: confirmDialog.estimatedPnL >= 0 ? '#4bffb5' : '#ff4976'\n            },\n            children: [\"$\", (_confirmDialog$estima = confirmDialog.estimatedPnL) === null || _confirmDialog$estima === void 0 ? void 0 : _confirmDialog$estima.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 28\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 22\n          }, this), \"Entry Price: $\", (_confirmDialog$positi = confirmDialog.position.entry_price) === null || _confirmDialog$positi === void 0 ? void 0 : _confirmDialog$positi.toFixed(4), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 77\n          }, this), \"Current Price: $\", (_confirmDialog$positi2 = confirmDialog.position.current_price) === null || _confirmDialog$positi2 === void 0 ? void 0 : _confirmDialog$positi2.toFixed(4), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"em\", {\n            children: \"This action cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ConfirmActions, {\n          children: [/*#__PURE__*/_jsxDEV(ConfirmButton, {\n            className: \"cancel\",\n            onClick: () => setConfirmDialog(null),\n            disabled: isClosing,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ConfirmButton, {\n            className: \"confirm\",\n            onClick: () => handleClosePosition(confirmDialog.symbol, confirmDialog.isPartial ? confirmDialog.size : null),\n            disabled: isClosing,\n            children: isClosing ? 'Closing...' : `${confirmDialog.isPartial ? 'Partial Close' : 'Close Position'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n};\n_s(PortfolioPanel, \"aHtx1KAdcDPx7QKAFYKiwIa9z+4=\");\n_c17 = PortfolioPanel;\nexport default PortfolioPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"PanelHeader\");\n$RefreshReg$(_c3, \"MetricCard\");\n$RefreshReg$(_c4, \"MetricLabel\");\n$RefreshReg$(_c5, \"MetricValue\");\n$RefreshReg$(_c6, \"PositionCard\");\n$RefreshReg$(_c7, \"PositionHeader\");\n$RefreshReg$(_c8, \"PositionSymbol\");\n$RefreshReg$(_c9, \"PositionPnL\");\n$RefreshReg$(_c0, \"PositionDetails\");\n$RefreshReg$(_c1, \"PositionActions\");\n$RefreshReg$(_c10, \"ActionButton\");\n$RefreshReg$(_c11, \"ConfirmDialog\");\n$RefreshReg$(_c12, \"ConfirmContent\");\n$RefreshReg$(_c13, \"ConfirmTitle\");\n$RefreshReg$(_c14, \"ConfirmText\");\n$RefreshReg$(_c15, \"ConfirmActions\");\n$RefreshReg$(_c16, \"ConfirmButton\");\n$RefreshReg$(_c17, \"PortfolioPanel\");", "map": {"version": 3, "names": ["React", "useState", "styled", "jsxDEV", "_jsxDEV", "PanelContainer", "div", "_c", "PanelHeader", "h3", "_c2", "MetricCard", "_c3", "MetricLabel", "_c4", "MetricValue", "props", "$positive", "$negative", "_c5", "PositionCard", "_c6", "PositionHeader", "_c7", "PositionSymbol", "_c8", "PositionPnL", "value", "_c9", "PositionDetails", "_c0", "PositionActions", "_c1", "ActionButton", "button", "_c10", "ConfirmDialog", "_c11", "Confirm<PERSON><PERSON>nt", "_c12", "ConfirmTitle", "_c13", "ConfirmText", "p", "_c14", "ConfirmActions", "_c15", "ConfirmButton", "_c16", "PortfolioPanel", "portfolio", "_s", "_portfolio$total_valu", "_portfolio$cash", "_portfolio$unrealized", "_portfolio$realized_p", "_confirmDialog$size", "_confirmDialog$estima", "_confirmDialog$positi", "_confirmDialog$positi2", "confirmDialog", "setConfirmDialog", "isClosing", "setIsClosing", "handleClosePosition", "symbol", "size", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "reason", "ok", "_result$pnl", "result", "json", "alert", "pnl", "toFixed", "console", "log", "error", "detail", "showCloseConfirmation", "position", "isPartial", "estimatedPnL", "unrealized_pnl", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "textAlign", "padding", "totalReturn", "total_return", "hasPositions", "positions", "Object", "keys", "length", "total_value", "cash", "realized_pnl", "marginTop", "fontSize", "marginBottom", "entries", "map", "_position$unrealized_", "_position$size", "_position$entry_price", "_position$current_pri", "_ref", "entry_price", "current_price", "className", "onClick", "disabled", "_position$entry_price2", "_position$current_pri2", "_position$size2", "_position$unrealized_2", "entryTime", "Date", "entry_time", "toLocaleString", "duration", "Math", "round", "now", "_c17", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/PortfolioPanel.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\n\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n\nconst MetricCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 12px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst MetricLabel = styled.div`\n  color: #888;\n  font-size: 14px;\n`;\n\nconst MetricValue = styled.div`\n  color: ${props =>\n    props.$positive ? '#4bffb5' :\n    props.$negative ? '#ff4976' : '#ffffff'\n  };\n  font-size: 16px;\n  font-weight: 600;\n`;\n\nconst PositionCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 8px;\n  border-left: 4px solid #4bffb5;\n`;\n\nconst PositionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n`;\n\nconst PositionSymbol = styled.div`\n  font-weight: 600;\n  color: #ffffff;\n`;\n\nconst PositionPnL = styled.div`\n  color: ${props => props.value >= 0 ? '#4bffb5' : '#ff4976'};\n  font-weight: 600;\n  font-size: 14px;\n`;\n\nconst PositionDetails = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n  font-size: 12px;\n  color: #888;\n`;\n\nconst PositionActions = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-top: 12px;\n  padding-top: 8px;\n  border-top: 1px solid #333;\n`;\n\nconst ActionButton = styled.button`\n  flex: 1;\n  padding: 6px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &.close {\n    background: #ff4976;\n    color: #fff;\n\n    &:hover:not(:disabled) {\n      background: #e63946;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.partial {\n    background: #ffa726;\n    color: #000;\n\n    &:hover:not(:disabled) {\n      background: #ff9800;\n      transform: translateY(-1px);\n    }\n  }\n\n  &.info {\n    background: #2a2a2a;\n    color: #888;\n    border: 1px solid #444;\n\n    &:hover:not(:disabled) {\n      background: #333;\n      color: #fff;\n    }\n  }\n`;\n\nconst ConfirmDialog = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n`;\n\nconst ConfirmContent = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 24px;\n  max-width: 400px;\n  width: 90%;\n  border: 1px solid #333;\n`;\n\nconst ConfirmTitle = styled.h3`\n  margin: 0 0 16px 0;\n  color: #ff4976;\n  font-size: 18px;\n`;\n\nconst ConfirmText = styled.p`\n  margin: 0 0 20px 0;\n  color: #888;\n  line-height: 1.4;\n`;\n\nconst ConfirmActions = styled.div`\n  display: flex;\n  gap: 12px;\n`;\n\nconst ConfirmButton = styled.button`\n  flex: 1;\n  padding: 10px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &.confirm {\n    background: #ff4976;\n    color: #fff;\n\n    &:hover {\n      background: #e63946;\n    }\n  }\n\n  &.cancel {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n\n    &:hover {\n      background: #333;\n    }\n  }\n`;\n\nconst PortfolioPanel = ({ portfolio }) => {\n  // 🔥 MANUAL POSITION MANAGEMENT: State for position management\n  const [confirmDialog, setConfirmDialog] = useState(null);\n  const [isClosing, setIsClosing] = useState(false);\n\n  // 🔥 MANUAL POSITION MANAGEMENT: Close position functionality\n  const handleClosePosition = async (symbol, size = null) => {\n    setIsClosing(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/close-position', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          symbol: symbol,\n          size: size, // null for full close, number for partial close\n          reason: 'manual_close'\n        }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        alert(`Position ${symbol} closed successfully! P&L: $${result.pnl?.toFixed(2) || '0.00'}`);\n        console.log('✅ Position closed:', result);\n      } else {\n        const error = await response.json();\n        alert(`Failed to close position: ${error.detail || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error closing position:', error);\n      alert('Error closing position');\n    } finally {\n      setIsClosing(false);\n      setConfirmDialog(null);\n    }\n  };\n\n  // 🔥 MANUAL POSITION MANAGEMENT: Show confirmation dialog\n  const showCloseConfirmation = (symbol, position, isPartial = false) => {\n    const size = isPartial ? position.size / 2 : position.size; // Close half for partial\n    const estimatedPnL = position.unrealized_pnl || 0;\n\n    setConfirmDialog({\n      symbol,\n      position,\n      size,\n      isPartial,\n      estimatedPnL\n    });\n  };\n\n  if (!portfolio) {\n    return (\n      <PanelContainer>\n        <PanelHeader>Portfolio</PanelHeader>\n        <div style={{ color: '#888', textAlign: 'center', padding: '20px' }}>\n          Loading portfolio data...\n        </div>\n      </PanelContainer>\n    );\n  }\n\n  const totalReturn = portfolio.total_return || 0;\n  const hasPositions = portfolio.positions && Object.keys(portfolio.positions).length > 0;\n\n  return (\n    <PanelContainer>\n      <PanelHeader>Portfolio</PanelHeader>\n      \n      <MetricCard>\n        <MetricLabel>Total Value</MetricLabel>\n        <MetricValue>${portfolio.total_value?.toFixed(2) || '0.00'}</MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Cash</MetricLabel>\n        <MetricValue>${portfolio.cash?.toFixed(2) || '0.00'}</MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Unrealized P&L</MetricLabel>\n        <MetricValue\n          $positive={portfolio.unrealized_pnl >= 0}\n          $negative={portfolio.unrealized_pnl < 0}\n        >\n          ${portfolio.unrealized_pnl?.toFixed(2) || '0.00'}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Realized P&L</MetricLabel>\n        <MetricValue\n          $positive={portfolio.realized_pnl >= 0}\n          $negative={portfolio.realized_pnl < 0}\n        >\n          ${portfolio.realized_pnl?.toFixed(2) || '0.00'}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Total Return</MetricLabel>\n        <MetricValue\n          $positive={totalReturn >= 0}\n          $negative={totalReturn < 0}\n        >\n          {totalReturn >= 0 ? '+' : ''}{totalReturn.toFixed(2)}%\n        </MetricValue>\n      </MetricCard>\n      \n      {hasPositions && (\n        <div style={{ marginTop: '20px' }}>\n          <h4 style={{ color: '#888', fontSize: '14px', marginBottom: '12px' }}>\n            Active Positions\n          </h4>\n          {Object.entries(portfolio.positions).map(([symbol, position]) => (\n            <PositionCard key={symbol}>\n              <PositionHeader>\n                <PositionSymbol>{symbol}</PositionSymbol>\n                <PositionPnL value={position.unrealized_pnl}>\n                  ${position.unrealized_pnl?.toFixed(2) || '0.00'}\n                </PositionPnL>\n              </PositionHeader>\n              <PositionDetails>\n                <div>Size: {position.size?.toFixed(4) || '0.0000'}</div>\n                <div>Entry: ${position.entry_price?.toFixed(4) || '0.0000'}</div>\n                <div>Current: ${position.current_price?.toFixed(4) || '0.0000'}</div>\n                <div>Value: ${(position.size * position.current_price)?.toFixed(2) || '0.00'}</div>\n              </PositionDetails>\n\n              {/* 🔥 MANUAL POSITION MANAGEMENT: Position action buttons */}\n              <PositionActions>\n                <ActionButton\n                  className=\"close\"\n                  onClick={() => showCloseConfirmation(symbol, position, false)}\n                  disabled={isClosing}\n                >\n                  {isClosing ? 'Closing...' : '🗙 Close All'}\n                </ActionButton>\n                <ActionButton\n                  className=\"partial\"\n                  onClick={() => showCloseConfirmation(symbol, position, true)}\n                  disabled={isClosing}\n                >\n                  📊 Close 50%\n                </ActionButton>\n                <ActionButton\n                  className=\"info\"\n                  onClick={() => {\n                    const entryTime = new Date(position.entry_time).toLocaleString();\n                    const duration = Math.round((Date.now() - new Date(position.entry_time)) / (1000 * 60));\n                    alert(`Position Details:\\n\\nSymbol: ${symbol}\\nEntry Time: ${entryTime}\\nDuration: ${duration} minutes\\nEntry Price: $${position.entry_price?.toFixed(4)}\\nCurrent Price: $${position.current_price?.toFixed(4)}\\nSize: ${position.size?.toFixed(4)}\\nP&L: $${position.unrealized_pnl?.toFixed(2)}`);\n                  }}\n                >\n                  ℹ️ Info\n                </ActionButton>\n              </PositionActions>\n            </PositionCard>\n          ))}\n        </div>\n      )}\n\n      {/* 🔥 MANUAL POSITION MANAGEMENT: Confirmation dialog */}\n      {confirmDialog && (\n        <ConfirmDialog>\n          <ConfirmContent>\n            <ConfirmTitle>\n              {confirmDialog.isPartial ? 'Close Partial Position' : 'Close Position'}\n            </ConfirmTitle>\n            <ConfirmText>\n              Are you sure you want to {confirmDialog.isPartial ? 'partially close' : 'close'} your {confirmDialog.symbol} position?\n              <br /><br />\n              <strong>Position Details:</strong><br />\n              Size to close: {confirmDialog.size?.toFixed(4)} {confirmDialog.symbol}<br />\n              Current P&L: <span style={{ color: confirmDialog.estimatedPnL >= 0 ? '#4bffb5' : '#ff4976' }}>\n                ${confirmDialog.estimatedPnL?.toFixed(2)}\n              </span><br />\n              Entry Price: ${confirmDialog.position.entry_price?.toFixed(4)}<br />\n              Current Price: ${confirmDialog.position.current_price?.toFixed(4)}\n              <br /><br />\n              <em>This action cannot be undone.</em>\n            </ConfirmText>\n            <ConfirmActions>\n              <ConfirmButton\n                className=\"cancel\"\n                onClick={() => setConfirmDialog(null)}\n                disabled={isClosing}\n              >\n                Cancel\n              </ConfirmButton>\n              <ConfirmButton\n                className=\"confirm\"\n                onClick={() => handleClosePosition(confirmDialog.symbol, confirmDialog.isPartial ? confirmDialog.size : null)}\n                disabled={isClosing}\n              >\n                {isClosing ? 'Closing...' : `${confirmDialog.isPartial ? 'Partial Close' : 'Close Position'}`}\n              </ConfirmButton>\n            </ConfirmActions>\n          </ConfirmContent>\n        </ConfirmDialog>\n      )}\n    </PanelContainer>\n  );\n};\n\nexport default PortfolioPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,WAAW,GAAGN,MAAM,CAACO,EAAE;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,UAAU,GAAGT,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GARID,UAAU;AAUhB,MAAME,WAAW,GAAGX,MAAM,CAACI,GAAG;AAC9B;AACA;AACA,CAAC;AAACQ,GAAA,GAHID,WAAW;AAKjB,MAAME,WAAW,GAAGb,MAAM,CAACI,GAAG;AAC9B,WAAWU,KAAK,IACZA,KAAK,CAACC,SAAS,GAAG,SAAS,GAC3BD,KAAK,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;AAC3C;AACA;AACA,CACC;AAACC,GAAA,GAPIJ,WAAW;AASjB,MAAMK,YAAY,GAAGlB,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GANID,YAAY;AAQlB,MAAME,cAAc,GAAGpB,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GALID,cAAc;AAOpB,MAAME,cAAc,GAAGtB,MAAM,CAACI,GAAG;AACjC;AACA;AACA,CAAC;AAACmB,GAAA,GAHID,cAAc;AAKpB,MAAME,WAAW,GAAGxB,MAAM,CAACI,GAAG;AAC9B,WAAWU,KAAK,IAAIA,KAAK,CAACW,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;AAC5D;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,eAAe,GAAG3B,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GANID,eAAe;AAQrB,MAAME,eAAe,GAAG7B,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GANID,eAAe;AAQrB,MAAME,YAAY,GAAG/B,MAAM,CAACgC,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7CIF,YAAY;AA+ClB,MAAMG,aAAa,GAAGlC,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GAXID,aAAa;AAanB,MAAME,cAAc,GAAGpC,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAPID,cAAc;AASpB,MAAME,YAAY,GAAGtC,MAAM,CAACO,EAAE;AAC9B;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAJID,YAAY;AAMlB,MAAME,WAAW,GAAGxC,MAAM,CAACyC,CAAC;AAC5B;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAJIF,WAAW;AAMjB,MAAMG,cAAc,GAAG3C,MAAM,CAACI,GAAG;AACjC;AACA;AACA,CAAC;AAACwC,IAAA,GAHID,cAAc;AAKpB,MAAME,aAAa,GAAG7C,MAAM,CAACgC,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,IAAA,GA5BID,aAAa;AA8BnB,MAAME,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACxC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM+D,mBAAmB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,IAAI,GAAG,IAAI,KAAK;IACzDH,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,EAAE;QACvEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBR,MAAM,EAAEA,MAAM;UACdC,IAAI,EAAEA,IAAI;UAAE;UACZQ,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAIP,QAAQ,CAACQ,EAAE,EAAE;QAAA,IAAAC,WAAA;QACf,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;QACpCC,KAAK,CAAC,YAAYd,MAAM,+BAA+B,EAAAW,WAAA,GAAAC,MAAM,CAACG,GAAG,cAAAJ,WAAA,uBAAVA,WAAA,CAAYK,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM,EAAE,CAAC;QAC1FC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEN,MAAM,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMO,KAAK,GAAG,MAAMjB,QAAQ,CAACW,IAAI,CAAC,CAAC;QACnCC,KAAK,CAAC,6BAA6BK,KAAK,CAACC,MAAM,IAAI,eAAe,EAAE,CAAC;MACvE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CL,KAAK,CAAC,wBAAwB,CAAC;IACjC,CAAC,SAAS;MACRhB,YAAY,CAAC,KAAK,CAAC;MACnBF,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMyB,qBAAqB,GAAGA,CAACrB,MAAM,EAAEsB,QAAQ,EAAEC,SAAS,GAAG,KAAK,KAAK;IACrE,MAAMtB,IAAI,GAAGsB,SAAS,GAAGD,QAAQ,CAACrB,IAAI,GAAG,CAAC,GAAGqB,QAAQ,CAACrB,IAAI,CAAC,CAAC;IAC5D,MAAMuB,YAAY,GAAGF,QAAQ,CAACG,cAAc,IAAI,CAAC;IAEjD7B,gBAAgB,CAAC;MACfI,MAAM;MACNsB,QAAQ;MACRrB,IAAI;MACJsB,SAAS;MACTC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAACvC,SAAS,EAAE;IACd,oBACE9C,OAAA,CAACC,cAAc;MAAAsF,QAAA,gBACbvF,OAAA,CAACI,WAAW;QAAAmF,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACpC3F,OAAA;QAAK4F,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAErB;EAEA,MAAMK,WAAW,GAAGlD,SAAS,CAACmD,YAAY,IAAI,CAAC;EAC/C,MAAMC,YAAY,GAAGpD,SAAS,CAACqD,SAAS,IAAIC,MAAM,CAACC,IAAI,CAACvD,SAAS,CAACqD,SAAS,CAAC,CAACG,MAAM,GAAG,CAAC;EAEvF,oBACEtG,OAAA,CAACC,cAAc;IAAAsF,QAAA,gBACbvF,OAAA,CAACI,WAAW;MAAAmF,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAEpC3F,OAAA,CAACO,UAAU;MAAAgF,QAAA,gBACTvF,OAAA,CAACS,WAAW;QAAA8E,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtC3F,OAAA,CAACW,WAAW;QAAA4E,QAAA,GAAC,GAAC,EAAC,EAAAvC,qBAAA,GAAAF,SAAS,CAACyD,WAAW,cAAAvD,qBAAA,uBAArBA,qBAAA,CAAuB6B,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAEb3F,OAAA,CAACO,UAAU;MAAAgF,QAAA,gBACTvF,OAAA,CAACS,WAAW;QAAA8E,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC/B3F,OAAA,CAACW,WAAW;QAAA4E,QAAA,GAAC,GAAC,EAAC,EAAAtC,eAAA,GAAAH,SAAS,CAAC0D,IAAI,cAAAvD,eAAA,uBAAdA,eAAA,CAAgB4B,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAEb3F,OAAA,CAACO,UAAU;MAAAgF,QAAA,gBACTvF,OAAA,CAACS,WAAW;QAAA8E,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzC3F,OAAA,CAACW,WAAW;QACVE,SAAS,EAAEiC,SAAS,CAACwC,cAAc,IAAI,CAAE;QACzCxE,SAAS,EAAEgC,SAAS,CAACwC,cAAc,GAAG,CAAE;QAAAC,QAAA,GACzC,GACE,EAAC,EAAArC,qBAAA,GAAAJ,SAAS,CAACwC,cAAc,cAAApC,qBAAA,uBAAxBA,qBAAA,CAA0B2B,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEb3F,OAAA,CAACO,UAAU;MAAAgF,QAAA,gBACTvF,OAAA,CAACS,WAAW;QAAA8E,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACvC3F,OAAA,CAACW,WAAW;QACVE,SAAS,EAAEiC,SAAS,CAAC2D,YAAY,IAAI,CAAE;QACvC3F,SAAS,EAAEgC,SAAS,CAAC2D,YAAY,GAAG,CAAE;QAAAlB,QAAA,GACvC,GACE,EAAC,EAAApC,qBAAA,GAAAL,SAAS,CAAC2D,YAAY,cAAAtD,qBAAA,uBAAtBA,qBAAA,CAAwB0B,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEb3F,OAAA,CAACO,UAAU;MAAAgF,QAAA,gBACTvF,OAAA,CAACS,WAAW;QAAA8E,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACvC3F,OAAA,CAACW,WAAW;QACVE,SAAS,EAAEmF,WAAW,IAAI,CAAE;QAC5BlF,SAAS,EAAEkF,WAAW,GAAG,CAAE;QAAAT,QAAA,GAE1BS,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,WAAW,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAC,GACvD;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEZO,YAAY,iBACXlG,OAAA;MAAK4F,KAAK,EAAE;QAAEc,SAAS,EAAE;MAAO,CAAE;MAAAnB,QAAA,gBAChCvF,OAAA;QAAI4F,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEc,QAAQ,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAArB,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACJS,MAAM,CAACS,OAAO,CAAC/D,SAAS,CAACqD,SAAS,CAAC,CAACW,GAAG,CAAC,CAAC,CAACjD,MAAM,EAAEsB,QAAQ,CAAC;QAAA,IAAA4B,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,IAAA;QAAA,oBAC1DnH,OAAA,CAACgB,YAAY;UAAAuE,QAAA,gBACXvF,OAAA,CAACkB,cAAc;YAAAqE,QAAA,gBACbvF,OAAA,CAACoB,cAAc;cAAAmE,QAAA,EAAE1B;YAAM;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eACzC3F,OAAA,CAACsB,WAAW;cAACC,KAAK,EAAE4D,QAAQ,CAACG,cAAe;cAAAC,QAAA,GAAC,GAC1C,EAAC,EAAAwB,qBAAA,GAAA5B,QAAQ,CAACG,cAAc,cAAAyB,qBAAA,uBAAvBA,qBAAA,CAAyBlC,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACjB3F,OAAA,CAACyB,eAAe;YAAA8D,QAAA,gBACdvF,OAAA;cAAAuF,QAAA,GAAK,QAAM,EAAC,EAAAyB,cAAA,GAAA7B,QAAQ,CAACrB,IAAI,cAAAkD,cAAA,uBAAbA,cAAA,CAAenC,OAAO,CAAC,CAAC,CAAC,KAAI,QAAQ;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD3F,OAAA;cAAAuF,QAAA,GAAK,UAAQ,EAAC,EAAA0B,qBAAA,GAAA9B,QAAQ,CAACiC,WAAW,cAAAH,qBAAA,uBAApBA,qBAAA,CAAsBpC,OAAO,CAAC,CAAC,CAAC,KAAI,QAAQ;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjE3F,OAAA;cAAAuF,QAAA,GAAK,YAAU,EAAC,EAAA2B,qBAAA,GAAA/B,QAAQ,CAACkC,aAAa,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBrC,OAAO,CAAC,CAAC,CAAC,KAAI,QAAQ;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrE3F,OAAA;cAAAuF,QAAA,GAAK,UAAQ,EAAC,EAAA4B,IAAA,GAAChC,QAAQ,CAACrB,IAAI,GAAGqB,QAAQ,CAACkC,aAAa,cAAAF,IAAA,uBAAvCA,IAAA,CAA0CtC,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAGlB3F,OAAA,CAAC2B,eAAe;YAAA4D,QAAA,gBACdvF,OAAA,CAAC6B,YAAY;cACXyF,SAAS,EAAC,OAAO;cACjBC,OAAO,EAAEA,CAAA,KAAMrC,qBAAqB,CAACrB,MAAM,EAAEsB,QAAQ,EAAE,KAAK,CAAE;cAC9DqC,QAAQ,EAAE9D,SAAU;cAAA6B,QAAA,EAEnB7B,SAAS,GAAG,YAAY,GAAG;YAAc;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACf3F,OAAA,CAAC6B,YAAY;cACXyF,SAAS,EAAC,SAAS;cACnBC,OAAO,EAAEA,CAAA,KAAMrC,qBAAqB,CAACrB,MAAM,EAAEsB,QAAQ,EAAE,IAAI,CAAE;cAC7DqC,QAAQ,EAAE9D,SAAU;cAAA6B,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACf3F,OAAA,CAAC6B,YAAY;cACXyF,SAAS,EAAC,MAAM;cAChBC,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAE,sBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,sBAAA;gBACb,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC3C,QAAQ,CAAC4C,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;gBAChE,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,IAAIN,IAAI,CAAC3C,QAAQ,CAAC4C,UAAU,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;gBACvFpD,KAAK,CAAC,gCAAgCd,MAAM,iBAAiBgE,SAAS,eAAeI,QAAQ,4BAAAR,sBAAA,GAA2BtC,QAAQ,CAACiC,WAAW,cAAAK,sBAAA,uBAApBA,sBAAA,CAAsB5C,OAAO,CAAC,CAAC,CAAC,sBAAA6C,sBAAA,GAAqBvC,QAAQ,CAACkC,aAAa,cAAAK,sBAAA,uBAAtBA,sBAAA,CAAwB7C,OAAO,CAAC,CAAC,CAAC,YAAA8C,eAAA,GAAWxC,QAAQ,CAACrB,IAAI,cAAA6D,eAAA,uBAAbA,eAAA,CAAe9C,OAAO,CAAC,CAAC,CAAC,YAAA+C,sBAAA,GAAWzC,QAAQ,CAACG,cAAc,cAAAsC,sBAAA,uBAAvBA,sBAAA,CAAyB/C,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;cACtS,CAAE;cAAAU,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GAxCD9B,MAAM;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyCX,CAAC;MAAA,CAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAnC,aAAa,iBACZxD,OAAA,CAACgC,aAAa;MAAAuD,QAAA,eACZvF,OAAA,CAACkC,cAAc;QAAAqD,QAAA,gBACbvF,OAAA,CAACoC,YAAY;UAAAmD,QAAA,EACV/B,aAAa,CAAC4B,SAAS,GAAG,wBAAwB,GAAG;QAAgB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACf3F,OAAA,CAACsC,WAAW;UAAAiD,QAAA,GAAC,2BACc,EAAC/B,aAAa,CAAC4B,SAAS,GAAG,iBAAiB,GAAG,OAAO,EAAC,QAAM,EAAC5B,aAAa,CAACK,MAAM,EAAC,YAC5G,eAAA7D,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAAA3F,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACZ3F,OAAA;YAAAuF,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAAA3F,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,mBACzB,GAAAvC,mBAAA,GAACI,aAAa,CAACM,IAAI,cAAAV,mBAAA,uBAAlBA,mBAAA,CAAoByB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACrB,aAAa,CAACK,MAAM,eAAC7D,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,iBAC/D,eAAA3F,OAAA;YAAM4F,KAAK,EAAE;cAAEC,KAAK,EAAErC,aAAa,CAAC6B,YAAY,IAAI,CAAC,GAAG,SAAS,GAAG;YAAU,CAAE;YAAAE,QAAA,GAAC,GAC3F,GAAAlC,qBAAA,GAACG,aAAa,CAAC6B,YAAY,cAAAhC,qBAAA,uBAA1BA,qBAAA,CAA4BwB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAAA3F,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,kBACC,GAAArC,qBAAA,GAACE,aAAa,CAAC2B,QAAQ,CAACiC,WAAW,cAAA9D,qBAAA,uBAAlCA,qBAAA,CAAoCuB,OAAO,CAAC,CAAC,CAAC,eAAC7E,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,oBACpD,GAAApC,sBAAA,GAACC,aAAa,CAAC2B,QAAQ,CAACkC,aAAa,cAAA9D,sBAAA,uBAApCA,sBAAA,CAAsCsB,OAAO,CAAC,CAAC,CAAC,eACjE7E,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAAA3F,OAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACZ3F,OAAA;YAAAuF,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACd3F,OAAA,CAACyC,cAAc;UAAA8C,QAAA,gBACbvF,OAAA,CAAC2C,aAAa;YACZ2E,SAAS,EAAC,QAAQ;YAClBC,OAAO,EAAEA,CAAA,KAAM9D,gBAAgB,CAAC,IAAI,CAAE;YACtC+D,QAAQ,EAAE9D,SAAU;YAAA6B,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAChB3F,OAAA,CAAC2C,aAAa;YACZ2E,SAAS,EAAC,SAAS;YACnBC,OAAO,EAAEA,CAAA,KAAM3D,mBAAmB,CAACJ,aAAa,CAACK,MAAM,EAAEL,aAAa,CAAC4B,SAAS,GAAG5B,aAAa,CAACM,IAAI,GAAG,IAAI,CAAE;YAC9G0D,QAAQ,EAAE9D,SAAU;YAAA6B,QAAA,EAEnB7B,SAAS,GAAG,YAAY,GAAG,GAAGF,aAAa,CAAC4B,SAAS,GAAG,eAAe,GAAG,gBAAgB;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAChB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAAC5C,EAAA,CA3MIF,cAAc;AAAAwF,IAAA,GAAdxF,cAAc;AA6MpB,eAAeA,cAAc;AAAC,IAAA1C,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAyF,IAAA;AAAAC,YAAA,CAAAnI,EAAA;AAAAmI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,IAAA;AAAAuG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}