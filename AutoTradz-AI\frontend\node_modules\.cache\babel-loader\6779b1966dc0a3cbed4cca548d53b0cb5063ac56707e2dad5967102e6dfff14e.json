{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\AlpacaMarketData.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MarketDataContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n_c = MarketDataContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n`;\n_c2 = Header;\nconst Title = styled.h3`\n  color: #4bffb5;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n`;\n_c3 = Title;\nconst RefreshButton = styled.button`\n  background: #4bffb5;\n  color: #000;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: #3de89f;\n  }\n  \n  &:disabled {\n    background: #666;\n    cursor: not-allowed;\n  }\n`;\n_c4 = RefreshButton;\nconst StatusIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: #888;\n`;\n_c5 = StatusIndicator;\nconst StatusDot = styled.div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${props => props.$status === 'live' ? '#4bffb5' : props.$status === 'error' ? '#ff4976' : '#ffa726'};\n  animation: ${props => props.$status === 'live' ? 'pulse 2s infinite' : 'none'};\n\n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n`;\n_c6 = StatusDot;\nconst MarketGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 16px;\n`;\n_c7 = MarketGrid;\nconst PairCard = styled.div`\n  background: #0a0a0a;\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid #333;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    border-color: #4bffb5;\n    transform: translateY(-2px);\n  }\n`;\n_c8 = PairCard;\nconst PairHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n`;\n_c9 = PairHeader;\nconst PairSymbol = styled.h4`\n  color: #fff;\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n`;\n_c0 = PairSymbol;\nconst PairPrice = styled.div`\n  font-size: 20px;\n  font-weight: 700;\n  color: #4bffb5;\n  font-family: 'Courier New', monospace;\n`;\n_c1 = PairPrice;\nconst PairStats = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n  margin-top: 12px;\n`;\n_c10 = PairStats;\nconst StatItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  color: #888;\n`;\n_c11 = StatItem;\nconst StatValue = styled.span`\n  color: ${props => {\n  if (props.type === 'positive') return '#4bffb5';\n  if (props.type === 'negative') return '#ff4976';\n  return '#ffa726';\n}};\n  font-weight: 600;\n`;\n_c12 = StatValue;\nconst ErrorMessage = styled.div`\n  background: rgba(255, 73, 118, 0.1);\n  border: 1px solid #ff4976;\n  border-radius: 8px;\n  padding: 16px;\n  color: #ff4976;\n  text-align: center;\n  margin-top: 16px;\n`;\n_c13 = ErrorMessage;\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: #888;\n  font-size: 14px;\n`;\n_c14 = LoadingSpinner;\nconst AlpacaMarketData = () => {\n  _s();\n  const [marketData, setMarketData] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(true);\n  const fetchMarketData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('http://localhost:8000/api/alpaca/live-market-data');\n      const result = await response.json();\n      if (result.success) {\n        setMarketData(result.data);\n        setLastUpdate(new Date(result.timestamp));\n        console.log('🚀 REAL Alpaca market data updated:', Object.keys(result.data).length, 'pairs');\n        console.log('✅ Source:', result.source, '- Message:', result.message);\n\n        // Log sample data to verify it's real\n        const firstPair = Object.keys(result.data)[0];\n        if (firstPair) {\n          console.log('📊 Sample data for', firstPair, ':', result.data[firstPair]);\n        }\n      } else {\n        setError(result.error || 'Failed to fetch real Alpaca market data');\n        console.error('❌ Alpaca API Error:', result.error);\n      }\n    } catch (err) {\n      setError('Network error: ' + err.message);\n      console.error('❌ Error fetching Alpaca market data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchMarketData();\n\n    // Set up WebSocket for real-time updates with retry logic\n    let ws;\n    let reconnectAttempts = 0;\n    const maxReconnectAttempts = 5;\n    const connectWebSocket = () => {\n      try {\n        ws = new WebSocket('ws://localhost:8000/ws');\n        ws.onopen = () => {\n          console.log('🚀 WebSocket connected for real-time market data');\n          reconnectAttempts = 0; // Reset on successful connection\n          ws.send(JSON.stringify({\n            type: 'subscribe',\n            channels: ['market_data']\n          }));\n        };\n        ws.onmessage = event => {\n          try {\n            const data = JSON.parse(event.data);\n\n            // Handle different WebSocket message formats\n            if (data.type === 'market_data_update' || data.type === 'market_update') {\n              // Handle typed messages\n              const updateData = data.data || data;\n              processMarketUpdate(updateData);\n            } else if (data.type === 'connection_established') {\n              console.log('✅ WebSocket connection confirmed:', data.message);\n            } else if (data.candlestick && data.price !== undefined) {\n              // Handle direct market data format (new format from backend)\n              processDirectMarketData(data);\n            } else if (data.symbol && (data.price !== undefined || data.close !== undefined)) {\n              // Handle simple symbol-price format\n              processMarketUpdate(data);\n            } else {\n              // Only warn if it's truly unrecognized data\n              console.debug('Unrecognized WebSocket message format:', data);\n            }\n          } catch (err) {\n            console.error('Error processing WebSocket message:', err);\n          }\n        };\n\n        // Function to process direct market data format\n        const processDirectMarketData = data => {\n          try {\n            const candlestick = data.candlestick;\n            const symbol = 'XRP/USD'; // Default symbol, should be extracted from data if available\n            const price = data.price;\n            if (!candlestick || price === null || price === undefined || isNaN(price)) {\n              console.warn('Invalid direct market data:', data);\n              return;\n            }\n\n            // Update market data\n            setMarketData(prevData => ({\n              ...prevData,\n              [symbol]: {\n                symbol: symbol,\n                price: price,\n                open: candlestick.open || price,\n                high: candlestick.high || price,\n                low: candlestick.low || price,\n                close: candlestick.close || price,\n                volume: candlestick.volume || 0,\n                timestamp: candlestick.timestamp || new Date().toISOString(),\n                source: 'websocket_direct',\n                change: data.price_change || 0,\n                change_percent: data.price_change_pct || 0\n              }\n            }));\n            console.log('📊 Direct market update:', symbol, '@', price);\n            setLastUpdate(new Date());\n\n            // Trigger chart update event\n            window.dispatchEvent(new CustomEvent('marketDataUpdate', {\n              detail: {\n                symbol: symbol,\n                candlestick: {\n                  time: Math.floor(new Date(candlestick.timestamp || Date.now()).getTime() / 1000),\n                  open: candlestick.open || price,\n                  high: candlestick.high || price,\n                  low: candlestick.low || price,\n                  close: candlestick.close || price,\n                  volume: candlestick.volume || 0\n                }\n              }\n            }));\n          } catch (err) {\n            console.error('Error processing direct market data:', err);\n          }\n        };\n\n        // Function to process standard market updates\n        const processMarketUpdate = updateData => {\n          try {\n            // Validate updateData has required fields\n            if (!updateData || !updateData.symbol && !updateData.candlestick) {\n              console.warn('Invalid market update data:', updateData);\n              return;\n            }\n            const symbol = updateData.symbol || 'XRP/USD'; // Default if not provided\n            const price = updateData.price || updateData.close;\n            if (price === null || price === undefined || isNaN(price)) {\n              console.warn('Invalid price data for symbol:', symbol, price);\n              return;\n            }\n\n            // Update market data with real-time tick\n            setMarketData(prevData => ({\n              ...prevData,\n              [symbol]: {\n                symbol: symbol,\n                price: price,\n                open: updateData.open || price,\n                high: updateData.high || price,\n                low: updateData.low || price,\n                close: updateData.close || price,\n                volume: updateData.volume || 0,\n                timestamp: updateData.timestamp || new Date().toISOString(),\n                source: updateData.source || 'websocket',\n                change: price - (updateData.open || price),\n                change_percent: updateData.open ? ((price - updateData.open) / updateData.open * 100).toFixed(2) : '0.00'\n              }\n            }));\n            console.log('📊 Real-time update:', symbol, '@', price);\n            setLastUpdate(new Date());\n\n            // Trigger chart update event for any listening charts\n            window.dispatchEvent(new CustomEvent('marketDataUpdate', {\n              detail: {\n                symbol: symbol,\n                candlestick: {\n                  time: Math.floor(new Date(updateData.timestamp || Date.now()).getTime() / 1000),\n                  open: updateData.open || price,\n                  high: updateData.high || price,\n                  low: updateData.low || price,\n                  close: updateData.close || price,\n                  volume: updateData.volume || 0\n                }\n              }\n            }));\n          } catch (err) {\n            console.error('Error processing market update:', err);\n          }\n        };\n        ws.onclose = event => {\n          console.log('WebSocket disconnected', event.code, event.reason);\n\n          // Attempt to reconnect if not intentionally closed\n          if (reconnectAttempts < maxReconnectAttempts && event.code !== 1000) {\n            reconnectAttempts++;\n            console.log(`Attempting to reconnect WebSocket (${reconnectAttempts}/${maxReconnectAttempts})...`);\n            setTimeout(connectWebSocket, 2000 * reconnectAttempts); // Exponential backoff\n          }\n        };\n        ws.onerror = error => {\n          console.error('WebSocket error:', error);\n        };\n      } catch (error) {\n        console.error('Failed to create WebSocket connection:', error);\n      }\n    };\n\n    // Initial connection with delay to ensure backend is ready\n    setTimeout(connectWebSocket, 1000);\n\n    // Cleanup on unmount\n    return () => {\n      if (ws) {\n        ws.close(1000, 'Component unmounting'); // Normal closure\n      }\n    };\n  }, []);\n  useEffect(() => {\n    if (autoRefresh) {\n      const interval = setInterval(fetchMarketData, 30000); // Refresh every 30 seconds\n      return () => clearInterval(interval);\n    }\n  }, [autoRefresh]);\n  const formatPrice = price => {\n    // Add null safety check\n    if (price === null || price === undefined || isNaN(price)) {\n      return '$0.0000';\n    }\n    const numPrice = Number(price);\n    if (numPrice >= 1) {\n      return `$${numPrice.toFixed(4)}`;\n    } else {\n      return `$${numPrice.toFixed(6)}`;\n    }\n  };\n  const formatVolume = volume => {\n    // Add null safety check\n    if (volume === null || volume === undefined || isNaN(volume)) {\n      return '0';\n    }\n    const numVolume = Number(volume);\n    if (numVolume >= 1000000) {\n      return `${(numVolume / 1000000).toFixed(2)}M`;\n    } else if (numVolume >= 1000) {\n      return `${(numVolume / 1000).toFixed(2)}K`;\n    }\n    return numVolume.toFixed(0);\n  };\n  const calculateChange = (open, close) => {\n    // Add null safety checks\n    if (open === null || open === undefined || isNaN(open) || close === null || close === undefined || isNaN(close)) {\n      return {\n        change: 0,\n        changePercent: 0\n      };\n    }\n    const numOpen = Number(open);\n    const numClose = Number(close);\n    const change = numClose - numOpen;\n    const changePercent = numOpen !== 0 ? change / numOpen * 100 : 0;\n    return {\n      change,\n      changePercent\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(MarketDataContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: \"\\uD83D\\uDE80 Live Alpaca Market Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n          children: [/*#__PURE__*/_jsxDEV(StatusDot, {\n            $status: error ? 'error' : loading ? 'loading' : 'live'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [error ? 'Error' : loading ? 'Loading...' : 'Live', lastUpdate && ` • ${lastUpdate.toLocaleTimeString()}`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            fontSize: '12px',\n            color: '#888',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '4px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: autoRefresh,\n            onChange: e => setAutoRefresh(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), \"Auto-refresh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RefreshButton, {\n          onClick: fetchMarketData,\n          disabled: loading,\n          children: [loading ? '⟳' : '🔄', \" Refresh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      children: [\"\\u274C \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 9\n    }, this), loading && Object.keys(marketData).length === 0 ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      children: \"Loading live market data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(MarketGrid, {\n      children: Object.entries(marketData).map(([symbol, data]) => {\n        // Add data validation\n        if (!data || typeof data !== 'object') {\n          console.warn(`Invalid data for symbol ${symbol}:`, data);\n          return null;\n        }\n        const {\n          change,\n          changePercent\n        } = calculateChange(data.open, data.close);\n        const isPositive = change >= 0;\n        return /*#__PURE__*/_jsxDEV(PairCard, {\n          children: [/*#__PURE__*/_jsxDEV(PairHeader, {\n            children: [/*#__PURE__*/_jsxDEV(PairSymbol, {\n              children: symbol || 'Unknown'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(PairPrice, {\n              children: formatPrice(data.close || data.price)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(PairStats, {\n            children: [/*#__PURE__*/_jsxDEV(StatItem, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Change:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                type: isPositive ? 'positive' : 'negative',\n                children: [isPositive ? '+' : '', (change || 0).toFixed(6), \" (\", (changePercent || 0).toFixed(2), \"%)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Volume:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                type: \"neutral\",\n                children: formatVolume(data.volume || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"High:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                type: \"positive\",\n                children: formatPrice(data.high || data.close || data.price || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Low:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                type: \"negative\",\n                children: formatPrice(data.low || data.close || data.price || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Open:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                type: \"neutral\",\n                children: formatPrice(data.open || data.close || data.price || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"VWAP:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                type: \"neutral\",\n                children: formatPrice(data.vwap || data.close || data.price || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 17\n          }, this)]\n        }, symbol, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 9\n    }, this), Object.keys(marketData).length === 0 && !loading && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        color: '#888',\n        padding: '40px'\n      },\n      children: \"No market data available. Click refresh to fetch live data.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 426,\n    columnNumber: 5\n  }, this);\n};\n_s(AlpacaMarketData, \"T1dIDkdhar+nOxHceDz1UcUQ9FI=\");\n_c15 = AlpacaMarketData;\nexport default AlpacaMarketData;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"MarketDataContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"RefreshButton\");\n$RefreshReg$(_c5, \"StatusIndicator\");\n$RefreshReg$(_c6, \"StatusDot\");\n$RefreshReg$(_c7, \"MarketGrid\");\n$RefreshReg$(_c8, \"PairCard\");\n$RefreshReg$(_c9, \"PairHeader\");\n$RefreshReg$(_c0, \"PairSymbol\");\n$RefreshReg$(_c1, \"PairPrice\");\n$RefreshReg$(_c10, \"PairStats\");\n$RefreshReg$(_c11, \"StatItem\");\n$RefreshReg$(_c12, \"StatValue\");\n$RefreshReg$(_c13, \"ErrorMessage\");\n$RefreshReg$(_c14, \"LoadingSpinner\");\n$RefreshReg$(_c15, \"AlpacaMarketData\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "jsxDEV", "_jsxDEV", "MarketDataContainer", "div", "_c", "Header", "_c2", "Title", "h3", "_c3", "RefreshButton", "button", "_c4", "StatusIndicator", "_c5", "StatusDot", "props", "$status", "_c6", "MarketGrid", "_c7", "PairCard", "_c8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c9", "PairSymbol", "h4", "_c0", "PairPrice", "_c1", "PairStats", "_c10", "StatItem", "_c11", "StatValue", "span", "type", "_c12", "ErrorMessage", "_c13", "LoadingSpinner", "_c14", "AlpacaMarketData", "_s", "marketData", "setMarketData", "loading", "setLoading", "error", "setError", "lastUpdate", "setLastUpdate", "autoRefresh", "setAutoRefresh", "fetchMarketData", "response", "fetch", "result", "json", "success", "data", "Date", "timestamp", "console", "log", "Object", "keys", "length", "source", "message", "firstPair", "err", "ws", "reconnectAttempts", "maxReconnectAttempts", "connectWebSocket", "WebSocket", "onopen", "send", "JSON", "stringify", "channels", "onmessage", "event", "parse", "updateData", "processMarketUpdate", "candlestick", "price", "undefined", "processDirectMarketData", "symbol", "close", "debug", "isNaN", "warn", "prevData", "open", "high", "low", "volume", "toISOString", "change", "price_change", "change_percent", "price_change_pct", "window", "dispatchEvent", "CustomEvent", "detail", "time", "Math", "floor", "now", "getTime", "toFixed", "onclose", "code", "reason", "setTimeout", "onerror", "interval", "setInterval", "clearInterval", "formatPrice", "numPrice", "Number", "formatVolume", "numVolume", "calculateChange", "changePercent", "numOpen", "numClose", "children", "style", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleTimeString", "fontSize", "color", "checked", "onChange", "e", "target", "onClick", "disabled", "entries", "map", "isPositive", "vwap", "textAlign", "padding", "_c15", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/AlpacaMarketData.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\nconst MarketDataContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n`;\n\nconst Title = styled.h3`\n  color: #4bffb5;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n`;\n\nconst RefreshButton = styled.button`\n  background: #4bffb5;\n  color: #000;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: #3de89f;\n  }\n  \n  &:disabled {\n    background: #666;\n    cursor: not-allowed;\n  }\n`;\n\nconst StatusIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: #888;\n`;\n\nconst StatusDot = styled.div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${props => props.$status === 'live' ? '#4bffb5' : props.$status === 'error' ? '#ff4976' : '#ffa726'};\n  animation: ${props => props.$status === 'live' ? 'pulse 2s infinite' : 'none'};\n\n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n`;\n\nconst MarketGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 16px;\n`;\n\nconst PairCard = styled.div`\n  background: #0a0a0a;\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid #333;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    border-color: #4bffb5;\n    transform: translateY(-2px);\n  }\n`;\n\nconst PairHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n`;\n\nconst PairSymbol = styled.h4`\n  color: #fff;\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n`;\n\nconst PairPrice = styled.div`\n  font-size: 20px;\n  font-weight: 700;\n  color: #4bffb5;\n  font-family: 'Courier New', monospace;\n`;\n\nconst PairStats = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n  margin-top: 12px;\n`;\n\nconst StatItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  color: #888;\n`;\n\nconst StatValue = styled.span`\n  color: ${props => {\n    if (props.type === 'positive') return '#4bffb5';\n    if (props.type === 'negative') return '#ff4976';\n    return '#ffa726';\n  }};\n  font-weight: 600;\n`;\n\nconst ErrorMessage = styled.div`\n  background: rgba(255, 73, 118, 0.1);\n  border: 1px solid #ff4976;\n  border-radius: 8px;\n  padding: 16px;\n  color: #ff4976;\n  text-align: center;\n  margin-top: 16px;\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: #888;\n  font-size: 14px;\n`;\n\nconst AlpacaMarketData = () => {\n  const [marketData, setMarketData] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(true);\n\n  const fetchMarketData = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('http://localhost:8000/api/alpaca/live-market-data');\n      const result = await response.json();\n\n      if (result.success) {\n        setMarketData(result.data);\n        setLastUpdate(new Date(result.timestamp));\n        console.log('🚀 REAL Alpaca market data updated:', Object.keys(result.data).length, 'pairs');\n        console.log('✅ Source:', result.source, '- Message:', result.message);\n\n        // Log sample data to verify it's real\n        const firstPair = Object.keys(result.data)[0];\n        if (firstPair) {\n          console.log('📊 Sample data for', firstPair, ':', result.data[firstPair]);\n        }\n      } else {\n        setError(result.error || 'Failed to fetch real Alpaca market data');\n        console.error('❌ Alpaca API Error:', result.error);\n      }\n    } catch (err) {\n      setError('Network error: ' + err.message);\n      console.error('❌ Error fetching Alpaca market data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchMarketData();\n\n    // Set up WebSocket for real-time updates with retry logic\n    let ws;\n    let reconnectAttempts = 0;\n    const maxReconnectAttempts = 5;\n\n    const connectWebSocket = () => {\n      try {\n        ws = new WebSocket('ws://localhost:8000/ws');\n\n        ws.onopen = () => {\n          console.log('🚀 WebSocket connected for real-time market data');\n          reconnectAttempts = 0; // Reset on successful connection\n          ws.send(JSON.stringify({\n            type: 'subscribe',\n            channels: ['market_data']\n          }));\n        };\n\n        ws.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data);\n\n            // Handle different WebSocket message formats\n            if (data.type === 'market_data_update' || data.type === 'market_update') {\n              // Handle typed messages\n              const updateData = data.data || data;\n              processMarketUpdate(updateData);\n            } else if (data.type === 'connection_established') {\n              console.log('✅ WebSocket connection confirmed:', data.message);\n            } else if (data.candlestick && data.price !== undefined) {\n              // Handle direct market data format (new format from backend)\n              processDirectMarketData(data);\n            } else if (data.symbol && (data.price !== undefined || data.close !== undefined)) {\n              // Handle simple symbol-price format\n              processMarketUpdate(data);\n            } else {\n              // Only warn if it's truly unrecognized data\n              console.debug('Unrecognized WebSocket message format:', data);\n            }\n          } catch (err) {\n            console.error('Error processing WebSocket message:', err);\n          }\n        };\n\n        // Function to process direct market data format\n        const processDirectMarketData = (data) => {\n          try {\n            const candlestick = data.candlestick;\n            const symbol = 'XRP/USD'; // Default symbol, should be extracted from data if available\n            const price = data.price;\n\n            if (!candlestick || price === null || price === undefined || isNaN(price)) {\n              console.warn('Invalid direct market data:', data);\n              return;\n            }\n\n            // Update market data\n            setMarketData(prevData => ({\n              ...prevData,\n              [symbol]: {\n                symbol: symbol,\n                price: price,\n                open: candlestick.open || price,\n                high: candlestick.high || price,\n                low: candlestick.low || price,\n                close: candlestick.close || price,\n                volume: candlestick.volume || 0,\n                timestamp: candlestick.timestamp || new Date().toISOString(),\n                source: 'websocket_direct',\n                change: data.price_change || 0,\n                change_percent: data.price_change_pct || 0\n              }\n            }));\n\n            console.log('📊 Direct market update:', symbol, '@', price);\n            setLastUpdate(new Date());\n\n            // Trigger chart update event\n            window.dispatchEvent(new CustomEvent('marketDataUpdate', {\n              detail: {\n                symbol: symbol,\n                candlestick: {\n                  time: Math.floor(new Date(candlestick.timestamp || Date.now()).getTime() / 1000),\n                  open: candlestick.open || price,\n                  high: candlestick.high || price,\n                  low: candlestick.low || price,\n                  close: candlestick.close || price,\n                  volume: candlestick.volume || 0\n                }\n              }\n            }));\n          } catch (err) {\n            console.error('Error processing direct market data:', err);\n          }\n        };\n\n        // Function to process standard market updates\n        const processMarketUpdate = (updateData) => {\n          try {\n            // Validate updateData has required fields\n            if (!updateData || (!updateData.symbol && !updateData.candlestick)) {\n              console.warn('Invalid market update data:', updateData);\n              return;\n            }\n\n            const symbol = updateData.symbol || 'XRP/USD'; // Default if not provided\n            const price = updateData.price || updateData.close;\n\n            if (price === null || price === undefined || isNaN(price)) {\n              console.warn('Invalid price data for symbol:', symbol, price);\n              return;\n            }\n\n            // Update market data with real-time tick\n            setMarketData(prevData => ({\n              ...prevData,\n              [symbol]: {\n                symbol: symbol,\n                price: price,\n                open: updateData.open || price,\n                high: updateData.high || price,\n                low: updateData.low || price,\n                close: updateData.close || price,\n                volume: updateData.volume || 0,\n                timestamp: updateData.timestamp || new Date().toISOString(),\n                source: updateData.source || 'websocket',\n                change: (price - (updateData.open || price)),\n                change_percent: updateData.open ? (((price - updateData.open) / updateData.open) * 100).toFixed(2) : '0.00'\n              }\n            }));\n\n            console.log('📊 Real-time update:', symbol, '@', price);\n            setLastUpdate(new Date());\n\n            // Trigger chart update event for any listening charts\n            window.dispatchEvent(new CustomEvent('marketDataUpdate', {\n              detail: {\n                symbol: symbol,\n                candlestick: {\n                  time: Math.floor(new Date(updateData.timestamp || Date.now()).getTime() / 1000),\n                  open: updateData.open || price,\n                  high: updateData.high || price,\n                  low: updateData.low || price,\n                  close: updateData.close || price,\n                  volume: updateData.volume || 0\n                }\n              }\n            }));\n          } catch (err) {\n            console.error('Error processing market update:', err);\n          }\n        };\n\n        ws.onclose = (event) => {\n          console.log('WebSocket disconnected', event.code, event.reason);\n\n          // Attempt to reconnect if not intentionally closed\n          if (reconnectAttempts < maxReconnectAttempts && event.code !== 1000) {\n            reconnectAttempts++;\n            console.log(`Attempting to reconnect WebSocket (${reconnectAttempts}/${maxReconnectAttempts})...`);\n            setTimeout(connectWebSocket, 2000 * reconnectAttempts); // Exponential backoff\n          }\n        };\n\n        ws.onerror = (error) => {\n          console.error('WebSocket error:', error);\n        };\n      } catch (error) {\n        console.error('Failed to create WebSocket connection:', error);\n      }\n    };\n\n    // Initial connection with delay to ensure backend is ready\n    setTimeout(connectWebSocket, 1000);\n\n    // Cleanup on unmount\n    return () => {\n      if (ws) {\n        ws.close(1000, 'Component unmounting'); // Normal closure\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    if (autoRefresh) {\n      const interval = setInterval(fetchMarketData, 30000); // Refresh every 30 seconds\n      return () => clearInterval(interval);\n    }\n  }, [autoRefresh]);\n\n  const formatPrice = (price) => {\n    // Add null safety check\n    if (price === null || price === undefined || isNaN(price)) {\n      return '$0.0000';\n    }\n\n    const numPrice = Number(price);\n    if (numPrice >= 1) {\n      return `$${numPrice.toFixed(4)}`;\n    } else {\n      return `$${numPrice.toFixed(6)}`;\n    }\n  };\n\n  const formatVolume = (volume) => {\n    // Add null safety check\n    if (volume === null || volume === undefined || isNaN(volume)) {\n      return '0';\n    }\n\n    const numVolume = Number(volume);\n    if (numVolume >= 1000000) {\n      return `${(numVolume / 1000000).toFixed(2)}M`;\n    } else if (numVolume >= 1000) {\n      return `${(numVolume / 1000).toFixed(2)}K`;\n    }\n    return numVolume.toFixed(0);\n  };\n\n  const calculateChange = (open, close) => {\n    // Add null safety checks\n    if (open === null || open === undefined || isNaN(open) ||\n        close === null || close === undefined || isNaN(close)) {\n      return { change: 0, changePercent: 0 };\n    }\n\n    const numOpen = Number(open);\n    const numClose = Number(close);\n    const change = numClose - numOpen;\n    const changePercent = numOpen !== 0 ? (change / numOpen) * 100 : 0;\n    return { change, changePercent };\n  };\n\n  return (\n    <MarketDataContainer>\n      <Header>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <Title>🚀 Live Alpaca Market Data</Title>\n          <StatusIndicator>\n            <StatusDot $status={error ? 'error' : loading ? 'loading' : 'live'} />\n            <span>\n              {error ? 'Error' : loading ? 'Loading...' : 'Live'}\n              {lastUpdate && ` • ${lastUpdate.toLocaleTimeString()}`}\n            </span>\n          </StatusIndicator>\n        </div>\n        \n        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>\n          <label style={{ fontSize: '12px', color: '#888', display: 'flex', alignItems: 'center', gap: '4px' }}>\n            <input\n              type=\"checkbox\"\n              checked={autoRefresh}\n              onChange={(e) => setAutoRefresh(e.target.checked)}\n            />\n            Auto-refresh\n          </label>\n          <RefreshButton onClick={fetchMarketData} disabled={loading}>\n            {loading ? '⟳' : '🔄'} Refresh\n          </RefreshButton>\n        </div>\n      </Header>\n\n      {error && (\n        <ErrorMessage>\n          ❌ {error}\n        </ErrorMessage>\n      )}\n\n      {loading && Object.keys(marketData).length === 0 ? (\n        <LoadingSpinner>\n          Loading live market data...\n        </LoadingSpinner>\n      ) : (\n        <MarketGrid>\n          {Object.entries(marketData).map(([symbol, data]) => {\n            // Add data validation\n            if (!data || typeof data !== 'object') {\n              console.warn(`Invalid data for symbol ${symbol}:`, data);\n              return null;\n            }\n\n            const { change, changePercent } = calculateChange(data.open, data.close);\n            const isPositive = change >= 0;\n\n            return (\n              <PairCard key={symbol}>\n                <PairHeader>\n                  <PairSymbol>{symbol || 'Unknown'}</PairSymbol>\n                  <PairPrice>{formatPrice(data.close || data.price)}</PairPrice>\n                </PairHeader>\n                \n                <PairStats>\n                  <StatItem>\n                    <span>Change:</span>\n                    <StatValue type={isPositive ? 'positive' : 'negative'}>\n                      {isPositive ? '+' : ''}{(change || 0).toFixed(6)} ({(changePercent || 0).toFixed(2)}%)\n                    </StatValue>\n                  </StatItem>\n\n                  <StatItem>\n                    <span>Volume:</span>\n                    <StatValue type=\"neutral\">{formatVolume(data.volume || 0)}</StatValue>\n                  </StatItem>\n\n                  <StatItem>\n                    <span>High:</span>\n                    <StatValue type=\"positive\">{formatPrice(data.high || data.close || data.price || 0)}</StatValue>\n                  </StatItem>\n\n                  <StatItem>\n                    <span>Low:</span>\n                    <StatValue type=\"negative\">{formatPrice(data.low || data.close || data.price || 0)}</StatValue>\n                  </StatItem>\n\n                  <StatItem>\n                    <span>Open:</span>\n                    <StatValue type=\"neutral\">{formatPrice(data.open || data.close || data.price || 0)}</StatValue>\n                  </StatItem>\n\n                  <StatItem>\n                    <span>VWAP:</span>\n                    <StatValue type=\"neutral\">{formatPrice(data.vwap || data.close || data.price || 0)}</StatValue>\n                  </StatItem>\n                </PairStats>\n              </PairCard>\n            );\n          })}\n        </MarketGrid>\n      )}\n      \n      {Object.keys(marketData).length === 0 && !loading && !error && (\n        <div style={{ textAlign: 'center', color: '#888', padding: '40px' }}>\n          No market data available. Click refresh to fetch live data.\n        </div>\n      )}\n    </MarketDataContainer>\n  );\n};\n\nexport default AlpacaMarketData;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,mBAAmB,GAAGH,MAAM,CAACI,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,mBAAmB;AAQzB,MAAMG,MAAM,GAAGN,MAAM,CAACI,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,MAAM;AAOZ,MAAME,KAAK,GAAGR,MAAM,CAACS,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,aAAa,GAAGX,MAAM,CAACY,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,aAAa;AAqBnB,MAAMG,eAAe,GAAGd,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GANID,eAAe;AAQrB,MAAME,SAAS,GAAGhB,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,gBAAgBa,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,MAAM,GAAG,SAAS,GAAGD,KAAK,CAACC,OAAO,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;AACjH,eAAeD,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,MAAM,GAAG,mBAAmB,GAAG,MAAM;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,SAAS;AAcf,MAAMI,UAAU,GAAGpB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAJID,UAAU;AAMhB,MAAME,QAAQ,GAAGtB,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAXID,QAAQ;AAad,MAAME,UAAU,GAAGxB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GALID,UAAU;AAOhB,MAAME,UAAU,GAAG1B,MAAM,CAAC2B,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,UAAU;AAOhB,MAAMG,SAAS,GAAG7B,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GALID,SAAS;AAOf,MAAME,SAAS,GAAG/B,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGjC,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GALID,QAAQ;AAOd,MAAME,SAAS,GAAGnC,MAAM,CAACoC,IAAI;AAC7B,WAAWnB,KAAK,IAAI;EAChB,IAAIA,KAAK,CAACoB,IAAI,KAAK,UAAU,EAAE,OAAO,SAAS;EAC/C,IAAIpB,KAAK,CAACoB,IAAI,KAAK,UAAU,EAAE,OAAO,SAAS;EAC/C,OAAO,SAAS;AAClB,CAAC;AACH;AACA,CAAC;AAACC,IAAA,GAPIH,SAAS;AASf,MAAMI,YAAY,GAAGvC,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GARID,YAAY;AAUlB,MAAME,cAAc,GAAGzC,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GAPID,cAAc;AASpB,MAAME,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAEpD,MAAMyD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCP,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,mDAAmD,CAAC;MACjF,MAAMC,MAAM,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAEpC,IAAID,MAAM,CAACE,OAAO,EAAE;QAClBd,aAAa,CAACY,MAAM,CAACG,IAAI,CAAC;QAC1BT,aAAa,CAAC,IAAIU,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAAC;QACzCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,MAAM,CAACC,IAAI,CAACT,MAAM,CAACG,IAAI,CAAC,CAACO,MAAM,EAAE,OAAO,CAAC;QAC5FJ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEP,MAAM,CAACW,MAAM,EAAE,YAAY,EAAEX,MAAM,CAACY,OAAO,CAAC;;QAErE;QACA,MAAMC,SAAS,GAAGL,MAAM,CAACC,IAAI,CAACT,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAIU,SAAS,EAAE;UACbP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,SAAS,EAAE,GAAG,EAAEb,MAAM,CAACG,IAAI,CAACU,SAAS,CAAC,CAAC;QAC3E;MACF,CAAC,MAAM;QACLrB,QAAQ,CAACQ,MAAM,CAACT,KAAK,IAAI,yCAAyC,CAAC;QACnEe,OAAO,CAACf,KAAK,CAAC,qBAAqB,EAAES,MAAM,CAACT,KAAK,CAAC;MACpD;IACF,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZtB,QAAQ,CAAC,iBAAiB,GAAGsB,GAAG,CAACF,OAAO,CAAC;MACzCN,OAAO,CAACf,KAAK,CAAC,sCAAsC,EAAEuB,GAAG,CAAC;IAC5D,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACdwD,eAAe,CAAC,CAAC;;IAEjB;IACA,IAAIkB,EAAE;IACN,IAAIC,iBAAiB,GAAG,CAAC;IACzB,MAAMC,oBAAoB,GAAG,CAAC;IAE9B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAI;QACFH,EAAE,GAAG,IAAII,SAAS,CAAC,wBAAwB,CAAC;QAE5CJ,EAAE,CAACK,MAAM,GAAG,MAAM;UAChBd,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;UAC/DS,iBAAiB,GAAG,CAAC,CAAC,CAAC;UACvBD,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;YACrB5C,IAAI,EAAE,WAAW;YACjB6C,QAAQ,EAAE,CAAC,aAAa;UAC1B,CAAC,CAAC,CAAC;QACL,CAAC;QAEDT,EAAE,CAACU,SAAS,GAAIC,KAAK,IAAK;UACxB,IAAI;YACF,MAAMvB,IAAI,GAAGmB,IAAI,CAACK,KAAK,CAACD,KAAK,CAACvB,IAAI,CAAC;;YAEnC;YACA,IAAIA,IAAI,CAACxB,IAAI,KAAK,oBAAoB,IAAIwB,IAAI,CAACxB,IAAI,KAAK,eAAe,EAAE;cACvE;cACA,MAAMiD,UAAU,GAAGzB,IAAI,CAACA,IAAI,IAAIA,IAAI;cACpC0B,mBAAmB,CAACD,UAAU,CAAC;YACjC,CAAC,MAAM,IAAIzB,IAAI,CAACxB,IAAI,KAAK,wBAAwB,EAAE;cACjD2B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEJ,IAAI,CAACS,OAAO,CAAC;YAChE,CAAC,MAAM,IAAIT,IAAI,CAAC2B,WAAW,IAAI3B,IAAI,CAAC4B,KAAK,KAAKC,SAAS,EAAE;cACvD;cACAC,uBAAuB,CAAC9B,IAAI,CAAC;YAC/B,CAAC,MAAM,IAAIA,IAAI,CAAC+B,MAAM,KAAK/B,IAAI,CAAC4B,KAAK,KAAKC,SAAS,IAAI7B,IAAI,CAACgC,KAAK,KAAKH,SAAS,CAAC,EAAE;cAChF;cACAH,mBAAmB,CAAC1B,IAAI,CAAC;YAC3B,CAAC,MAAM;cACL;cACAG,OAAO,CAAC8B,KAAK,CAAC,wCAAwC,EAAEjC,IAAI,CAAC;YAC/D;UACF,CAAC,CAAC,OAAOW,GAAG,EAAE;YACZR,OAAO,CAACf,KAAK,CAAC,qCAAqC,EAAEuB,GAAG,CAAC;UAC3D;QACF,CAAC;;QAED;QACA,MAAMmB,uBAAuB,GAAI9B,IAAI,IAAK;UACxC,IAAI;YACF,MAAM2B,WAAW,GAAG3B,IAAI,CAAC2B,WAAW;YACpC,MAAMI,MAAM,GAAG,SAAS,CAAC,CAAC;YAC1B,MAAMH,KAAK,GAAG5B,IAAI,CAAC4B,KAAK;YAExB,IAAI,CAACD,WAAW,IAAIC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIK,KAAK,CAACN,KAAK,CAAC,EAAE;cACzEzB,OAAO,CAACgC,IAAI,CAAC,6BAA6B,EAAEnC,IAAI,CAAC;cACjD;YACF;;YAEA;YACAf,aAAa,CAACmD,QAAQ,KAAK;cACzB,GAAGA,QAAQ;cACX,CAACL,MAAM,GAAG;gBACRA,MAAM,EAAEA,MAAM;gBACdH,KAAK,EAAEA,KAAK;gBACZS,IAAI,EAAEV,WAAW,CAACU,IAAI,IAAIT,KAAK;gBAC/BU,IAAI,EAAEX,WAAW,CAACW,IAAI,IAAIV,KAAK;gBAC/BW,GAAG,EAAEZ,WAAW,CAACY,GAAG,IAAIX,KAAK;gBAC7BI,KAAK,EAAEL,WAAW,CAACK,KAAK,IAAIJ,KAAK;gBACjCY,MAAM,EAAEb,WAAW,CAACa,MAAM,IAAI,CAAC;gBAC/BtC,SAAS,EAAEyB,WAAW,CAACzB,SAAS,IAAI,IAAID,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;gBAC5DjC,MAAM,EAAE,kBAAkB;gBAC1BkC,MAAM,EAAE1C,IAAI,CAAC2C,YAAY,IAAI,CAAC;gBAC9BC,cAAc,EAAE5C,IAAI,CAAC6C,gBAAgB,IAAI;cAC3C;YACF,CAAC,CAAC,CAAC;YAEH1C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE2B,MAAM,EAAE,GAAG,EAAEH,KAAK,CAAC;YAC3DrC,aAAa,CAAC,IAAIU,IAAI,CAAC,CAAC,CAAC;;YAEzB;YACA6C,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,kBAAkB,EAAE;cACvDC,MAAM,EAAE;gBACNlB,MAAM,EAAEA,MAAM;gBACdJ,WAAW,EAAE;kBACXuB,IAAI,EAAEC,IAAI,CAACC,KAAK,CAAC,IAAInD,IAAI,CAAC0B,WAAW,CAACzB,SAAS,IAAID,IAAI,CAACoD,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;kBAChFjB,IAAI,EAAEV,WAAW,CAACU,IAAI,IAAIT,KAAK;kBAC/BU,IAAI,EAAEX,WAAW,CAACW,IAAI,IAAIV,KAAK;kBAC/BW,GAAG,EAAEZ,WAAW,CAACY,GAAG,IAAIX,KAAK;kBAC7BI,KAAK,EAAEL,WAAW,CAACK,KAAK,IAAIJ,KAAK;kBACjCY,MAAM,EAAEb,WAAW,CAACa,MAAM,IAAI;gBAChC;cACF;YACF,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,OAAO7B,GAAG,EAAE;YACZR,OAAO,CAACf,KAAK,CAAC,sCAAsC,EAAEuB,GAAG,CAAC;UAC5D;QACF,CAAC;;QAED;QACA,MAAMe,mBAAmB,GAAID,UAAU,IAAK;UAC1C,IAAI;YACF;YACA,IAAI,CAACA,UAAU,IAAK,CAACA,UAAU,CAACM,MAAM,IAAI,CAACN,UAAU,CAACE,WAAY,EAAE;cAClExB,OAAO,CAACgC,IAAI,CAAC,6BAA6B,EAAEV,UAAU,CAAC;cACvD;YACF;YAEA,MAAMM,MAAM,GAAGN,UAAU,CAACM,MAAM,IAAI,SAAS,CAAC,CAAC;YAC/C,MAAMH,KAAK,GAAGH,UAAU,CAACG,KAAK,IAAIH,UAAU,CAACO,KAAK;YAElD,IAAIJ,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIK,KAAK,CAACN,KAAK,CAAC,EAAE;cACzDzB,OAAO,CAACgC,IAAI,CAAC,gCAAgC,EAAEJ,MAAM,EAAEH,KAAK,CAAC;cAC7D;YACF;;YAEA;YACA3C,aAAa,CAACmD,QAAQ,KAAK;cACzB,GAAGA,QAAQ;cACX,CAACL,MAAM,GAAG;gBACRA,MAAM,EAAEA,MAAM;gBACdH,KAAK,EAAEA,KAAK;gBACZS,IAAI,EAAEZ,UAAU,CAACY,IAAI,IAAIT,KAAK;gBAC9BU,IAAI,EAAEb,UAAU,CAACa,IAAI,IAAIV,KAAK;gBAC9BW,GAAG,EAAEd,UAAU,CAACc,GAAG,IAAIX,KAAK;gBAC5BI,KAAK,EAAEP,UAAU,CAACO,KAAK,IAAIJ,KAAK;gBAChCY,MAAM,EAAEf,UAAU,CAACe,MAAM,IAAI,CAAC;gBAC9BtC,SAAS,EAAEuB,UAAU,CAACvB,SAAS,IAAI,IAAID,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;gBAC3DjC,MAAM,EAAEiB,UAAU,CAACjB,MAAM,IAAI,WAAW;gBACxCkC,MAAM,EAAGd,KAAK,IAAIH,UAAU,CAACY,IAAI,IAAIT,KAAK,CAAE;gBAC5CgB,cAAc,EAAEnB,UAAU,CAACY,IAAI,GAAG,CAAE,CAACT,KAAK,GAAGH,UAAU,CAACY,IAAI,IAAIZ,UAAU,CAACY,IAAI,GAAI,GAAG,EAAEkB,OAAO,CAAC,CAAC,CAAC,GAAG;cACvG;YACF,CAAC,CAAC,CAAC;YAEHpD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE2B,MAAM,EAAE,GAAG,EAAEH,KAAK,CAAC;YACvDrC,aAAa,CAAC,IAAIU,IAAI,CAAC,CAAC,CAAC;;YAEzB;YACA6C,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,kBAAkB,EAAE;cACvDC,MAAM,EAAE;gBACNlB,MAAM,EAAEA,MAAM;gBACdJ,WAAW,EAAE;kBACXuB,IAAI,EAAEC,IAAI,CAACC,KAAK,CAAC,IAAInD,IAAI,CAACwB,UAAU,CAACvB,SAAS,IAAID,IAAI,CAACoD,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;kBAC/EjB,IAAI,EAAEZ,UAAU,CAACY,IAAI,IAAIT,KAAK;kBAC9BU,IAAI,EAAEb,UAAU,CAACa,IAAI,IAAIV,KAAK;kBAC9BW,GAAG,EAAEd,UAAU,CAACc,GAAG,IAAIX,KAAK;kBAC5BI,KAAK,EAAEP,UAAU,CAACO,KAAK,IAAIJ,KAAK;kBAChCY,MAAM,EAAEf,UAAU,CAACe,MAAM,IAAI;gBAC/B;cACF;YACF,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,OAAO7B,GAAG,EAAE;YACZR,OAAO,CAACf,KAAK,CAAC,iCAAiC,EAAEuB,GAAG,CAAC;UACvD;QACF,CAAC;QAEDC,EAAE,CAAC4C,OAAO,GAAIjC,KAAK,IAAK;UACtBpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,KAAK,CAACkC,IAAI,EAAElC,KAAK,CAACmC,MAAM,CAAC;;UAE/D;UACA,IAAI7C,iBAAiB,GAAGC,oBAAoB,IAAIS,KAAK,CAACkC,IAAI,KAAK,IAAI,EAAE;YACnE5C,iBAAiB,EAAE;YACnBV,OAAO,CAACC,GAAG,CAAC,sCAAsCS,iBAAiB,IAAIC,oBAAoB,MAAM,CAAC;YAClG6C,UAAU,CAAC5C,gBAAgB,EAAE,IAAI,GAAGF,iBAAiB,CAAC,CAAC,CAAC;UAC1D;QACF,CAAC;QAEDD,EAAE,CAACgD,OAAO,GAAIxE,KAAK,IAAK;UACtBe,OAAO,CAACf,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QAC1C,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;IACF,CAAC;;IAED;IACAuE,UAAU,CAAC5C,gBAAgB,EAAE,IAAI,CAAC;;IAElC;IACA,OAAO,MAAM;MACX,IAAIH,EAAE,EAAE;QACNA,EAAE,CAACoB,KAAK,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN9F,SAAS,CAAC,MAAM;IACd,IAAIsD,WAAW,EAAE;MACf,MAAMqE,QAAQ,GAAGC,WAAW,CAACpE,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC;MACtD,OAAO,MAAMqE,aAAa,CAACF,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACrE,WAAW,CAAC,CAAC;EAEjB,MAAMwE,WAAW,GAAIpC,KAAK,IAAK;IAC7B;IACA,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIK,KAAK,CAACN,KAAK,CAAC,EAAE;MACzD,OAAO,SAAS;IAClB;IAEA,MAAMqC,QAAQ,GAAGC,MAAM,CAACtC,KAAK,CAAC;IAC9B,IAAIqC,QAAQ,IAAI,CAAC,EAAE;MACjB,OAAO,IAAIA,QAAQ,CAACV,OAAO,CAAC,CAAC,CAAC,EAAE;IAClC,CAAC,MAAM;MACL,OAAO,IAAIU,QAAQ,CAACV,OAAO,CAAC,CAAC,CAAC,EAAE;IAClC;EACF,CAAC;EAED,MAAMY,YAAY,GAAI3B,MAAM,IAAK;IAC/B;IACA,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKX,SAAS,IAAIK,KAAK,CAACM,MAAM,CAAC,EAAE;MAC5D,OAAO,GAAG;IACZ;IAEA,MAAM4B,SAAS,GAAGF,MAAM,CAAC1B,MAAM,CAAC;IAChC,IAAI4B,SAAS,IAAI,OAAO,EAAE;MACxB,OAAO,GAAG,CAACA,SAAS,GAAG,OAAO,EAAEb,OAAO,CAAC,CAAC,CAAC,GAAG;IAC/C,CAAC,MAAM,IAAIa,SAAS,IAAI,IAAI,EAAE;MAC5B,OAAO,GAAG,CAACA,SAAS,GAAG,IAAI,EAAEb,OAAO,CAAC,CAAC,CAAC,GAAG;IAC5C;IACA,OAAOa,SAAS,CAACb,OAAO,CAAC,CAAC,CAAC;EAC7B,CAAC;EAED,MAAMc,eAAe,GAAGA,CAAChC,IAAI,EAAEL,KAAK,KAAK;IACvC;IACA,IAAIK,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKR,SAAS,IAAIK,KAAK,CAACG,IAAI,CAAC,IAClDL,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKH,SAAS,IAAIK,KAAK,CAACF,KAAK,CAAC,EAAE;MACzD,OAAO;QAAEU,MAAM,EAAE,CAAC;QAAE4B,aAAa,EAAE;MAAE,CAAC;IACxC;IAEA,MAAMC,OAAO,GAAGL,MAAM,CAAC7B,IAAI,CAAC;IAC5B,MAAMmC,QAAQ,GAAGN,MAAM,CAAClC,KAAK,CAAC;IAC9B,MAAMU,MAAM,GAAG8B,QAAQ,GAAGD,OAAO;IACjC,MAAMD,aAAa,GAAGC,OAAO,KAAK,CAAC,GAAI7B,MAAM,GAAG6B,OAAO,GAAI,GAAG,GAAG,CAAC;IAClE,OAAO;MAAE7B,MAAM;MAAE4B;IAAc,CAAC;EAClC,CAAC;EAED,oBACEjI,OAAA,CAACC,mBAAmB;IAAAmI,QAAA,gBAClBpI,OAAA,CAACI,MAAM;MAAAgI,QAAA,gBACLpI,OAAA;QAAKqI,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjEpI,OAAA,CAACM,KAAK;UAAA8H,QAAA,EAAC;QAA0B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzC5I,OAAA,CAACY,eAAe;UAAAwH,QAAA,gBACdpI,OAAA,CAACc,SAAS;YAACE,OAAO,EAAE+B,KAAK,GAAG,OAAO,GAAGF,OAAO,GAAG,SAAS,GAAG;UAAO;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtE5I,OAAA;YAAAoI,QAAA,GACGrF,KAAK,GAAG,OAAO,GAAGF,OAAO,GAAG,YAAY,GAAG,MAAM,EACjDI,UAAU,IAAI,MAAMA,UAAU,CAAC4F,kBAAkB,CAAC,CAAC,EAAE;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAEN5I,OAAA;QAAKqI,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,KAAK;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBAChEpI,OAAA;UAAOqI,KAAK,EAAE;YAAES,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAET,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAJ,QAAA,gBACnGpI,OAAA;YACEmC,IAAI,EAAC,UAAU;YACf6G,OAAO,EAAE7F,WAAY;YACrB8F,QAAQ,EAAGC,CAAC,IAAK9F,cAAc,CAAC8F,CAAC,CAACC,MAAM,CAACH,OAAO;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5I,OAAA,CAACS,aAAa;UAAC2I,OAAO,EAAE/F,eAAgB;UAACgG,QAAQ,EAAExG,OAAQ;UAAAuF,QAAA,GACxDvF,OAAO,GAAG,GAAG,GAAG,IAAI,EAAC,UACxB;QAAA;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAER7F,KAAK,iBACJ/C,OAAA,CAACqC,YAAY;MAAA+F,QAAA,GAAC,SACV,EAACrF,KAAK;IAAA;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACf,EAEA/F,OAAO,IAAImB,MAAM,CAACC,IAAI,CAACtB,UAAU,CAAC,CAACuB,MAAM,KAAK,CAAC,gBAC9ClE,OAAA,CAACuC,cAAc;MAAA6F,QAAA,EAAC;IAEhB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAAC,gBAEjB5I,OAAA,CAACkB,UAAU;MAAAkH,QAAA,EACRpE,MAAM,CAACsF,OAAO,CAAC3G,UAAU,CAAC,CAAC4G,GAAG,CAAC,CAAC,CAAC7D,MAAM,EAAE/B,IAAI,CAAC,KAAK;QAClD;QACA,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UACrCG,OAAO,CAACgC,IAAI,CAAC,2BAA2BJ,MAAM,GAAG,EAAE/B,IAAI,CAAC;UACxD,OAAO,IAAI;QACb;QAEA,MAAM;UAAE0C,MAAM;UAAE4B;QAAc,CAAC,GAAGD,eAAe,CAACrE,IAAI,CAACqC,IAAI,EAAErC,IAAI,CAACgC,KAAK,CAAC;QACxE,MAAM6D,UAAU,GAAGnD,MAAM,IAAI,CAAC;QAE9B,oBACErG,OAAA,CAACoB,QAAQ;UAAAgH,QAAA,gBACPpI,OAAA,CAACsB,UAAU;YAAA8G,QAAA,gBACTpI,OAAA,CAACwB,UAAU;cAAA4G,QAAA,EAAE1C,MAAM,IAAI;YAAS;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC9C5I,OAAA,CAAC2B,SAAS;cAAAyG,QAAA,EAAET,WAAW,CAAChE,IAAI,CAACgC,KAAK,IAAIhC,IAAI,CAAC4B,KAAK;YAAC;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eAEb5I,OAAA,CAAC6B,SAAS;YAAAuG,QAAA,gBACRpI,OAAA,CAAC+B,QAAQ;cAAAqG,QAAA,gBACPpI,OAAA;gBAAAoI,QAAA,EAAM;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpB5I,OAAA,CAACiC,SAAS;gBAACE,IAAI,EAAEqH,UAAU,GAAG,UAAU,GAAG,UAAW;gBAAApB,QAAA,GACnDoB,UAAU,GAAG,GAAG,GAAG,EAAE,EAAE,CAACnD,MAAM,IAAI,CAAC,EAAEa,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAC,CAACe,aAAa,IAAI,CAAC,EAAEf,OAAO,CAAC,CAAC,CAAC,EAAC,IACtF;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEX5I,OAAA,CAAC+B,QAAQ;cAAAqG,QAAA,gBACPpI,OAAA;gBAAAoI,QAAA,EAAM;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpB5I,OAAA,CAACiC,SAAS;gBAACE,IAAI,EAAC,SAAS;gBAAAiG,QAAA,EAAEN,YAAY,CAACnE,IAAI,CAACwC,MAAM,IAAI,CAAC;cAAC;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eAEX5I,OAAA,CAAC+B,QAAQ;cAAAqG,QAAA,gBACPpI,OAAA;gBAAAoI,QAAA,EAAM;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClB5I,OAAA,CAACiC,SAAS;gBAACE,IAAI,EAAC,UAAU;gBAAAiG,QAAA,EAAET,WAAW,CAAChE,IAAI,CAACsC,IAAI,IAAItC,IAAI,CAACgC,KAAK,IAAIhC,IAAI,CAAC4B,KAAK,IAAI,CAAC;cAAC;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC,eAEX5I,OAAA,CAAC+B,QAAQ;cAAAqG,QAAA,gBACPpI,OAAA;gBAAAoI,QAAA,EAAM;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB5I,OAAA,CAACiC,SAAS;gBAACE,IAAI,EAAC,UAAU;gBAAAiG,QAAA,EAAET,WAAW,CAAChE,IAAI,CAACuC,GAAG,IAAIvC,IAAI,CAACgC,KAAK,IAAIhC,IAAI,CAAC4B,KAAK,IAAI,CAAC;cAAC;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eAEX5I,OAAA,CAAC+B,QAAQ;cAAAqG,QAAA,gBACPpI,OAAA;gBAAAoI,QAAA,EAAM;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClB5I,OAAA,CAACiC,SAAS;gBAACE,IAAI,EAAC,SAAS;gBAAAiG,QAAA,EAAET,WAAW,CAAChE,IAAI,CAACqC,IAAI,IAAIrC,IAAI,CAACgC,KAAK,IAAIhC,IAAI,CAAC4B,KAAK,IAAI,CAAC;cAAC;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eAEX5I,OAAA,CAAC+B,QAAQ;cAAAqG,QAAA,gBACPpI,OAAA;gBAAAoI,QAAA,EAAM;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClB5I,OAAA,CAACiC,SAAS;gBAACE,IAAI,EAAC,SAAS;gBAAAiG,QAAA,EAAET,WAAW,CAAChE,IAAI,CAAC8F,IAAI,IAAI9F,IAAI,CAACgC,KAAK,IAAIhC,IAAI,CAAC4B,KAAK,IAAI,CAAC;cAAC;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GAtCClD,MAAM;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuCX,CAAC;MAEf,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CACb,EAEA5E,MAAM,CAACC,IAAI,CAACtB,UAAU,CAAC,CAACuB,MAAM,KAAK,CAAC,IAAI,CAACrB,OAAO,IAAI,CAACE,KAAK,iBACzD/C,OAAA;MAAKqI,KAAK,EAAE;QAAEqB,SAAS,EAAE,QAAQ;QAAEX,KAAK,EAAE,MAAM;QAAEY,OAAO,EAAE;MAAO,CAAE;MAAAvB,QAAA,EAAC;IAErE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAE1B,CAAC;AAAClG,EAAA,CA1XID,gBAAgB;AAAAmH,IAAA,GAAhBnH,gBAAgB;AA4XtB,eAAeA,gBAAgB;AAAC,IAAAtC,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAoH,IAAA;AAAAC,YAAA,CAAA1J,EAAA;AAAA0J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAAtI,GAAA;AAAAsI,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA/H,IAAA;AAAA+H,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAArH,IAAA;AAAAqH,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}