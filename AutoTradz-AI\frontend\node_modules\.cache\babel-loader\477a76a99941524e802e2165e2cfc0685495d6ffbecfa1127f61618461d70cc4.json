{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\DecisionPanel.jsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n_c = PanelContainer;\nconst PanelHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  h3 {\n    margin: 0;\n    color: #4bffb5;\n  }\n`;\n_c2 = PanelHeader;\nconst StatusIndicator = styled.div`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.$active ? '#4bffb5' : '#666'};\n  animation: ${props => props.$active ? 'pulse 2s infinite' : 'none'};\n\n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n`;\n_c3 = StatusIndicator;\nconst DecisionCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 16px;\n  border-left: 4px solid ${props => props.$signal === 'BUY' ? '#4bffb5' : props.$signal === 'SELL' ? '#ff4976' : '#ffa726'};\n`;\n_c4 = DecisionCard;\nconst SignalType = styled.div`\n  font-size: 18px;\n  font-weight: bold;\n  color: ${props => props.$signal === 'BUY' ? '#4bffb5' : props.$signal === 'SELL' ? '#ff4976' : '#ffa726'};\n  margin-bottom: 8px;\n`;\n_c5 = SignalType;\nconst ConfidenceBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: #333;\n  border-radius: 4px;\n  overflow: hidden;\n  margin: 8px 0;\n\n  .fill {\n    height: 100%;\n    background: linear-gradient(90deg, #ff4976, #ffa726, #4bffb5);\n    width: ${props => props.$confidence * 100}%;\n    transition: width 0.3s ease;\n  }\n`;\n_c6 = ConfidenceBar;\nconst MetricRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin: 8px 0;\n  font-size: 14px;\n\n  .label {\n    color: #888;\n  }\n\n  .value {\n    color: #fff;\n    font-weight: 500;\n  }\n`;\n_c7 = MetricRow;\nconst ReasoningText = styled.div`\n  font-size: 13px;\n  color: #ccc;\n  margin-top: 12px;\n  padding: 8px;\n  background: #1a1a1a;\n  border-radius: 4px;\n  border-left: 3px solid #4bffb5;\n`;\n_c8 = ReasoningText;\nconst DataSourceIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: #888;\n  margin-bottom: 16px;\n\n  .indicator {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: ${props => props.$isLive ? '#4bffb5' : '#ffa726'};\n  }\n\n  .source {\n    color: ${props => props.$isLive ? '#4bffb5' : '#ffa726'};\n    font-weight: 500;\n  }\n`;\n_c9 = DataSourceIndicator;\nconst RefreshButton = styled.button`\n  background: #2a2a2a;\n  border: 1px solid #4bffb5;\n  color: #4bffb5;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #4bffb5;\n    color: #1a1a1a;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n`;\n_c0 = RefreshButton;\nconst DecisionPanel = ({\n  signals,\n  isActive,\n  dataSource = 'mock'\n}) => {\n  var _latestSignal$price, _latestSignal$positio;\n  const latestSignal = Array.isArray(signals) && signals.length > 0 ? signals[0] : null;\n  const isLive = dataSource === 'live';\n  const handleRefresh = () => {\n    window.location.reload();\n  };\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"AI Decision Engine\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RefreshButton, {\n          onClick: handleRefresh,\n          title: \"Refresh Page\",\n          children: \"\\uD83D\\uDD04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n          $active: isActive\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DataSourceIndicator, {\n      $isLive: isLive,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"indicator\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Data Source: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"source\",\n          children: isLive ? 'Live Coinbase' : 'Mock Data'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), latestSignal ? /*#__PURE__*/_jsxDEV(DecisionCard, {\n      $signal: latestSignal.signal,\n      children: [/*#__PURE__*/_jsxDEV(SignalType, {\n        $signal: latestSignal.signal,\n        children: latestSignal.signal\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MetricRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Confidence:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [(latestSignal.confidence * 100).toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ConfidenceBar, {\n        $confidence: latestSignal.confidence,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fill\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MetricRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Price:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [\"$\", (_latestSignal$price = latestSignal.price) === null || _latestSignal$price === void 0 ? void 0 : _latestSignal$price.toFixed(4)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MetricRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Risk Score:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [(latestSignal.risk_score * 100).toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MetricRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Position Size:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [(_latestSignal$positio = latestSignal.position_size) === null || _latestSignal$positio === void 0 ? void 0 : _latestSignal$positio.toFixed(4), \" XRP\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MetricRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Strategy:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: latestSignal.strategy\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MetricRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Timestamp:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: new Date(latestSignal.timestamp).toLocaleTimeString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), latestSignal.reasoning && /*#__PURE__*/_jsxDEV(ReasoningText, {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"AI Reasoning:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 15\n        }, this), \" \", latestSignal.reasoning]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(DecisionCard, {\n      $signal: \"HOLD\",\n      children: [/*#__PURE__*/_jsxDEV(SignalType, {\n        $signal: \"HOLD\",\n        children: \"Waiting for market data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ReasoningText, {\n        children: \"The AI is analyzing market conditions and will generate signals once sufficient data is available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 9\n    }, this), signals && signals.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: '#888',\n          fontSize: '14px',\n          marginBottom: '12px'\n        },\n        children: \"Recent Signals\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), signals.slice(1, 4).map((signal, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          padding: '8px 0',\n          borderBottom: '1px solid #333',\n          fontSize: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: signal.signal === 'BUY' ? '#4bffb5' : signal.signal === 'SELL' ? '#ff4976' : '#ffa726'\n          },\n          children: signal.signal\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#888'\n          },\n          children: [(signal.confidence * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#888'\n          },\n          children: new Date(signal.timestamp).toLocaleTimeString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_c1 = DecisionPanel;\nexport default DecisionPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"PanelHeader\");\n$RefreshReg$(_c3, \"StatusIndicator\");\n$RefreshReg$(_c4, \"DecisionCard\");\n$RefreshReg$(_c5, \"SignalType\");\n$RefreshReg$(_c6, \"ConfidenceBar\");\n$RefreshReg$(_c7, \"MetricRow\");\n$RefreshReg$(_c8, \"ReasoningText\");\n$RefreshReg$(_c9, \"DataSourceIndicator\");\n$RefreshReg$(_c0, \"RefreshButton\");\n$RefreshReg$(_c1, \"DecisionPanel\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "PanelContainer", "div", "_c", "PanelHeader", "_c2", "StatusIndicator", "props", "$active", "_c3", "DecisionCard", "$signal", "_c4", "SignalType", "_c5", "ConfidenceBar", "$confidence", "_c6", "MetricRow", "_c7", "ReasoningText", "_c8", "DataSourceIndicator", "$isLive", "_c9", "RefreshButton", "button", "_c0", "DecisionPanel", "signals", "isActive", "dataSource", "_latestSignal$price", "_latestSignal$positio", "latestSignal", "Array", "isArray", "length", "isLive", "handleRefresh", "window", "location", "reload", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "alignItems", "gap", "onClick", "title", "className", "signal", "confidence", "toFixed", "price", "risk_score", "position_size", "strategy", "Date", "timestamp", "toLocaleTimeString", "reasoning", "color", "fontSize", "marginBottom", "slice", "map", "index", "justifyContent", "padding", "borderBottom", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/DecisionPanel.jsx"], "sourcesContent": ["import React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst PanelContainer = styled.div`\r\n  background: #1a1a1a;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  color: #ffffff;\r\n  border: 1px solid #333;\r\n`;\r\n\r\nconst PanelHeader = styled.div`\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    color: #4bffb5;\r\n  }\r\n`;\r\n\r\nconst StatusIndicator = styled.div`\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  background: ${props => props.$active ? '#4bffb5' : '#666'};\r\n  animation: ${props => props.$active ? 'pulse 2s infinite' : 'none'};\r\n\r\n  @keyframes pulse {\r\n    0% { opacity: 1; }\r\n    50% { opacity: 0.5; }\r\n    100% { opacity: 1; }\r\n  }\r\n`;\r\n\r\nconst DecisionCard = styled.div`\r\n  background: #2a2a2a;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  border-left: 4px solid ${props =>\r\n    props.$signal === 'BUY' ? '#4bffb5' :\r\n    props.$signal === 'SELL' ? '#ff4976' : '#ffa726'\r\n  };\r\n`;\r\n\r\nconst SignalType = styled.div`\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: ${props =>\r\n    props.$signal === 'BUY' ? '#4bffb5' :\r\n    props.$signal === 'SELL' ? '#ff4976' : '#ffa726'\r\n  };\r\n  margin-bottom: 8px;\r\n`;\r\n\r\nconst ConfidenceBar = styled.div`\r\n  width: 100%;\r\n  height: 8px;\r\n  background: #333;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  margin: 8px 0;\r\n\r\n  .fill {\r\n    height: 100%;\r\n    background: linear-gradient(90deg, #ff4976, #ffa726, #4bffb5);\r\n    width: ${props => props.$confidence * 100}%;\r\n    transition: width 0.3s ease;\r\n  }\r\n`;\r\n\r\nconst MetricRow = styled.div`\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin: 8px 0;\r\n  font-size: 14px;\r\n\r\n  .label {\r\n    color: #888;\r\n  }\r\n\r\n  .value {\r\n    color: #fff;\r\n    font-weight: 500;\r\n  }\r\n`;\r\n\r\nconst ReasoningText = styled.div`\r\n  font-size: 13px;\r\n  color: #ccc;\r\n  margin-top: 12px;\r\n  padding: 8px;\r\n  background: #1a1a1a;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #4bffb5;\r\n`;\r\n\r\nconst DataSourceIndicator = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n  color: #888;\r\n  margin-bottom: 16px;\r\n\r\n  .indicator {\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    background: ${props => props.$isLive ? '#4bffb5' : '#ffa726'};\r\n  }\r\n\r\n  .source {\r\n    color: ${props => props.$isLive ? '#4bffb5' : '#ffa726'};\r\n    font-weight: 500;\r\n  }\r\n`;\r\n\r\nconst RefreshButton = styled.button`\r\n  background: #2a2a2a;\r\n  border: 1px solid #4bffb5;\r\n  color: #4bffb5;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n\r\n  &:hover {\r\n    background: #4bffb5;\r\n    color: #1a1a1a;\r\n  }\r\n\r\n  &:active {\r\n    transform: scale(0.95);\r\n  }\r\n`;\r\n\r\nconst DecisionPanel = ({ signals, isActive, dataSource = 'mock' }) => {\r\n  const latestSignal = Array.isArray(signals) && signals.length > 0 ? signals[0] : null;\r\n  const isLive = dataSource === 'live';\r\n\r\n  const handleRefresh = () => {\r\n    window.location.reload();\r\n  };\r\n\r\n  return (\r\n    <PanelContainer>\r\n      <PanelHeader>\r\n        <h3>AI Decision Engine</h3>\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\r\n          <RefreshButton onClick={handleRefresh} title=\"Refresh Page\">\r\n            🔄\r\n          </RefreshButton>\r\n          <StatusIndicator $active={isActive} />\r\n        </div>\r\n      </PanelHeader>\r\n\r\n      <DataSourceIndicator $isLive={isLive}>\r\n        <div className=\"indicator\" />\r\n        <span>Data Source: <span className=\"source\">{isLive ? 'Live Coinbase' : 'Mock Data'}</span></span>\r\n      </DataSourceIndicator>\r\n\r\n      {latestSignal ? (\r\n        <DecisionCard $signal={latestSignal.signal}>\r\n          <SignalType $signal={latestSignal.signal}>\r\n            {latestSignal.signal}\r\n          </SignalType>\r\n\r\n          <MetricRow>\r\n            <span className=\"label\">Confidence:</span>\r\n            <span className=\"value\">{(latestSignal.confidence * 100).toFixed(1)}%</span>\r\n          </MetricRow>\r\n\r\n          <ConfidenceBar $confidence={latestSignal.confidence}>\r\n            <div className=\"fill\" />\r\n          </ConfidenceBar>\r\n\r\n          <MetricRow>\r\n            <span className=\"label\">Price:</span>\r\n            <span className=\"value\">${latestSignal.price?.toFixed(4)}</span>\r\n          </MetricRow>\r\n\r\n          <MetricRow>\r\n            <span className=\"label\">Risk Score:</span>\r\n            <span className=\"value\">{(latestSignal.risk_score * 100).toFixed(1)}%</span>\r\n          </MetricRow>\r\n\r\n          <MetricRow>\r\n            <span className=\"label\">Position Size:</span>\r\n            <span className=\"value\">{latestSignal.position_size?.toFixed(4)} XRP</span>\r\n          </MetricRow>\r\n\r\n          <MetricRow>\r\n            <span className=\"label\">Strategy:</span>\r\n            <span className=\"value\">{latestSignal.strategy}</span>\r\n          </MetricRow>\r\n\r\n          <MetricRow>\r\n            <span className=\"label\">Timestamp:</span>\r\n            <span className=\"value\">\r\n              {new Date(latestSignal.timestamp).toLocaleTimeString()}\r\n            </span>\r\n          </MetricRow>\r\n\r\n          {latestSignal.reasoning && (\r\n            <ReasoningText>\r\n              <strong>AI Reasoning:</strong> {latestSignal.reasoning}\r\n            </ReasoningText>\r\n          )}\r\n        </DecisionCard>\r\n      ) : (\r\n        <DecisionCard $signal=\"HOLD\">\r\n          <SignalType $signal=\"HOLD\">\r\n            Waiting for market data...\r\n          </SignalType>\r\n          <ReasoningText>\r\n            The AI is analyzing market conditions and will generate signals once sufficient data is available.\r\n          </ReasoningText>\r\n        </DecisionCard>\r\n      )}\r\n\r\n      {signals && signals.length > 1 && (\r\n        <div>\r\n          <h4 style={{ color: '#888', fontSize: '14px', marginBottom: '12px' }}>\r\n            Recent Signals\r\n          </h4>\r\n          {signals.slice(1, 4).map((signal, index) => (\r\n            <div key={index} style={{\r\n              display: 'flex',\r\n              justifyContent: 'space-between',\r\n              padding: '8px 0',\r\n              borderBottom: '1px solid #333',\r\n              fontSize: '12px'\r\n            }}>\r\n              <span style={{\r\n                color: signal.signal === 'BUY' ? '#4bffb5' :\r\n                       signal.signal === 'SELL' ? '#ff4976' : '#ffa726'\r\n              }}>\r\n                {signal.signal}\r\n              </span>\r\n              <span style={{ color: '#888' }}>\r\n                {(signal.confidence * 100).toFixed(0)}%\r\n              </span>\r\n              <span style={{ color: '#888' }}>\r\n                {new Date(signal.timestamp).toLocaleTimeString()}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </PanelContainer>\r\n  );\r\n};\r\n\r\nexport default DecisionPanel;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,WAAW,GAAGN,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAVID,WAAW;AAYjB,MAAME,eAAe,GAAGR,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA,gBAAgBK,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AAC3D,eAAeD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,mBAAmB,GAAG,MAAM;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,eAAe;AAcrB,MAAMI,YAAY,GAAGZ,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA,2BAA2BK,KAAK,IAC5BA,KAAK,CAACI,OAAO,KAAK,KAAK,GAAG,SAAS,GACnCJ,KAAK,CAACI,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;AACpD,CACC;AAACC,GAAA,GATIF,YAAY;AAWlB,MAAMG,UAAU,GAAGf,MAAM,CAACI,GAAG;AAC7B;AACA;AACA,WAAWK,KAAK,IACZA,KAAK,CAACI,OAAO,KAAK,KAAK,GAAG,SAAS,GACnCJ,KAAK,CAACI,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;AACpD;AACA,CACC;AAACG,GAAA,GARID,UAAU;AAUhB,MAAME,aAAa,GAAGjB,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACS,WAAW,GAAG,GAAG;AAC7C;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,aAAa;AAgBnB,MAAMG,SAAS,GAAGpB,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAdID,SAAS;AAgBf,MAAME,aAAa,GAAGtB,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GARID,aAAa;AAUnB,MAAME,mBAAmB,GAAGxB,MAAM,CAACI,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBK,KAAK,IAAIA,KAAK,CAACgB,OAAO,GAAG,SAAS,GAAG,SAAS;AAChE;AACA;AACA;AACA,aAAahB,KAAK,IAAIA,KAAK,CAACgB,OAAO,GAAG,SAAS,GAAG,SAAS;AAC3D;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,mBAAmB;AAqBzB,MAAMG,aAAa,GAAG3B,MAAM,CAAC4B,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAlBIF,aAAa;AAoBnB,MAAMG,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,UAAU,GAAG;AAAO,CAAC,KAAK;EAAA,IAAAC,mBAAA,EAAAC,qBAAA;EACpE,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC,IAAIA,OAAO,CAACQ,MAAM,GAAG,CAAC,GAAGR,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EACrF,MAAMS,MAAM,GAAGP,UAAU,KAAK,MAAM;EAEpC,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IAC1BC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,oBACE1C,OAAA,CAACC,cAAc;IAAA0C,QAAA,gBACb3C,OAAA,CAACI,WAAW;MAAAuC,QAAA,gBACV3C,OAAA;QAAA2C,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B/C,OAAA;QAAKgD,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAR,QAAA,gBAChE3C,OAAA,CAACyB,aAAa;UAAC2B,OAAO,EAAEb,aAAc;UAACc,KAAK,EAAC,cAAc;UAAAV,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChB/C,OAAA,CAACM,eAAe;UAACE,OAAO,EAAEsB;QAAS;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd/C,OAAA,CAACsB,mBAAmB;MAACC,OAAO,EAAEe,MAAO;MAAAK,QAAA,gBACnC3C,OAAA;QAAKsD,SAAS,EAAC;MAAW;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7B/C,OAAA;QAAA2C,QAAA,GAAM,eAAa,eAAA3C,OAAA;UAAMsD,SAAS,EAAC,QAAQ;UAAAX,QAAA,EAAEL,MAAM,GAAG,eAAe,GAAG;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,EAErBb,YAAY,gBACXlC,OAAA,CAACU,YAAY;MAACC,OAAO,EAAEuB,YAAY,CAACqB,MAAO;MAAAZ,QAAA,gBACzC3C,OAAA,CAACa,UAAU;QAACF,OAAO,EAAEuB,YAAY,CAACqB,MAAO;QAAAZ,QAAA,EACtCT,YAAY,CAACqB;MAAM;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEb/C,OAAA,CAACkB,SAAS;QAAAyB,QAAA,gBACR3C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1C/C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,GAAE,CAACT,YAAY,CAACsB,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAEZ/C,OAAA,CAACe,aAAa;QAACC,WAAW,EAAEkB,YAAY,CAACsB,UAAW;QAAAb,QAAA,eAClD3C,OAAA;UAAKsD,SAAS,EAAC;QAAM;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEhB/C,OAAA,CAACkB,SAAS;QAAAyB,QAAA,gBACR3C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrC/C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,GAAC,GAAC,GAAAX,mBAAA,GAACE,YAAY,CAACwB,KAAK,cAAA1B,mBAAA,uBAAlBA,mBAAA,CAAoByB,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAEZ/C,OAAA,CAACkB,SAAS;QAAAyB,QAAA,gBACR3C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1C/C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,GAAE,CAACT,YAAY,CAACyB,UAAU,GAAG,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAEZ/C,OAAA,CAACkB,SAAS;QAAAyB,QAAA,gBACR3C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7C/C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,IAAAV,qBAAA,GAAEC,YAAY,CAAC0B,aAAa,cAAA3B,qBAAA,uBAA1BA,qBAAA,CAA4BwB,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAEZ/C,OAAA,CAACkB,SAAS;QAAAyB,QAAA,gBACR3C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxC/C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EAAET,YAAY,CAAC2B;QAAQ;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAEZ/C,OAAA,CAACkB,SAAS;QAAAyB,QAAA,gBACR3C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzC/C,OAAA;UAAMsD,SAAS,EAAC,OAAO;UAAAX,QAAA,EACpB,IAAImB,IAAI,CAAC5B,YAAY,CAAC6B,SAAS,CAAC,CAACC,kBAAkB,CAAC;QAAC;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAEXb,YAAY,CAAC+B,SAAS,iBACrBjE,OAAA,CAACoB,aAAa;QAAAuB,QAAA,gBACZ3C,OAAA;UAAA2C,QAAA,EAAQ;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACb,YAAY,CAAC+B,SAAS;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAChB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,gBAEf/C,OAAA,CAACU,YAAY;MAACC,OAAO,EAAC,MAAM;MAAAgC,QAAA,gBAC1B3C,OAAA,CAACa,UAAU;QAACF,OAAO,EAAC,MAAM;QAAAgC,QAAA,EAAC;MAE3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/C,OAAA,CAACoB,aAAa;QAAAuB,QAAA,EAAC;MAEf;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACf,EAEAlB,OAAO,IAAIA,OAAO,CAACQ,MAAM,GAAG,CAAC,iBAC5BrC,OAAA;MAAA2C,QAAA,gBACE3C,OAAA;QAAIgD,KAAK,EAAE;UAAEkB,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAzB,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACJlB,OAAO,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACf,MAAM,EAAEgB,KAAK,kBACrCvE,OAAA;QAAiBgD,KAAK,EAAE;UACtBC,OAAO,EAAE,MAAM;UACfuB,cAAc,EAAE,eAAe;UAC/BC,OAAO,EAAE,OAAO;UAChBC,YAAY,EAAE,gBAAgB;UAC9BP,QAAQ,EAAE;QACZ,CAAE;QAAAxB,QAAA,gBACA3C,OAAA;UAAMgD,KAAK,EAAE;YACXkB,KAAK,EAAEX,MAAM,CAACA,MAAM,KAAK,KAAK,GAAG,SAAS,GACnCA,MAAM,CAACA,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG;UAChD,CAAE;UAAAZ,QAAA,EACCY,MAAM,CAACA;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACP/C,OAAA;UAAMgD,KAAK,EAAE;YAAEkB,KAAK,EAAE;UAAO,CAAE;UAAAvB,QAAA,GAC5B,CAACY,MAAM,CAACC,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP/C,OAAA;UAAMgD,KAAK,EAAE;YAAEkB,KAAK,EAAE;UAAO,CAAE;UAAAvB,QAAA,EAC5B,IAAImB,IAAI,CAACP,MAAM,CAACQ,SAAS,CAAC,CAACC,kBAAkB,CAAC;QAAC;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA,GAlBCwB,KAAK;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBV,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAAC4B,GAAA,GAnHI/C,aAAa;AAqHnB,eAAeA,aAAa;AAAC,IAAAzB,EAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAgD,GAAA;AAAAC,YAAA,CAAAzE,EAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA9D,GAAA;AAAA8D,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}