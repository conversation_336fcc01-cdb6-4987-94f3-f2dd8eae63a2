{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\LoginPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\n\n// Animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\nconst slideIn = keyframes`\n  from { transform: translateX(-100%); }\n  to { transform: translateX(0); }\n`;\nconst pulse = keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n`;\n\n// Styled Components\nconst LoginContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n    opacity: 0.3;\n  }\n`;\n_c = LoginContainer;\nconst LoginCard = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 3rem;\n  margin: auto;\n  width: 100%;\n  max-width: 450px;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);\n  animation: ${fadeIn} 0.8s ease-out;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    margin: 2rem;\n    padding: 2rem;\n  }\n`;\n_c2 = LoginCard;\nconst Logo = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n`;\n_c3 = Logo;\nconst LogoIcon = styled.div`\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 20px;\n  margin: 0 auto 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2rem;\n  color: white;\n  font-weight: bold;\n  animation: ${pulse} 2s infinite;\n`;\n_c4 = LogoIcon;\nconst Title = styled.h1`\n  color: #2d3748;\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0;\n  text-align: center;\n`;\n_c5 = Title;\nconst Subtitle = styled.p`\n  color: #718096;\n  font-size: 1rem;\n  margin: 0.5rem 0 0 0;\n  text-align: center;\n`;\n_c6 = Subtitle;\nconst Form = styled.form`\n  margin-top: 2rem;\n`;\n_c7 = Form;\nconst FormGroup = styled.div`\n  margin-bottom: 1.5rem;\n`;\n_c8 = FormGroup;\nconst Label = styled.label`\n  display: block;\n  color: #4a5568;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n`;\n_c9 = Label;\nconst Input = styled.input`\n  width: 100%;\n  padding: 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n\n  &::placeholder {\n    color: #a0aec0;\n  }\n`;\n_c0 = Input;\nconst Button = styled.button`\n  width: 100%;\n  padding: 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c1 = Button;\nconst ToggleButton = styled.button`\n  background: none;\n  border: none;\n  color: #667eea;\n  font-weight: 600;\n  cursor: pointer;\n  text-decoration: underline;\n  font-size: 0.9rem;\n  margin-top: 1rem;\n  width: 100%;\n\n  &:hover {\n    color: #764ba2;\n  }\n`;\n_c10 = ToggleButton;\nconst ErrorMessage = styled.div`\n  background: #fed7d7;\n  color: #c53030;\n  padding: 0.75rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  font-size: 0.9rem;\n  text-align: center;\n  animation: ${slideIn} 0.3s ease-out;\n`;\n_c11 = ErrorMessage;\nconst SuccessMessage = styled.div`\n  background: #c6f6d5;\n  color: #2f855a;\n  padding: 0.75rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  font-size: 0.9rem;\n  text-align: center;\n  animation: ${slideIn} 0.3s ease-out;\n`;\n_c12 = SuccessMessage;\nconst LoadingSpinner = styled.div`\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n_c13 = LoadingSpinner;\nconst LoginPage = ({\n  onLogin\n}) => {\n  _s();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    email: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';\n      const payload = isLogin ? {\n        username: formData.username,\n        password: formData.password\n      } : {\n        username: formData.username,\n        password: formData.password,\n        email: formData.email\n      };\n      const response = await fetch(`http://localhost:8000${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(payload)\n      });\n      const data = await response.json();\n      if (data.success) {\n        if (isLogin) {\n          setSuccess('Login successful! Welcome to AutoTradz AI');\n          setTimeout(() => {\n            onLogin(data.user);\n          }, 1000);\n        } else {\n          setSuccess('Registration successful! You can now log in.');\n          setTimeout(() => {\n            setIsLogin(true);\n            setFormData({\n              username: '',\n              password: '',\n              email: ''\n            });\n          }, 1500);\n        }\n      } else {\n        setError(data.message || 'An error occurred');\n      }\n    } catch (err) {\n      setError('Connection failed. Please check if the server is running.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setFormData({\n      username: '',\n      password: '',\n      email: ''\n    });\n    setError('');\n    setSuccess('');\n  };\n  return /*#__PURE__*/_jsxDEV(LoginContainer, {\n    children: /*#__PURE__*/_jsxDEV(LoginCard, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n          children: \"AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          children: \"AutoTradz AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n          children: \"Institutional-Grade Trading Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 19\n      }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            name: \"username\",\n            value: formData.username,\n            onChange: handleInputChange,\n            placeholder: \"Enter your username\",\n            required: true,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Email (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            placeholder: \"Enter your email\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            placeholder: \"Enter your password\",\n            required: true,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 24\n          }, this) : isLogin ? 'Sign In' : 'Create Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n          type: \"button\",\n          onClick: toggleMode,\n          disabled: loading,\n          children: isLogin ? \"Don't have an account? Create one\" : \"Already have an account? Sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"u/CIwzvvaxZdBRyskJXOhLXHF7U=\");\n_c14 = LoginPage;\nexport default LoginPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"LoginContainer\");\n$RefreshReg$(_c2, \"LoginCard\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"LogoIcon\");\n$RefreshReg$(_c5, \"Title\");\n$RefreshReg$(_c6, \"Subtitle\");\n$RefreshReg$(_c7, \"Form\");\n$RefreshReg$(_c8, \"FormGroup\");\n$RefreshReg$(_c9, \"Label\");\n$RefreshReg$(_c0, \"Input\");\n$RefreshReg$(_c1, \"Button\");\n$RefreshReg$(_c10, \"ToggleButton\");\n$RefreshReg$(_c11, \"ErrorMessage\");\n$RefreshReg$(_c12, \"SuccessMessage\");\n$RefreshReg$(_c13, \"LoadingSpinner\");\n$RefreshReg$(_c14, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "keyframes", "jsxDEV", "_jsxDEV", "fadeIn", "slideIn", "pulse", "LoginContainer", "div", "_c", "LoginCard", "_c2", "Logo", "_c3", "LogoIcon", "_c4", "Title", "h1", "_c5", "Subtitle", "p", "_c6", "Form", "form", "_c7", "FormGroup", "_c8", "Label", "label", "_c9", "Input", "input", "_c0", "<PERSON><PERSON>", "button", "_c1", "ToggleButton", "_c10", "ErrorMessage", "_c11", "SuccessMessage", "_c12", "LoadingSpinner", "_c13", "LoginPage", "onLogin", "_s", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "username", "password", "email", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "endpoint", "payload", "response", "fetch", "method", "headers", "credentials", "body", "JSON", "stringify", "data", "json", "setTimeout", "user", "message", "err", "toggleMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "placeholder", "required", "disabled", "onClick", "_c14", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/LoginPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\n\n// Animations\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\n\nconst slideIn = keyframes`\n  from { transform: translateX(-100%); }\n  to { transform: translateX(0); }\n`;\n\nconst pulse = keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n`;\n\n// Styled Components\nconst LoginContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n    opacity: 0.3;\n  }\n`;\n\nconst LoginCard = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 3rem;\n  margin: auto;\n  width: 100%;\n  max-width: 450px;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);\n  animation: ${fadeIn} 0.8s ease-out;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    margin: 2rem;\n    padding: 2rem;\n  }\n`;\n\nconst Logo = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n`;\n\nconst LogoIcon = styled.div`\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 20px;\n  margin: 0 auto 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2rem;\n  color: white;\n  font-weight: bold;\n  animation: ${pulse} 2s infinite;\n`;\n\nconst Title = styled.h1`\n  color: #2d3748;\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0;\n  text-align: center;\n`;\n\nconst Subtitle = styled.p`\n  color: #718096;\n  font-size: 1rem;\n  margin: 0.5rem 0 0 0;\n  text-align: center;\n`;\n\nconst Form = styled.form`\n  margin-top: 2rem;\n`;\n\nconst FormGroup = styled.div`\n  margin-bottom: 1.5rem;\n`;\n\nconst Label = styled.label`\n  display: block;\n  color: #4a5568;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n\n  &::placeholder {\n    color: #a0aec0;\n  }\n`;\n\nconst Button = styled.button`\n  width: 100%;\n  padding: 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst ToggleButton = styled.button`\n  background: none;\n  border: none;\n  color: #667eea;\n  font-weight: 600;\n  cursor: pointer;\n  text-decoration: underline;\n  font-size: 0.9rem;\n  margin-top: 1rem;\n  width: 100%;\n\n  &:hover {\n    color: #764ba2;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #fed7d7;\n  color: #c53030;\n  padding: 0.75rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  font-size: 0.9rem;\n  text-align: center;\n  animation: ${slideIn} 0.3s ease-out;\n`;\n\nconst SuccessMessage = styled.div`\n  background: #c6f6d5;\n  color: #2f855a;\n  padding: 0.75rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  font-size: 0.9rem;\n  text-align: center;\n  animation: ${slideIn} 0.3s ease-out;\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nconst LoginPage = ({ onLogin }) => {\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    email: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';\n      const payload = isLogin \n        ? { username: formData.username, password: formData.password }\n        : { username: formData.username, password: formData.password, email: formData.email };\n\n      const response = await fetch(`http://localhost:8000${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(payload),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        if (isLogin) {\n          setSuccess('Login successful! Welcome to AutoTradz AI');\n          setTimeout(() => {\n            onLogin(data.user);\n          }, 1000);\n        } else {\n          setSuccess('Registration successful! You can now log in.');\n          setTimeout(() => {\n            setIsLogin(true);\n            setFormData({ username: '', password: '', email: '' });\n          }, 1500);\n        }\n      } else {\n        setError(data.message || 'An error occurred');\n      }\n    } catch (err) {\n      setError('Connection failed. Please check if the server is running.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setFormData({ username: '', password: '', email: '' });\n    setError('');\n    setSuccess('');\n  };\n\n  return (\n    <LoginContainer>\n      <LoginCard>\n        <Logo>\n          <LogoIcon>AI</LogoIcon>\n          <Title>AutoTradz AI</Title>\n          <Subtitle>Institutional-Grade Trading Platform</Subtitle>\n        </Logo>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        {success && <SuccessMessage>{success}</SuccessMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <FormGroup>\n            <Label>Username</Label>\n            <Input\n              type=\"text\"\n              name=\"username\"\n              value={formData.username}\n              onChange={handleInputChange}\n              placeholder=\"Enter your username\"\n              required\n              disabled={loading}\n            />\n          </FormGroup>\n\n          {!isLogin && (\n            <FormGroup>\n              <Label>Email (Optional)</Label>\n              <Input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                placeholder=\"Enter your email\"\n                disabled={loading}\n              />\n            </FormGroup>\n          )}\n\n          <FormGroup>\n            <Label>Password</Label>\n            <Input\n              type=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              placeholder=\"Enter your password\"\n              required\n              disabled={loading}\n            />\n          </FormGroup>\n\n          <Button type=\"submit\" disabled={loading}>\n            {loading ? <LoadingSpinner /> : (isLogin ? 'Sign In' : 'Create Account')}\n          </Button>\n\n          <ToggleButton type=\"button\" onClick={toggleMode} disabled={loading}>\n            {isLogin \n              ? \"Don't have an account? Create one\" \n              : \"Already have an account? Sign in\"\n            }\n          </ToggleButton>\n        </Form>\n      </LoginCard>\n    </LoginContainer>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAGH,SAAS;AACxB;AACA;AACA,CAAC;AAED,MAAMI,OAAO,GAAGJ,SAAS;AACzB;AACA;AACA,CAAC;AAED,MAAMK,KAAK,GAAGL,SAAS;AACvB;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMM,cAAc,GAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAjBIF,cAAc;AAmBpB,MAAMG,SAAS,GAAGV,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeJ,MAAM;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAjBID,SAAS;AAmBf,MAAME,IAAI,GAAGZ,MAAM,CAACQ,GAAG;AACvB;AACA;AACA,CAAC;AAACK,GAAA,GAHID,IAAI;AAKV,MAAME,QAAQ,GAAGd,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,KAAK;AACpB,CAAC;AAACS,GAAA,GAbID,QAAQ;AAed,MAAME,KAAK,GAAGhB,MAAM,CAACiB,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,KAAK;AAQX,MAAMG,QAAQ,GAAGnB,MAAM,CAACoB,CAAC;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,QAAQ;AAOd,MAAMG,IAAI,GAAGtB,MAAM,CAACuB,IAAI;AACxB;AACA,CAAC;AAACC,GAAA,GAFIF,IAAI;AAIV,MAAMG,SAAS,GAAGzB,MAAM,CAACQ,GAAG;AAC5B;AACA,CAAC;AAACkB,GAAA,GAFID,SAAS;AAIf,MAAME,KAAK,GAAG3B,MAAM,CAAC4B,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,KAAK;AAQX,MAAMG,KAAK,GAAG9B,MAAM,CAAC+B,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAlBIF,KAAK;AAoBX,MAAMG,MAAM,GAAGjC,MAAM,CAACkC,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA5BIF,MAAM;AA8BZ,MAAMG,YAAY,GAAGpC,MAAM,CAACkC,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAdID,YAAY;AAgBlB,MAAME,YAAY,GAAGtC,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,OAAO;AACtB,CAAC;AAACkC,IAAA,GATID,YAAY;AAWlB,MAAME,cAAc,GAAGxC,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,OAAO;AACtB,CAAC;AAACoC,IAAA,GATID,cAAc;AAWpB,MAAME,cAAc,GAAG1C,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAbID,cAAc;AAepB,MAAME,SAAS,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC;IACvCqD,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0D,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4D,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM8D,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCd,WAAW,CAACe,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIP,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBZ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMS,QAAQ,GAAGrB,OAAO,GAAG,iBAAiB,GAAG,oBAAoB;MACnE,MAAMsB,OAAO,GAAGtB,OAAO,GACnB;QAAEI,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAAEC,QAAQ,EAAEH,QAAQ,CAACG;MAAS,CAAC,GAC5D;QAAED,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAAEC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAAEC,KAAK,EAAEJ,QAAQ,CAACI;MAAM,CAAC;MAEvF,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwBH,QAAQ,EAAE,EAAE;QAC/DI,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,WAAW,EAAE,SAAS;QACtBC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,OAAO;MAC9B,CAAC,CAAC;MAEF,MAAMS,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACpB,OAAO,EAAE;QAChB,IAAIX,OAAO,EAAE;UACXY,UAAU,CAAC,2CAA2C,CAAC;UACvDqB,UAAU,CAAC,MAAM;YACfnC,OAAO,CAACiC,IAAI,CAACG,IAAI,CAAC;UACpB,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLtB,UAAU,CAAC,8CAA8C,CAAC;UAC1DqB,UAAU,CAAC,MAAM;YACfhC,UAAU,CAAC,IAAI,CAAC;YAChBE,WAAW,CAAC;cAAEC,QAAQ,EAAE,EAAE;cAAEC,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAG,CAAC,CAAC;UACxD,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,MAAM;QACLI,QAAQ,CAACqB,IAAI,CAACI,OAAO,IAAI,mBAAmB,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ1B,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,UAAU,GAAGA,CAAA,KAAM;IACvBpC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBG,WAAW,CAAC;MAAEC,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;IACtDI,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,oBACExD,OAAA,CAACI,cAAc;IAAA8E,QAAA,eACblF,OAAA,CAACO,SAAS;MAAA2E,QAAA,gBACRlF,OAAA,CAACS,IAAI;QAAAyE,QAAA,gBACHlF,OAAA,CAACW,QAAQ;UAAAuE,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACvBtF,OAAA,CAACa,KAAK;UAAAqE,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3BtF,OAAA,CAACgB,QAAQ;UAAAkE,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,EAENjC,KAAK,iBAAIrD,OAAA,CAACmC,YAAY;QAAA+C,QAAA,EAAE7B;MAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,EAC7C/B,OAAO,iBAAIvD,OAAA,CAACqC,cAAc;QAAA6C,QAAA,EAAE3B;MAAO;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAEtDtF,OAAA,CAACmB,IAAI;QAACoE,QAAQ,EAAExB,YAAa;QAAAmB,QAAA,gBAC3BlF,OAAA,CAACsB,SAAS;UAAA4D,QAAA,gBACRlF,OAAA,CAACwB,KAAK;YAAA0D,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvBtF,OAAA,CAAC2B,KAAK;YACJ6D,IAAI,EAAC,MAAM;YACX7B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEd,QAAQ,CAACE,QAAS;YACzByC,QAAQ,EAAEhC,iBAAkB;YAC5BiC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;YACRC,QAAQ,EAAEzC;UAAQ;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEX,CAAC1C,OAAO,iBACP5C,OAAA,CAACsB,SAAS;UAAA4D,QAAA,gBACRlF,OAAA,CAACwB,KAAK;YAAA0D,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/BtF,OAAA,CAAC2B,KAAK;YACJ6D,IAAI,EAAC,OAAO;YACZ7B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEd,QAAQ,CAACI,KAAM;YACtBuC,QAAQ,EAAEhC,iBAAkB;YAC5BiC,WAAW,EAAC,kBAAkB;YAC9BE,QAAQ,EAAEzC;UAAQ;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ,eAEDtF,OAAA,CAACsB,SAAS;UAAA4D,QAAA,gBACRlF,OAAA,CAACwB,KAAK;YAAA0D,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvBtF,OAAA,CAAC2B,KAAK;YACJ6D,IAAI,EAAC,UAAU;YACf7B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEd,QAAQ,CAACG,QAAS;YACzBwC,QAAQ,EAAEhC,iBAAkB;YAC5BiC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;YACRC,QAAQ,EAAEzC;UAAQ;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZtF,OAAA,CAAC8B,MAAM;UAAC0D,IAAI,EAAC,QAAQ;UAACI,QAAQ,EAAEzC,OAAQ;UAAA+B,QAAA,EACrC/B,OAAO,gBAAGnD,OAAA,CAACuC,cAAc;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAI1C,OAAO,GAAG,SAAS,GAAG;QAAiB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eAETtF,OAAA,CAACiC,YAAY;UAACuD,IAAI,EAAC,QAAQ;UAACK,OAAO,EAAEZ,UAAW;UAACW,QAAQ,EAAEzC,OAAQ;UAAA+B,QAAA,EAChEtC,OAAO,GACJ,mCAAmC,GACnC;QAAkC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAAC3C,EAAA,CA7IIF,SAAS;AAAAqD,IAAA,GAATrD,SAAS;AA+If,eAAeA,SAAS;AAAC,IAAAnC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAsD,IAAA;AAAAC,YAAA,CAAAzF,EAAA;AAAAyF,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}