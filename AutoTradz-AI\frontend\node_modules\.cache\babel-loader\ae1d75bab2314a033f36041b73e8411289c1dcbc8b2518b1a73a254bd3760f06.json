{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\PerformancePanel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n_c = PanelContainer;\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n_c2 = PanelHeader;\nconst MetricGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 12px;\n  margin-bottom: 16px;\n`;\n_c3 = MetricGrid;\nconst MetricCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 12px;\n  text-align: center;\n`;\n_c4 = MetricCard;\nconst MetricLabel = styled.div`\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n`;\n_c5 = MetricLabel;\nconst MetricValue = styled.div`\n  color: ${props => props.$positive ? '#4bffb5' : props.$negative ? '#ff4976' : '#ffffff'};\n  font-size: 16px;\n  font-weight: 600;\n`;\n_c6 = MetricValue;\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: #333;\n  border-radius: 4px;\n  overflow: hidden;\n  margin: 8px 0;\n  \n  .fill {\n    height: 100%;\n    background: ${props => props.color || '#4bffb5'};\n    width: ${props => Math.min(Math.max(props.value, 0), 100)}%;\n    transition: width 0.3s ease;\n  }\n`;\n_c7 = ProgressBar;\nconst StatRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #333;\n  font-size: 14px;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  .label {\n    color: #888;\n  }\n  \n  .value {\n    color: #fff;\n    font-weight: 500;\n  }\n`;\n_c8 = StatRow;\nconst PerformancePanel = ({\n  performance,\n  portfolio\n}) => {\n  _s();\n  // 🔥 REAL-TIME PERFORMANCE: Fetch live performance data\n  const [livePerformance, setLivePerformance] = useState(null);\n  const [strategyPerformance, setStrategyPerformance] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  // Fetch real-time performance data\n  useEffect(() => {\n    const fetchPerformanceData = async () => {\n      try {\n        // Fetch basic performance metrics\n        const perfResponse = await fetch('http://localhost:8000/performance');\n        if (perfResponse.ok) {\n          const perfData = await perfResponse.json();\n          setLivePerformance(perfData);\n        }\n\n        // Fetch strategy-specific performance\n        const stratResponse = await fetch('http://localhost:8000/api/strategy-performance');\n        if (stratResponse.ok) {\n          const stratData = await stratResponse.json();\n          setStrategyPerformance(stratData);\n        }\n        setLastUpdate(new Date());\n      } catch (error) {\n        console.error('Error fetching performance data:', error);\n      }\n    };\n\n    // Initial fetch\n    fetchPerformanceData();\n\n    // Set up periodic updates every 5 seconds\n    const interval = setInterval(fetchPerformanceData, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Combine performance data from multiple sources\n  const combinedPerformance = {\n    ...performance,\n    ...livePerformance,\n    // Calculate additional metrics from portfolio data\n    ...(portfolio && {\n      total_return: portfolio.total_return || 0,\n      realized_pnl: portfolio.realized_pnl || 0,\n      unrealized_pnl: portfolio.unrealized_pnl || 0,\n      total_value: portfolio.total_value || 0,\n      cash: portfolio.cash || 0,\n      positions_count: portfolio.positions ? Object.keys(portfolio.positions).length : 0\n    })\n  };\n  if (!combinedPerformance || Object.keys(combinedPerformance).length === 0) {\n    return /*#__PURE__*/_jsxDEV(PanelContainer, {\n      children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n        children: \"Performance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#888',\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: lastUpdate ? 'Loading performance data...' : 'No performance data available'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 🔥 ENHANCED METRICS: Calculate comprehensive performance metrics\n  const winRate = combinedPerformance.win_rate || 0;\n  const sharpeRatio = combinedPerformance.sharpe_ratio || 0;\n  const maxDrawdown = combinedPerformance.max_drawdown || 0;\n  const profitFactor = combinedPerformance.profit_factor || 1;\n  const totalReturn = combinedPerformance.total_return || 0;\n  const realizedPnL = combinedPerformance.realized_pnl || 0;\n  const unrealizedPnL = combinedPerformance.unrealized_pnl || 0;\n  const totalPnL = realizedPnL + unrealizedPnL;\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [\"Performance\", lastUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#666',\n          fontFamily: 'monospace',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '6px',\n            height: '6px',\n            borderRadius: '50%',\n            backgroundColor: '#4bffb5',\n            animation: 'pulse 2s infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), lastUpdate.toLocaleTimeString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricGrid, {\n      children: [/*#__PURE__*/_jsxDEV(MetricCard, {\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Total P&L\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          $positive: totalPnL > 0,\n          $negative: totalPnL < 0,\n          children: [\"$\", totalPnL.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '10px',\n            color: '#666',\n            marginTop: '2px'\n          },\n          children: [\"R: $\", realizedPnL.toFixed(2), \" | U: $\", unrealizedPnL.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Total Return\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          $positive: totalReturn > 0,\n          $negative: totalReturn < 0,\n          children: [totalReturn.toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n          value: Math.abs(totalReturn),\n          color: totalReturn >= 0 ? '#4bffb5' : '#ff4976',\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fill\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Win Rate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          $positive: winRate > 0.5,\n          $negative: winRate < 0.3,\n          children: [(winRate * 100).toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n          value: winRate * 100,\n          color: \"#4bffb5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fill\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Active Positions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          children: combinedPerformance.positions_count || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '10px',\n            color: '#666',\n            marginTop: '2px'\n          },\n          children: [\"Cash: $\", (combinedPerformance.cash || 0).toFixed(0)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(StatRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Total Trades\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: combinedPerformance.total_trades || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Buy Signals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          style: {\n            color: '#4bffb5'\n          },\n          children: combinedPerformance.buy_signals || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Sell Signals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          style: {\n            color: '#ff4976'\n          },\n          children: combinedPerformance.sell_signals || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Avg Confidence\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [((combinedPerformance.avg_confidence || 0) * 100).toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Avg Risk Score\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [((combinedPerformance.avg_risk_score || 0) * 100).toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), strategyPerformance && strategyPerformance.performance && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(StatRow, {\n          style: {\n            borderTop: '1px solid #444',\n            paddingTop: '12px',\n            marginTop: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"label\",\n            style: {\n              fontWeight: 'bold',\n              color: '#4bffb5'\n            },\n            children: \"Strategy Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), Object.entries(strategyPerformance.performance).map(([strategy, perf]) => /*#__PURE__*/_jsxDEV(StatRow, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"label\",\n            children: strategy.replace('_', ' ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value\",\n            children: [perf.total_trades, \"T | \", (perf.win_rate * 100).toFixed(0), \"%WR\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 17\n          }, this)]\n        }, strategy, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true), combinedPerformance.total_volume && /*#__PURE__*/_jsxDEV(StatRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Total Volume\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [\"$\", combinedPerformance.total_volume.toFixed(0)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), combinedPerformance.total_fees && /*#__PURE__*/_jsxDEV(StatRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Total Fees\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [\"$\", combinedPerformance.total_fees.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(PerformancePanel, \"Bg9B1SA2Bk26X6C+m2J1MBqona8=\");\n_c9 = PerformancePanel;\nexport default PerformancePanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"PanelHeader\");\n$RefreshReg$(_c3, \"MetricGrid\");\n$RefreshReg$(_c4, \"MetricCard\");\n$RefreshReg$(_c5, \"MetricLabel\");\n$RefreshReg$(_c6, \"MetricValue\");\n$RefreshReg$(_c7, \"ProgressBar\");\n$RefreshReg$(_c8, \"StatRow\");\n$RefreshReg$(_c9, \"PerformancePanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PanelContainer", "div", "_c", "PanelHeader", "h3", "_c2", "MetricGrid", "_c3", "MetricCard", "_c4", "MetricLabel", "_c5", "MetricValue", "props", "$positive", "$negative", "_c6", "ProgressBar", "color", "Math", "min", "max", "value", "_c7", "StatRow", "_c8", "PerformancePanel", "performance", "portfolio", "_s", "livePerformance", "setLivePerformance", "strategyPerformance", "setStrategyPerformance", "lastUpdate", "setLastUpdate", "fetchPerformanceData", "perfResponse", "fetch", "ok", "perfData", "json", "stratResponse", "stratData", "Date", "error", "console", "interval", "setInterval", "clearInterval", "combinedPerformance", "total_return", "realized_pnl", "unrealized_pnl", "total_value", "cash", "positions_count", "positions", "Object", "keys", "length", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "padding", "winRate", "win_rate", "sharpeRatio", "sharpe_ratio", "maxDrawdown", "max_drawdown", "profitFactor", "profit_factor", "totalReturn", "realizedPnL", "unrealizedPnL", "totalPnL", "display", "justifyContent", "alignItems", "fontSize", "fontFamily", "gap", "width", "height", "borderRadius", "backgroundColor", "animation", "toLocaleTimeString", "toFixed", "marginTop", "abs", "className", "total_trades", "buy_signals", "sell_signals", "avg_confidence", "avg_risk_score", "borderTop", "paddingTop", "fontWeight", "entries", "map", "strategy", "perf", "replace", "total_volume", "total_fees", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/PerformancePanel.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\nconst PanelContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n`;\n\nconst PanelHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n`;\n\nconst MetricGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 12px;\n  margin-bottom: 16px;\n`;\n\nconst MetricCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 12px;\n  text-align: center;\n`;\n\nconst MetricLabel = styled.div`\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n`;\n\nconst MetricValue = styled.div`\n  color: ${props =>\n    props.$positive ? '#4bffb5' :\n    props.$negative ? '#ff4976' : '#ffffff'\n  };\n  font-size: 16px;\n  font-weight: 600;\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: #333;\n  border-radius: 4px;\n  overflow: hidden;\n  margin: 8px 0;\n  \n  .fill {\n    height: 100%;\n    background: ${props => props.color || '#4bffb5'};\n    width: ${props => Math.min(Math.max(props.value, 0), 100)}%;\n    transition: width 0.3s ease;\n  }\n`;\n\nconst StatRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #333;\n  font-size: 14px;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  .label {\n    color: #888;\n  }\n  \n  .value {\n    color: #fff;\n    font-weight: 500;\n  }\n`;\n\nconst PerformancePanel = ({ performance, portfolio }) => {\n  // 🔥 REAL-TIME PERFORMANCE: Fetch live performance data\n  const [livePerformance, setLivePerformance] = useState(null);\n  const [strategyPerformance, setStrategyPerformance] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  // Fetch real-time performance data\n  useEffect(() => {\n    const fetchPerformanceData = async () => {\n      try {\n        // Fetch basic performance metrics\n        const perfResponse = await fetch('http://localhost:8000/performance');\n        if (perfResponse.ok) {\n          const perfData = await perfResponse.json();\n          setLivePerformance(perfData);\n        }\n\n        // Fetch strategy-specific performance\n        const stratResponse = await fetch('http://localhost:8000/api/strategy-performance');\n        if (stratResponse.ok) {\n          const stratData = await stratResponse.json();\n          setStrategyPerformance(stratData);\n        }\n\n        setLastUpdate(new Date());\n      } catch (error) {\n        console.error('Error fetching performance data:', error);\n      }\n    };\n\n    // Initial fetch\n    fetchPerformanceData();\n\n    // Set up periodic updates every 5 seconds\n    const interval = setInterval(fetchPerformanceData, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Combine performance data from multiple sources\n  const combinedPerformance = {\n    ...performance,\n    ...livePerformance,\n    // Calculate additional metrics from portfolio data\n    ...(portfolio && {\n      total_return: portfolio.total_return || 0,\n      realized_pnl: portfolio.realized_pnl || 0,\n      unrealized_pnl: portfolio.unrealized_pnl || 0,\n      total_value: portfolio.total_value || 0,\n      cash: portfolio.cash || 0,\n      positions_count: portfolio.positions ? Object.keys(portfolio.positions).length : 0\n    })\n  };\n\n  if (!combinedPerformance || Object.keys(combinedPerformance).length === 0) {\n    return (\n      <PanelContainer>\n        <PanelHeader>Performance</PanelHeader>\n        <div style={{ color: '#888', textAlign: 'center', padding: '20px' }}>\n          {lastUpdate ? 'Loading performance data...' : 'No performance data available'}\n        </div>\n      </PanelContainer>\n    );\n  }\n\n  // 🔥 ENHANCED METRICS: Calculate comprehensive performance metrics\n  const winRate = combinedPerformance.win_rate || 0;\n  const sharpeRatio = combinedPerformance.sharpe_ratio || 0;\n  const maxDrawdown = combinedPerformance.max_drawdown || 0;\n  const profitFactor = combinedPerformance.profit_factor || 1;\n  const totalReturn = combinedPerformance.total_return || 0;\n  const realizedPnL = combinedPerformance.realized_pnl || 0;\n  const unrealizedPnL = combinedPerformance.unrealized_pnl || 0;\n  const totalPnL = realizedPnL + unrealizedPnL;\n\n  return (\n    <PanelContainer>\n      <PanelHeader style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        Performance\n        {lastUpdate && (\n          <div style={{\n            fontSize: '10px',\n            color: '#666',\n            fontFamily: 'monospace',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '4px'\n          }}>\n            <div style={{\n              width: '6px',\n              height: '6px',\n              borderRadius: '50%',\n              backgroundColor: '#4bffb5',\n              animation: 'pulse 2s infinite'\n            }} />\n            {lastUpdate.toLocaleTimeString()}\n          </div>\n        )}\n      </PanelHeader>\n\n      <MetricGrid>\n        <MetricCard>\n          <MetricLabel>Total P&L</MetricLabel>\n          <MetricValue $positive={totalPnL > 0} $negative={totalPnL < 0}>\n            ${totalPnL.toFixed(2)}\n          </MetricValue>\n          <div style={{ fontSize: '10px', color: '#666', marginTop: '2px' }}>\n            R: ${realizedPnL.toFixed(2)} | U: ${unrealizedPnL.toFixed(2)}\n          </div>\n        </MetricCard>\n\n        <MetricCard>\n          <MetricLabel>Total Return</MetricLabel>\n          <MetricValue $positive={totalReturn > 0} $negative={totalReturn < 0}>\n            {totalReturn.toFixed(2)}%\n          </MetricValue>\n          <ProgressBar value={Math.abs(totalReturn)} color={totalReturn >= 0 ? '#4bffb5' : '#ff4976'}>\n            <div className=\"fill\" />\n          </ProgressBar>\n        </MetricCard>\n\n        <MetricCard>\n          <MetricLabel>Win Rate</MetricLabel>\n          <MetricValue $positive={winRate > 0.5} $negative={winRate < 0.3}>\n            {(winRate * 100).toFixed(1)}%\n          </MetricValue>\n          <ProgressBar value={winRate * 100} color=\"#4bffb5\">\n            <div className=\"fill\" />\n          </ProgressBar>\n        </MetricCard>\n\n        <MetricCard>\n          <MetricLabel>Active Positions</MetricLabel>\n          <MetricValue>\n            {combinedPerformance.positions_count || 0}\n          </MetricValue>\n          <div style={{ fontSize: '10px', color: '#666', marginTop: '2px' }}>\n            Cash: ${(combinedPerformance.cash || 0).toFixed(0)}\n          </div>\n        </MetricCard>\n      </MetricGrid>\n      \n      <div>\n        <StatRow>\n          <span className=\"label\">Total Trades</span>\n          <span className=\"value\">{combinedPerformance.total_trades || 0}</span>\n        </StatRow>\n\n        <StatRow>\n          <span className=\"label\">Buy Signals</span>\n          <span className=\"value\" style={{ color: '#4bffb5' }}>\n            {combinedPerformance.buy_signals || 0}\n          </span>\n        </StatRow>\n\n        <StatRow>\n          <span className=\"label\">Sell Signals</span>\n          <span className=\"value\" style={{ color: '#ff4976' }}>\n            {combinedPerformance.sell_signals || 0}\n          </span>\n        </StatRow>\n\n        <StatRow>\n          <span className=\"label\">Avg Confidence</span>\n          <span className=\"value\">\n            {((combinedPerformance.avg_confidence || 0) * 100).toFixed(1)}%\n          </span>\n        </StatRow>\n\n        <StatRow>\n          <span className=\"label\">Avg Risk Score</span>\n          <span className=\"value\">\n            {((combinedPerformance.avg_risk_score || 0) * 100).toFixed(1)}%\n          </span>\n        </StatRow>\n\n        {/* 🔥 STRATEGY PERFORMANCE: Show individual strategy performance */}\n        {strategyPerformance && strategyPerformance.performance && (\n          <>\n            <StatRow style={{ borderTop: '1px solid #444', paddingTop: '12px', marginTop: '8px' }}>\n              <span className=\"label\" style={{ fontWeight: 'bold', color: '#4bffb5' }}>Strategy Performance</span>\n              <span className=\"value\"></span>\n            </StatRow>\n            {Object.entries(strategyPerformance.performance).map(([strategy, perf]) => (\n              <StatRow key={strategy}>\n                <span className=\"label\">{strategy.replace('_', ' ')}</span>\n                <span className=\"value\">\n                  {perf.total_trades}T | {(perf.win_rate * 100).toFixed(0)}%WR\n                </span>\n              </StatRow>\n            ))}\n          </>\n        )}\n\n        {combinedPerformance.total_volume && (\n          <StatRow>\n            <span className=\"label\">Total Volume</span>\n            <span className=\"value\">${combinedPerformance.total_volume.toFixed(0)}</span>\n          </StatRow>\n        )}\n\n        {combinedPerformance.total_fees && (\n          <StatRow>\n            <span className=\"label\">Total Fees</span>\n            <span className=\"value\">${combinedPerformance.total_fees.toFixed(2)}</span>\n          </StatRow>\n        )}\n      </div>\n    </PanelContainer>\n  );\n};\n\nexport default PerformancePanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,cAAc,GAAGL,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,WAAW,GAAGR,MAAM,CAACS,EAAE;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,UAAU,GAAGX,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GALID,UAAU;AAOhB,MAAME,UAAU,GAAGb,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GALID,UAAU;AAOhB,MAAME,WAAW,GAAGf,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAJID,WAAW;AAMjB,MAAME,WAAW,GAAGjB,MAAM,CAACM,GAAG;AAC9B,WAAWY,KAAK,IACZA,KAAK,CAACC,SAAS,GAAG,SAAS,GAC3BD,KAAK,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;AAC3C;AACA;AACA,CACC;AAACC,GAAA,GAPIJ,WAAW;AASjB,MAAMK,WAAW,GAAGtB,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBY,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAI,SAAS;AACnD,aAAaL,KAAK,IAAIM,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACR,KAAK,CAACS,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAC7D;AACA;AACA,CAAC;AAACC,GAAA,GAdIN,WAAW;AAgBjB,MAAMO,OAAO,GAAG7B,MAAM,CAACM,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GApBID,OAAO;AAsBb,MAAME,gBAAgB,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACvD;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI;QACF;QACA,MAAMC,YAAY,GAAG,MAAMC,KAAK,CAAC,mCAAmC,CAAC;QACrE,IAAID,YAAY,CAACE,EAAE,EAAE;UACnB,MAAMC,QAAQ,GAAG,MAAMH,YAAY,CAACI,IAAI,CAAC,CAAC;UAC1CV,kBAAkB,CAACS,QAAQ,CAAC;QAC9B;;QAEA;QACA,MAAME,aAAa,GAAG,MAAMJ,KAAK,CAAC,gDAAgD,CAAC;QACnF,IAAII,aAAa,CAACH,EAAE,EAAE;UACpB,MAAMI,SAAS,GAAG,MAAMD,aAAa,CAACD,IAAI,CAAC,CAAC;UAC5CR,sBAAsB,CAACU,SAAS,CAAC;QACnC;QAEAR,aAAa,CAAC,IAAIS,IAAI,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;;IAED;IACAT,oBAAoB,CAAC,CAAC;;IAEtB;IACA,MAAMW,QAAQ,GAAGC,WAAW,CAACZ,oBAAoB,EAAE,IAAI,CAAC;IACxD,OAAO,MAAMa,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,mBAAmB,GAAG;IAC1B,GAAGvB,WAAW;IACd,GAAGG,eAAe;IAClB;IACA,IAAIF,SAAS,IAAI;MACfuB,YAAY,EAAEvB,SAAS,CAACuB,YAAY,IAAI,CAAC;MACzCC,YAAY,EAAExB,SAAS,CAACwB,YAAY,IAAI,CAAC;MACzCC,cAAc,EAAEzB,SAAS,CAACyB,cAAc,IAAI,CAAC;MAC7CC,WAAW,EAAE1B,SAAS,CAAC0B,WAAW,IAAI,CAAC;MACvCC,IAAI,EAAE3B,SAAS,CAAC2B,IAAI,IAAI,CAAC;MACzBC,eAAe,EAAE5B,SAAS,CAAC6B,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC/B,SAAS,CAAC6B,SAAS,CAAC,CAACG,MAAM,GAAG;IACnF,CAAC;EACH,CAAC;EAED,IAAI,CAACV,mBAAmB,IAAIQ,MAAM,CAACC,IAAI,CAACT,mBAAmB,CAAC,CAACU,MAAM,KAAK,CAAC,EAAE;IACzE,oBACE/D,OAAA,CAACG,cAAc;MAAA6D,QAAA,gBACbhE,OAAA,CAACM,WAAW;QAAA0D,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtCpE,OAAA;QAAKqE,KAAK,EAAE;UAAEhD,KAAK,EAAE,MAAM;UAAEiD,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAP,QAAA,EACjE3B,UAAU,GAAG,6BAA6B,GAAG;MAA+B;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAErB;;EAEA;EACA,MAAMI,OAAO,GAAGnB,mBAAmB,CAACoB,QAAQ,IAAI,CAAC;EACjD,MAAMC,WAAW,GAAGrB,mBAAmB,CAACsB,YAAY,IAAI,CAAC;EACzD,MAAMC,WAAW,GAAGvB,mBAAmB,CAACwB,YAAY,IAAI,CAAC;EACzD,MAAMC,YAAY,GAAGzB,mBAAmB,CAAC0B,aAAa,IAAI,CAAC;EAC3D,MAAMC,WAAW,GAAG3B,mBAAmB,CAACC,YAAY,IAAI,CAAC;EACzD,MAAM2B,WAAW,GAAG5B,mBAAmB,CAACE,YAAY,IAAI,CAAC;EACzD,MAAM2B,aAAa,GAAG7B,mBAAmB,CAACG,cAAc,IAAI,CAAC;EAC7D,MAAM2B,QAAQ,GAAGF,WAAW,GAAGC,aAAa;EAE5C,oBACElF,OAAA,CAACG,cAAc;IAAA6D,QAAA,gBACbhE,OAAA,CAACM,WAAW;MAAC+D,KAAK,EAAE;QAAEe,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAtB,QAAA,GAAC,aAE9F,EAAC3B,UAAU,iBACTrC,OAAA;QAAKqE,KAAK,EAAE;UACVkB,QAAQ,EAAE,MAAM;UAChBlE,KAAK,EAAE,MAAM;UACbmE,UAAU,EAAE,WAAW;UACvBJ,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBG,GAAG,EAAE;QACP,CAAE;QAAAzB,QAAA,gBACAhE,OAAA;UAAKqE,KAAK,EAAE;YACVqB,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,KAAK;YACbC,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE,SAAS;YAC1BC,SAAS,EAAE;UACb;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACJ/B,UAAU,CAAC0D,kBAAkB,CAAC,CAAC;MAAA;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAEdpE,OAAA,CAACS,UAAU;MAAAuD,QAAA,gBACThE,OAAA,CAACW,UAAU;QAAAqD,QAAA,gBACThE,OAAA,CAACa,WAAW;UAAAmD,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACpCpE,OAAA,CAACe,WAAW;UAACE,SAAS,EAAEkE,QAAQ,GAAG,CAAE;UAACjE,SAAS,EAAEiE,QAAQ,GAAG,CAAE;UAAAnB,QAAA,GAAC,GAC5D,EAACmB,QAAQ,CAACa,OAAO,CAAC,CAAC,CAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACdpE,OAAA;UAAKqE,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAElE,KAAK,EAAE,MAAM;YAAE4E,SAAS,EAAE;UAAM,CAAE;UAAAjC,QAAA,GAAC,MAC7D,EAACiB,WAAW,CAACe,OAAO,CAAC,CAAC,CAAC,EAAC,SAAO,EAACd,aAAa,CAACc,OAAO,CAAC,CAAC,CAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbpE,OAAA,CAACW,UAAU;QAAAqD,QAAA,gBACThE,OAAA,CAACa,WAAW;UAAAmD,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvCpE,OAAA,CAACe,WAAW;UAACE,SAAS,EAAE+D,WAAW,GAAG,CAAE;UAAC9D,SAAS,EAAE8D,WAAW,GAAG,CAAE;UAAAhB,QAAA,GACjEgB,WAAW,CAACgB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1B;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdpE,OAAA,CAACoB,WAAW;UAACK,KAAK,EAAEH,IAAI,CAAC4E,GAAG,CAAClB,WAAW,CAAE;UAAC3D,KAAK,EAAE2D,WAAW,IAAI,CAAC,GAAG,SAAS,GAAG,SAAU;UAAAhB,QAAA,eACzFhE,OAAA;YAAKmG,SAAS,EAAC;UAAM;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEbpE,OAAA,CAACW,UAAU;QAAAqD,QAAA,gBACThE,OAAA,CAACa,WAAW;UAAAmD,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACnCpE,OAAA,CAACe,WAAW;UAACE,SAAS,EAAEuD,OAAO,GAAG,GAAI;UAACtD,SAAS,EAAEsD,OAAO,GAAG,GAAI;UAAAR,QAAA,GAC7D,CAACQ,OAAO,GAAG,GAAG,EAAEwB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9B;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdpE,OAAA,CAACoB,WAAW;UAACK,KAAK,EAAE+C,OAAO,GAAG,GAAI;UAACnD,KAAK,EAAC,SAAS;UAAA2C,QAAA,eAChDhE,OAAA;YAAKmG,SAAS,EAAC;UAAM;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEbpE,OAAA,CAACW,UAAU;QAAAqD,QAAA,gBACThE,OAAA,CAACa,WAAW;UAAAmD,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC3CpE,OAAA,CAACe,WAAW;UAAAiD,QAAA,EACTX,mBAAmB,CAACM,eAAe,IAAI;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACdpE,OAAA;UAAKqE,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAElE,KAAK,EAAE,MAAM;YAAE4E,SAAS,EAAE;UAAM,CAAE;UAAAjC,QAAA,GAAC,SAC1D,EAAC,CAACX,mBAAmB,CAACK,IAAI,IAAI,CAAC,EAAEsC,OAAO,CAAC,CAAC,CAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEbpE,OAAA;MAAAgE,QAAA,gBACEhE,OAAA,CAAC2B,OAAO;QAAAqC,QAAA,gBACNhE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3CpE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAEX,mBAAmB,CAAC+C,YAAY,IAAI;QAAC;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAEVpE,OAAA,CAAC2B,OAAO;QAAAqC,QAAA,gBACNhE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CpE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAC9B,KAAK,EAAE;YAAEhD,KAAK,EAAE;UAAU,CAAE;UAAA2C,QAAA,EACjDX,mBAAmB,CAACgD,WAAW,IAAI;QAAC;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEVpE,OAAA,CAAC2B,OAAO;QAAAqC,QAAA,gBACNhE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3CpE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAC9B,KAAK,EAAE;YAAEhD,KAAK,EAAE;UAAU,CAAE;UAAA2C,QAAA,EACjDX,mBAAmB,CAACiD,YAAY,IAAI;QAAC;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEVpE,OAAA,CAAC2B,OAAO;QAAAqC,QAAA,gBACNhE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7CpE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,GACpB,CAAC,CAACX,mBAAmB,CAACkD,cAAc,IAAI,CAAC,IAAI,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC,EAAC,GAChE;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEVpE,OAAA,CAAC2B,OAAO;QAAAqC,QAAA,gBACNhE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7CpE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,GACpB,CAAC,CAACX,mBAAmB,CAACmD,cAAc,IAAI,CAAC,IAAI,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC,EAAC,GAChE;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGTjC,mBAAmB,IAAIA,mBAAmB,CAACL,WAAW,iBACrD9B,OAAA,CAAAE,SAAA;QAAA8D,QAAA,gBACEhE,OAAA,CAAC2B,OAAO;UAAC0C,KAAK,EAAE;YAAEoC,SAAS,EAAE,gBAAgB;YAAEC,UAAU,EAAE,MAAM;YAAET,SAAS,EAAE;UAAM,CAAE;UAAAjC,QAAA,gBACpFhE,OAAA;YAAMmG,SAAS,EAAC,OAAO;YAAC9B,KAAK,EAAE;cAAEsC,UAAU,EAAE,MAAM;cAAEtF,KAAK,EAAE;YAAU,CAAE;YAAA2C,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpGpE,OAAA;YAAMmG,SAAS,EAAC;UAAO;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,EACTP,MAAM,CAAC+C,OAAO,CAACzE,mBAAmB,CAACL,WAAW,CAAC,CAAC+E,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEC,IAAI,CAAC,kBACpE/G,OAAA,CAAC2B,OAAO;UAAAqC,QAAA,gBACNhE,OAAA;YAAMmG,SAAS,EAAC,OAAO;YAAAnC,QAAA,EAAE8C,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG;UAAC;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DpE,OAAA;YAAMmG,SAAS,EAAC,OAAO;YAAAnC,QAAA,GACpB+C,IAAI,CAACX,YAAY,EAAC,MAAI,EAAC,CAACW,IAAI,CAACtC,QAAQ,GAAG,GAAG,EAAEuB,OAAO,CAAC,CAAC,CAAC,EAAC,KAC3D;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAJK0C,QAAQ;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKb,CACV,CAAC;MAAA,eACF,CACH,EAEAf,mBAAmB,CAAC4D,YAAY,iBAC/BjH,OAAA,CAAC2B,OAAO;QAAAqC,QAAA,gBACNhE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3CpE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,GAAC,GAAC,EAACX,mBAAmB,CAAC4D,YAAY,CAACjB,OAAO,CAAC,CAAC,CAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CACV,EAEAf,mBAAmB,CAAC6D,UAAU,iBAC7BlH,OAAA,CAAC2B,OAAO;QAAAqC,QAAA,gBACNhE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCpE,OAAA;UAAMmG,SAAS,EAAC,OAAO;UAAAnC,QAAA,GAAC,GAAC,EAACX,mBAAmB,CAAC6D,UAAU,CAAClB,OAAO,CAAC,CAAC,CAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAErB,CAAC;AAACpC,EAAA,CAjNIH,gBAAgB;AAAAsF,GAAA,GAAhBtF,gBAAgB;AAmNtB,eAAeA,gBAAgB;AAAC,IAAAxB,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAuF,GAAA;AAAAC,YAAA,CAAA/G,EAAA;AAAA+G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}