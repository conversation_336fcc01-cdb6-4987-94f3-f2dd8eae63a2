{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check authentication status on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/auth/me', {\n        credentials: 'include'\n      });\n      const data = await response.json();\n      if (data.authenticated && data.user) {\n        setUser(data.user);\n        setIsAuthenticated(true);\n      } else {\n        setUser(null);\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setUser(null);\n      setIsAuthenticated(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = userData => {\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n  const logout = async () => {\n    try {\n      await fetch('http://localhost:8000/api/auth/logout', {\n        method: 'POST',\n        credentials: 'include'\n      });\n    } catch (error) {\n      console.error('Logout failed:', error);\n    } finally {\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    checkAuthStatus\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "response", "fetch", "credentials", "data", "json", "authenticated", "error", "console", "login", "userData", "logout", "method", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check authentication status on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/auth/me', {\n        credentials: 'include',\n      });\n      \n      const data = await response.json();\n      \n      if (data.authenticated && data.user) {\n        setUser(data.user);\n        setIsAuthenticated(true);\n      } else {\n        setUser(null);\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setUser(null);\n      setIsAuthenticated(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = (userData) => {\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n\n  const logout = async () => {\n    try {\n      await fetch('http://localhost:8000/api/auth/logout', {\n        method: 'POST',\n        credentials: 'include',\n      });\n    } catch (error) {\n      console.error('Logout failed:', error);\n    } finally {\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    checkAuthStatus\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACdiB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;QAChEC,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,aAAa,IAAIF,IAAI,CAACV,IAAI,EAAE;QACnCC,OAAO,CAACS,IAAI,CAACV,IAAI,CAAC;QAClBK,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLJ,OAAO,CAAC,IAAI,CAAC;QACbI,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CZ,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,KAAK,GAAIC,QAAQ,IAAK;IAC1Bf,OAAO,CAACe,QAAQ,CAAC;IACjBX,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMY,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMT,KAAK,CAAC,uCAAuC,EAAE;QACnDU,MAAM,EAAE,MAAM;QACdT,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC,CAAC,SAAS;MACRZ,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMc,KAAK,GAAG;IACZnB,IAAI;IACJI,eAAe;IACfF,OAAO;IACPa,KAAK;IACLE,MAAM;IACNX;EACF,CAAC;EAED,oBACEf,OAAA,CAACC,WAAW,CAAC4B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArB,QAAA,EAChCA;EAAQ;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzB,GAAA,CAnEWF,YAAY;AAAA4B,EAAA,GAAZ5B,YAAY;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}