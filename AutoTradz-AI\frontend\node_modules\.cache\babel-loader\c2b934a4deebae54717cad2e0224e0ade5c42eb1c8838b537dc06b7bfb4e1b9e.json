{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\ActivePositionsDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n_c = DashboardContainer;\nconst DashboardHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\n_c2 = DashboardHeader;\nconst PositionsGrid = styled.div`\n  display: grid;\n  gap: 16px;\n`;\n_c3 = PositionsGrid;\nconst PositionCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid ${props => props.$pnl > 0 ? '#4bffb5' : props.$pnl < 0 ? '#ff4976' : '#666'};\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #333;\n    transform: translateY(-2px);\n  }\n`;\n_c4 = PositionCard;\nconst PositionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n`;\n_c5 = PositionHeader;\nconst SymbolInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c6 = SymbolInfo;\nconst Symbol = styled.div`\n  font-weight: 700;\n  font-size: 16px;\n  color: #fff;\n`;\n_c7 = Symbol;\nconst PositionType = styled.div`\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 600;\n  background: ${props => props.$type === 'LONG' ? '#4bffb5' : '#ff4976'};\n  color: ${props => props.$type === 'LONG' ? '#000' : '#fff'};\n`;\n_c8 = PositionType;\nconst PnLDisplay = styled.div`\n  text-align: right;\n`;\n_c9 = PnLDisplay;\nconst PnLValue = styled.div`\n  font-size: 18px;\n  font-weight: 700;\n  color: ${props => props.$value > 0 ? '#4bffb5' : props.$value < 0 ? '#ff4976' : '#666'};\n`;\n_c0 = PnLValue;\nconst PnLPercentage = styled.div`\n  font-size: 12px;\n  color: ${props => props.$value > 0 ? '#4bffb5' : props.$value < 0 ? '#ff4976' : '#666'};\n`;\n_c1 = PnLPercentage;\nconst PositionDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin-bottom: 16px;\n`;\n_c10 = PositionDetails;\nconst DetailItem = styled.div`\n  text-align: center;\n`;\n_c11 = DetailItem;\nconst DetailLabel = styled.div`\n  font-size: 10px;\n  color: #888;\n  margin-bottom: 2px;\n  text-transform: uppercase;\n  font-weight: 500;\n`;\n_c12 = DetailLabel;\nconst DetailValue = styled.div`\n  font-size: 14px;\n  font-weight: 600;\n  color: #fff;\n`;\n_c13 = DetailValue;\nconst PositionActions = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-top: 12px;\n`;\n_c14 = PositionActions;\nconst ActionButton = styled.button`\n  flex: 1;\n  padding: 8px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.close {\n    background: #ff4976;\n    color: #fff;\n    \n    &:hover:not(:disabled) {\n      background: #e63946;\n    }\n  }\n  \n  &.partial {\n    background: #ffa726;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #ff9800;\n    }\n  }\n  \n  &.info {\n    background: #2a2a2a;\n    color: #888;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n      color: #fff;\n    }\n  }\n`;\n_c15 = ActionButton;\nconst SummaryCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 20px;\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 16px;\n`;\n_c16 = SummaryCard;\nconst SummaryItem = styled.div`\n  text-align: center;\n`;\n_c17 = SummaryItem;\nconst SummaryLabel = styled.div`\n  font-size: 12px;\n  color: #888;\n  margin-bottom: 4px;\n`;\n_c18 = SummaryLabel;\nconst SummaryValue = styled.div`\n  font-size: 18px;\n  font-weight: 700;\n  color: ${props => props.$positive ? '#4bffb5' : props.$negative ? '#ff4976' : '#fff'};\n`;\n_c19 = SummaryValue;\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 40px 20px;\n  color: #888;\n`;\n_c20 = EmptyState;\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: 16px;\n`;\n_c21 = EmptyIcon;\nconst EmptyText = styled.div`\n  font-size: 16px;\n  margin-bottom: 8px;\n`;\n_c22 = EmptyText;\nconst EmptySubtext = styled.div`\n  font-size: 12px;\n  color: #666;\n`;\n_c23 = EmptySubtext;\nconst ActivePositionsDashboard = ({\n  portfolio\n}) => {\n  _s();\n  const [positions, setPositions] = useState([]);\n  const [summary, setSummary] = useState({\n    totalPositions: 0,\n    totalValue: 0,\n    totalPnL: 0,\n    totalPnLPercent: 0\n  });\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  // Process portfolio data\n  useEffect(() => {\n    if (portfolio && portfolio.positions) {\n      const positionArray = Object.entries(portfolio.positions).map(([symbol, position]) => {\n        const duration = position.entry_time ? Math.round((Date.now() - new Date(position.entry_time)) / (1000 * 60)) : 0;\n        const pnlPercent = position.entry_price ? (position.current_price - position.entry_price) / position.entry_price * 100 : 0;\n        return {\n          symbol,\n          ...position,\n          duration,\n          pnlPercent,\n          positionType: position.size > 0 ? 'LONG' : 'SHORT',\n          marketValue: position.size * position.current_price\n        };\n      });\n      setPositions(positionArray);\n\n      // Calculate summary\n      const totalValue = positionArray.reduce((sum, pos) => sum + pos.marketValue, 0);\n      const totalPnL = positionArray.reduce((sum, pos) => sum + (pos.unrealized_pnl || 0), 0);\n      const totalPnLPercent = portfolio.total_value ? totalPnL / portfolio.total_value * 100 : 0;\n      setSummary({\n        totalPositions: positionArray.length,\n        totalValue,\n        totalPnL,\n        totalPnLPercent\n      });\n      setLastUpdate(new Date());\n    }\n  }, [portfolio]);\n  const formatDuration = minutes => {\n    if (minutes < 60) return `${minutes}m`;\n    const hours = Math.floor(minutes / 60);\n    if (hours < 24) return `${hours}h ${minutes % 60}m`;\n    const days = Math.floor(hours / 24);\n    return `${days}d ${hours % 24}h`;\n  };\n  const handleClosePosition = async (symbol, isPartial = false) => {\n    try {\n      const position = positions.find(p => p.symbol === symbol);\n      const size = isPartial ? position.size / 2 : null;\n      const response = await fetch('http://localhost:8000/api/close-position', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          symbol,\n          size,\n          reason: 'manual_close_dashboard'\n        })\n      });\n      if (response.ok) {\n        var _result$pnl;\n        const result = await response.json();\n        alert(`Position ${symbol} ${isPartial ? 'partially ' : ''}closed! P&L: $${(_result$pnl = result.pnl) === null || _result$pnl === void 0 ? void 0 : _result$pnl.toFixed(2)}`);\n      } else {\n        alert('Failed to close position');\n      }\n    } catch (error) {\n      console.error('Error closing position:', error);\n      alert('Error closing position');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DashboardContainer, {\n    children: [/*#__PURE__*/_jsxDEV(DashboardHeader, {\n      children: [\"\\uD83D\\uDCCA Active Positions\", lastUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#666',\n          fontFamily: 'monospace'\n        },\n        children: [\"Updated: \", lastUpdate.toLocaleTimeString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SummaryCard, {\n      children: [/*#__PURE__*/_jsxDEV(SummaryItem, {\n        children: [/*#__PURE__*/_jsxDEV(SummaryLabel, {\n          children: \"Total Positions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryValue, {\n          children: summary.totalPositions\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SummaryItem, {\n        children: [/*#__PURE__*/_jsxDEV(SummaryLabel, {\n          children: \"Total Value\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryValue, {\n          children: [\"$\", summary.totalValue.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SummaryItem, {\n        children: [/*#__PURE__*/_jsxDEV(SummaryLabel, {\n          children: \"Total P&L\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryValue, {\n          $positive: summary.totalPnL > 0,\n          $negative: summary.totalPnL < 0,\n          children: [\"$\", summary.totalPnL.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SummaryItem, {\n        children: [/*#__PURE__*/_jsxDEV(SummaryLabel, {\n          children: \"P&L %\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryValue, {\n          $positive: summary.totalPnLPercent > 0,\n          $negative: summary.totalPnLPercent < 0,\n          children: [summary.totalPnLPercent >= 0 ? '+' : '', summary.totalPnLPercent.toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), positions.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n      children: [/*#__PURE__*/_jsxDEV(EmptyIcon, {\n        children: \"\\uD83D\\uDCC8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(EmptyText, {\n        children: \"No Active Positions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(EmptySubtext, {\n        children: \"Start trading to see your positions here\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(PositionsGrid, {\n      children: positions.map(position => {\n        var _position$unrealized_, _position$entry_price, _position$current_pri;\n        return /*#__PURE__*/_jsxDEV(PositionCard, {\n          $pnl: position.unrealized_pnl,\n          children: [/*#__PURE__*/_jsxDEV(PositionHeader, {\n            children: [/*#__PURE__*/_jsxDEV(SymbolInfo, {\n              children: [/*#__PURE__*/_jsxDEV(Symbol, {\n                children: position.symbol\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PositionType, {\n                $type: position.positionType,\n                children: position.positionType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(PnLDisplay, {\n              children: [/*#__PURE__*/_jsxDEV(PnLValue, {\n                $value: position.unrealized_pnl,\n                children: [\"$\", ((_position$unrealized_ = position.unrealized_pnl) === null || _position$unrealized_ === void 0 ? void 0 : _position$unrealized_.toFixed(2)) || '0.00']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PnLPercentage, {\n                $value: position.pnlPercent,\n                children: [position.pnlPercent >= 0 ? '+' : '', position.pnlPercent.toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PositionDetails, {\n            children: [/*#__PURE__*/_jsxDEV(DetailItem, {\n              children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n                children: \"Size\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n                children: Math.abs(position.size).toFixed(4)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DetailItem, {\n              children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n                children: \"Entry Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n                children: [\"$\", (_position$entry_price = position.entry_price) === null || _position$entry_price === void 0 ? void 0 : _position$entry_price.toFixed(4)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DetailItem, {\n              children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n                children: \"Current Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n                children: [\"$\", (_position$current_pri = position.current_price) === null || _position$current_pri === void 0 ? void 0 : _position$current_pri.toFixed(4)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DetailItem, {\n              children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n                children: \"Duration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n                children: formatDuration(position.duration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PositionActions, {\n            children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n              className: \"close\",\n              onClick: () => handleClosePosition(position.symbol, false),\n              children: \"\\uD83D\\uDDD9 Close All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n              className: \"partial\",\n              onClick: () => handleClosePosition(position.symbol, true),\n              children: \"\\uD83D\\uDCCA Close 50%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n              className: \"info\",\n              onClick: () => {\n                var _position$entry_price2, _position$current_pri2, _position$unrealized_2;\n                const info = `Position: ${position.symbol}\\nType: ${position.positionType}\\nSize: ${Math.abs(position.size).toFixed(4)}\\nEntry: $${(_position$entry_price2 = position.entry_price) === null || _position$entry_price2 === void 0 ? void 0 : _position$entry_price2.toFixed(4)}\\nCurrent: $${(_position$current_pri2 = position.current_price) === null || _position$current_pri2 === void 0 ? void 0 : _position$current_pri2.toFixed(4)}\\nP&L: $${(_position$unrealized_2 = position.unrealized_pnl) === null || _position$unrealized_2 === void 0 ? void 0 : _position$unrealized_2.toFixed(2)} (${position.pnlPercent.toFixed(2)}%)\\nDuration: ${formatDuration(position.duration)}\\nValue: $${position.marketValue.toFixed(2)}`;\n                alert(info);\n              },\n              children: \"\\u2139\\uFE0F Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this)]\n        }, position.symbol, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 302,\n    columnNumber: 5\n  }, this);\n};\n_s(ActivePositionsDashboard, \"1DHNnto3iv89exOrE6zZxu5hvog=\");\n_c24 = ActivePositionsDashboard;\nexport default ActivePositionsDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24;\n$RefreshReg$(_c, \"DashboardContainer\");\n$RefreshReg$(_c2, \"DashboardHeader\");\n$RefreshReg$(_c3, \"PositionsGrid\");\n$RefreshReg$(_c4, \"PositionCard\");\n$RefreshReg$(_c5, \"PositionHeader\");\n$RefreshReg$(_c6, \"SymbolInfo\");\n$RefreshReg$(_c7, \"Symbol\");\n$RefreshReg$(_c8, \"PositionType\");\n$RefreshReg$(_c9, \"PnLDisplay\");\n$RefreshReg$(_c0, \"PnLValue\");\n$RefreshReg$(_c1, \"PnLPercentage\");\n$RefreshReg$(_c10, \"PositionDetails\");\n$RefreshReg$(_c11, \"DetailItem\");\n$RefreshReg$(_c12, \"DetailLabel\");\n$RefreshReg$(_c13, \"DetailValue\");\n$RefreshReg$(_c14, \"PositionActions\");\n$RefreshReg$(_c15, \"ActionButton\");\n$RefreshReg$(_c16, \"SummaryCard\");\n$RefreshReg$(_c17, \"SummaryItem\");\n$RefreshReg$(_c18, \"SummaryLabel\");\n$RefreshReg$(_c19, \"SummaryValue\");\n$RefreshReg$(_c20, \"EmptyState\");\n$RefreshReg$(_c21, \"EmptyIcon\");\n$RefreshReg$(_c22, \"EmptyText\");\n$RefreshReg$(_c23, \"EmptySubtext\");\n$RefreshReg$(_c24, \"ActivePositionsDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "jsxDEV", "_jsxDEV", "DashboardContainer", "div", "_c", "DashboardHeader", "h3", "_c2", "PositionsGrid", "_c3", "PositionCard", "props", "$pnl", "_c4", "PositionHeader", "_c5", "SymbolInfo", "_c6", "Symbol", "_c7", "PositionType", "$type", "_c8", "PnLDisplay", "_c9", "PnLValue", "$value", "_c0", "PnLPercentage", "_c1", "PositionDetails", "_c10", "DetailItem", "_c11", "DetailLabel", "_c12", "DetailValue", "_c13", "PositionActions", "_c14", "ActionButton", "button", "_c15", "SummaryCard", "_c16", "SummaryItem", "_c17", "Summary<PERSON><PERSON><PERSON>", "_c18", "SummaryValue", "$positive", "$negative", "_c19", "EmptyState", "_c20", "EmptyIcon", "_c21", "EmptyText", "_c22", "EmptySubtext", "_c23", "ActivePositionsDashboard", "portfolio", "_s", "positions", "setPositions", "summary", "set<PERSON>ummary", "totalPositions", "totalValue", "totalPnL", "totalPnLPercent", "lastUpdate", "setLastUpdate", "positionArray", "Object", "entries", "map", "symbol", "position", "duration", "entry_time", "Math", "round", "Date", "now", "pnlPercent", "entry_price", "current_price", "positionType", "size", "marketValue", "reduce", "sum", "pos", "unrealized_pnl", "total_value", "length", "formatDuration", "minutes", "hours", "floor", "days", "handleClosePosition", "isPartial", "find", "p", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "reason", "ok", "_result$pnl", "result", "json", "alert", "pnl", "toFixed", "error", "console", "children", "style", "fontSize", "color", "fontFamily", "toLocaleTimeString", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_position$unrealized_", "_position$entry_price", "_position$current_pri", "abs", "className", "onClick", "_position$entry_price2", "_position$current_pri2", "_position$unrealized_2", "info", "_c24", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/ActivePositionsDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\nconst DashboardContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n\nconst DashboardHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\n\nconst PositionsGrid = styled.div`\n  display: grid;\n  gap: 16px;\n`;\n\nconst PositionCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid ${props =>\n    props.$pnl > 0 ? '#4bffb5' :\n    props.$pnl < 0 ? '#ff4976' : '#666'\n  };\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #333;\n    transform: translateY(-2px);\n  }\n`;\n\nconst PositionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n`;\n\nconst SymbolInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst Symbol = styled.div`\n  font-weight: 700;\n  font-size: 16px;\n  color: #fff;\n`;\n\nconst PositionType = styled.div`\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 600;\n  background: ${props => props.$type === 'LONG' ? '#4bffb5' : '#ff4976'};\n  color: ${props => props.$type === 'LONG' ? '#000' : '#fff'};\n`;\n\nconst PnLDisplay = styled.div`\n  text-align: right;\n`;\n\nconst PnLValue = styled.div`\n  font-size: 18px;\n  font-weight: 700;\n  color: ${props =>\n    props.$value > 0 ? '#4bffb5' :\n    props.$value < 0 ? '#ff4976' : '#666'\n  };\n`;\n\nconst PnLPercentage = styled.div`\n  font-size: 12px;\n  color: ${props =>\n    props.$value > 0 ? '#4bffb5' :\n    props.$value < 0 ? '#ff4976' : '#666'\n  };\n`;\n\nconst PositionDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin-bottom: 16px;\n`;\n\nconst DetailItem = styled.div`\n  text-align: center;\n`;\n\nconst DetailLabel = styled.div`\n  font-size: 10px;\n  color: #888;\n  margin-bottom: 2px;\n  text-transform: uppercase;\n  font-weight: 500;\n`;\n\nconst DetailValue = styled.div`\n  font-size: 14px;\n  font-weight: 600;\n  color: #fff;\n`;\n\nconst PositionActions = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-top: 12px;\n`;\n\nconst ActionButton = styled.button`\n  flex: 1;\n  padding: 8px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.close {\n    background: #ff4976;\n    color: #fff;\n    \n    &:hover:not(:disabled) {\n      background: #e63946;\n    }\n  }\n  \n  &.partial {\n    background: #ffa726;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #ff9800;\n    }\n  }\n  \n  &.info {\n    background: #2a2a2a;\n    color: #888;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n      color: #fff;\n    }\n  }\n`;\n\nconst SummaryCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 20px;\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 16px;\n`;\n\nconst SummaryItem = styled.div`\n  text-align: center;\n`;\n\nconst SummaryLabel = styled.div`\n  font-size: 12px;\n  color: #888;\n  margin-bottom: 4px;\n`;\n\nconst SummaryValue = styled.div`\n  font-size: 18px;\n  font-weight: 700;\n  color: ${props =>\n    props.$positive ? '#4bffb5' :\n    props.$negative ? '#ff4976' : '#fff'\n  };\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 40px 20px;\n  color: #888;\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: 16px;\n`;\n\nconst EmptyText = styled.div`\n  font-size: 16px;\n  margin-bottom: 8px;\n`;\n\nconst EmptySubtext = styled.div`\n  font-size: 12px;\n  color: #666;\n`;\n\nconst ActivePositionsDashboard = ({ portfolio }) => {\n  const [positions, setPositions] = useState([]);\n  const [summary, setSummary] = useState({\n    totalPositions: 0,\n    totalValue: 0,\n    totalPnL: 0,\n    totalPnLPercent: 0\n  });\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  // Process portfolio data\n  useEffect(() => {\n    if (portfolio && portfolio.positions) {\n      const positionArray = Object.entries(portfolio.positions).map(([symbol, position]) => {\n        const duration = position.entry_time ? \n          Math.round((Date.now() - new Date(position.entry_time)) / (1000 * 60)) : 0;\n        \n        const pnlPercent = position.entry_price ? \n          ((position.current_price - position.entry_price) / position.entry_price) * 100 : 0;\n        \n        return {\n          symbol,\n          ...position,\n          duration,\n          pnlPercent,\n          positionType: position.size > 0 ? 'LONG' : 'SHORT',\n          marketValue: position.size * position.current_price\n        };\n      });\n\n      setPositions(positionArray);\n\n      // Calculate summary\n      const totalValue = positionArray.reduce((sum, pos) => sum + pos.marketValue, 0);\n      const totalPnL = positionArray.reduce((sum, pos) => sum + (pos.unrealized_pnl || 0), 0);\n      const totalPnLPercent = portfolio.total_value ? (totalPnL / portfolio.total_value) * 100 : 0;\n\n      setSummary({\n        totalPositions: positionArray.length,\n        totalValue,\n        totalPnL,\n        totalPnLPercent\n      });\n\n      setLastUpdate(new Date());\n    }\n  }, [portfolio]);\n\n  const formatDuration = (minutes) => {\n    if (minutes < 60) return `${minutes}m`;\n    const hours = Math.floor(minutes / 60);\n    if (hours < 24) return `${hours}h ${minutes % 60}m`;\n    const days = Math.floor(hours / 24);\n    return `${days}d ${hours % 24}h`;\n  };\n\n  const handleClosePosition = async (symbol, isPartial = false) => {\n    try {\n      const position = positions.find(p => p.symbol === symbol);\n      const size = isPartial ? position.size / 2 : null;\n      \n      const response = await fetch('http://localhost:8000/api/close-position', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          symbol,\n          size,\n          reason: 'manual_close_dashboard'\n        }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        alert(`Position ${symbol} ${isPartial ? 'partially ' : ''}closed! P&L: $${result.pnl?.toFixed(2)}`);\n      } else {\n        alert('Failed to close position');\n      }\n    } catch (error) {\n      console.error('Error closing position:', error);\n      alert('Error closing position');\n    }\n  };\n\n  return (\n    <DashboardContainer>\n      <DashboardHeader>\n        📊 Active Positions\n        {lastUpdate && (\n          <div style={{ \n            fontSize: '10px', \n            color: '#666', \n            fontFamily: 'monospace'\n          }}>\n            Updated: {lastUpdate.toLocaleTimeString()}\n          </div>\n        )}\n      </DashboardHeader>\n\n      {/* Summary */}\n      <SummaryCard>\n        <SummaryItem>\n          <SummaryLabel>Total Positions</SummaryLabel>\n          <SummaryValue>{summary.totalPositions}</SummaryValue>\n        </SummaryItem>\n        <SummaryItem>\n          <SummaryLabel>Total Value</SummaryLabel>\n          <SummaryValue>${summary.totalValue.toFixed(2)}</SummaryValue>\n        </SummaryItem>\n        <SummaryItem>\n          <SummaryLabel>Total P&L</SummaryLabel>\n          <SummaryValue\n            $positive={summary.totalPnL > 0}\n            $negative={summary.totalPnL < 0}\n          >\n            ${summary.totalPnL.toFixed(2)}\n          </SummaryValue>\n        </SummaryItem>\n        <SummaryItem>\n          <SummaryLabel>P&L %</SummaryLabel>\n          <SummaryValue\n            $positive={summary.totalPnLPercent > 0}\n            $negative={summary.totalPnLPercent < 0}\n          >\n            {summary.totalPnLPercent >= 0 ? '+' : ''}{summary.totalPnLPercent.toFixed(2)}%\n          </SummaryValue>\n        </SummaryItem>\n      </SummaryCard>\n\n      {/* Positions */}\n      {positions.length === 0 ? (\n        <EmptyState>\n          <EmptyIcon>📈</EmptyIcon>\n          <EmptyText>No Active Positions</EmptyText>\n          <EmptySubtext>Start trading to see your positions here</EmptySubtext>\n        </EmptyState>\n      ) : (\n        <PositionsGrid>\n          {positions.map((position) => (\n            <PositionCard key={position.symbol} $pnl={position.unrealized_pnl}>\n              <PositionHeader>\n                <SymbolInfo>\n                  <Symbol>{position.symbol}</Symbol>\n                  <PositionType $type={position.positionType}>\n                    {position.positionType}\n                  </PositionType>\n                </SymbolInfo>\n                <PnLDisplay>\n                  <PnLValue $value={position.unrealized_pnl}>\n                    ${position.unrealized_pnl?.toFixed(2) || '0.00'}\n                  </PnLValue>\n                  <PnLPercentage $value={position.pnlPercent}>\n                    {position.pnlPercent >= 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%\n                  </PnLPercentage>\n                </PnLDisplay>\n              </PositionHeader>\n\n              <PositionDetails>\n                <DetailItem>\n                  <DetailLabel>Size</DetailLabel>\n                  <DetailValue>{Math.abs(position.size).toFixed(4)}</DetailValue>\n                </DetailItem>\n                <DetailItem>\n                  <DetailLabel>Entry Price</DetailLabel>\n                  <DetailValue>${position.entry_price?.toFixed(4)}</DetailValue>\n                </DetailItem>\n                <DetailItem>\n                  <DetailLabel>Current Price</DetailLabel>\n                  <DetailValue>${position.current_price?.toFixed(4)}</DetailValue>\n                </DetailItem>\n                <DetailItem>\n                  <DetailLabel>Duration</DetailLabel>\n                  <DetailValue>{formatDuration(position.duration)}</DetailValue>\n                </DetailItem>\n              </PositionDetails>\n\n              <PositionActions>\n                <ActionButton\n                  className=\"close\"\n                  onClick={() => handleClosePosition(position.symbol, false)}\n                >\n                  🗙 Close All\n                </ActionButton>\n                <ActionButton\n                  className=\"partial\"\n                  onClick={() => handleClosePosition(position.symbol, true)}\n                >\n                  📊 Close 50%\n                </ActionButton>\n                <ActionButton\n                  className=\"info\"\n                  onClick={() => {\n                    const info = `Position: ${position.symbol}\\nType: ${position.positionType}\\nSize: ${Math.abs(position.size).toFixed(4)}\\nEntry: $${position.entry_price?.toFixed(4)}\\nCurrent: $${position.current_price?.toFixed(4)}\\nP&L: $${position.unrealized_pnl?.toFixed(2)} (${position.pnlPercent.toFixed(2)}%)\\nDuration: ${formatDuration(position.duration)}\\nValue: $${position.marketValue.toFixed(2)}`;\n                    alert(info);\n                  }}\n                >\n                  ℹ️ Details\n                </ActionButton>\n              </PositionActions>\n            </PositionCard>\n          ))}\n        </PositionsGrid>\n      )}\n    </DashboardContainer>\n  );\n};\n\nexport default ActivePositionsDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,kBAAkB,GAAGH,MAAM,CAACI,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,kBAAkB;AASxB,MAAMG,eAAe,GAAGN,MAAM,CAACO,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,eAAe;AASrB,MAAMG,aAAa,GAAGT,MAAM,CAACI,GAAG;AAChC;AACA;AACA,CAAC;AAACM,GAAA,GAHID,aAAa;AAKnB,MAAME,YAAY,GAAGX,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA,2BAA2BQ,KAAK,IAC5BA,KAAK,CAACC,IAAI,GAAG,CAAC,GAAG,SAAS,GAC1BD,KAAK,CAACC,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,GAAA,GAdIH,YAAY;AAgBlB,MAAMI,cAAc,GAAGf,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GALID,cAAc;AAOpB,MAAME,UAAU,GAAGjB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,UAAU;AAMhB,MAAME,MAAM,GAAGnB,MAAM,CAACI,GAAG;AACzB;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,MAAM;AAMZ,MAAME,YAAY,GAAGrB,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA,gBAAgBQ,KAAK,IAAIA,KAAK,CAACU,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;AACvE,WAAWV,KAAK,IAAIA,KAAK,CAACU,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;AAC5D,CAAC;AAACC,GAAA,GAPIF,YAAY;AASlB,MAAMG,UAAU,GAAGxB,MAAM,CAACI,GAAG;AAC7B;AACA,CAAC;AAACqB,GAAA,GAFID,UAAU;AAIhB,MAAME,QAAQ,GAAG1B,MAAM,CAACI,GAAG;AAC3B;AACA;AACA,WAAWQ,KAAK,IACZA,KAAK,CAACe,MAAM,GAAG,CAAC,GAAG,SAAS,GAC5Bf,KAAK,CAACe,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM;AACzC,CACC;AAACC,GAAA,GAPIF,QAAQ;AASd,MAAMG,aAAa,GAAG7B,MAAM,CAACI,GAAG;AAChC;AACA,WAAWQ,KAAK,IACZA,KAAK,CAACe,MAAM,GAAG,CAAC,GAAG,SAAS,GAC5Bf,KAAK,CAACe,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM;AACzC,CACC;AAACG,GAAA,GANID,aAAa;AAQnB,MAAME,eAAe,GAAG/B,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GALID,eAAe;AAOrB,MAAME,UAAU,GAAGjC,MAAM,CAACI,GAAG;AAC7B;AACA,CAAC;AAAC8B,IAAA,GAFID,UAAU;AAIhB,MAAME,WAAW,GAAGnC,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GANID,WAAW;AAQjB,MAAME,WAAW,GAAGrC,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAJID,WAAW;AAMjB,MAAME,eAAe,GAAGvC,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GAJID,eAAe;AAMrB,MAAME,YAAY,GAAGzC,MAAM,CAAC0C,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA3CIF,YAAY;AA6ClB,MAAMG,WAAW,GAAG5C,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GARID,WAAW;AAUjB,MAAME,WAAW,GAAG9C,MAAM,CAACI,GAAG;AAC9B;AACA,CAAC;AAAC2C,IAAA,GAFID,WAAW;AAIjB,MAAME,YAAY,GAAGhD,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GAJID,YAAY;AAMlB,MAAME,YAAY,GAAGlD,MAAM,CAACI,GAAG;AAC/B;AACA;AACA,WAAWQ,KAAK,IACZA,KAAK,CAACuC,SAAS,GAAG,SAAS,GAC3BvC,KAAK,CAACwC,SAAS,GAAG,SAAS,GAAG,MAAM;AACxC,CACC;AAACC,IAAA,GAPIH,YAAY;AASlB,MAAMI,UAAU,GAAGtD,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACmD,IAAA,GAJID,UAAU;AAMhB,MAAME,SAAS,GAAGxD,MAAM,CAACI,GAAG;AAC5B;AACA;AACA,CAAC;AAACqD,IAAA,GAHID,SAAS;AAKf,MAAME,SAAS,GAAG1D,MAAM,CAACI,GAAG;AAC5B;AACA;AACA,CAAC;AAACuD,IAAA,GAHID,SAAS;AAKf,MAAME,YAAY,GAAG5D,MAAM,CAACI,GAAG;AAC/B;AACA;AACA,CAAC;AAACyD,IAAA,GAHID,YAAY;AAKlB,MAAME,wBAAwB,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC;IACrCuE,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,CAAC;IACXC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgE,SAAS,IAAIA,SAAS,CAACE,SAAS,EAAE;MACpC,MAAMU,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACd,SAAS,CAACE,SAAS,CAAC,CAACa,GAAG,CAAC,CAAC,CAACC,MAAM,EAAEC,QAAQ,CAAC,KAAK;QACpF,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,UAAU,GAClCC,IAAI,CAACC,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAID,IAAI,CAACL,QAAQ,CAACE,UAAU,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC;QAE5E,MAAMK,UAAU,GAAGP,QAAQ,CAACQ,WAAW,GACpC,CAACR,QAAQ,CAACS,aAAa,GAAGT,QAAQ,CAACQ,WAAW,IAAIR,QAAQ,CAACQ,WAAW,GAAI,GAAG,GAAG,CAAC;QAEpF,OAAO;UACLT,MAAM;UACN,GAAGC,QAAQ;UACXC,QAAQ;UACRM,UAAU;UACVG,YAAY,EAAEV,QAAQ,CAACW,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;UAClDC,WAAW,EAAEZ,QAAQ,CAACW,IAAI,GAAGX,QAAQ,CAACS;QACxC,CAAC;MACH,CAAC,CAAC;MAEFvB,YAAY,CAACS,aAAa,CAAC;;MAE3B;MACA,MAAML,UAAU,GAAGK,aAAa,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACH,WAAW,EAAE,CAAC,CAAC;MAC/E,MAAMrB,QAAQ,GAAGI,aAAa,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACC,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACvF,MAAMxB,eAAe,GAAGT,SAAS,CAACkC,WAAW,GAAI1B,QAAQ,GAAGR,SAAS,CAACkC,WAAW,GAAI,GAAG,GAAG,CAAC;MAE5F7B,UAAU,CAAC;QACTC,cAAc,EAAEM,aAAa,CAACuB,MAAM;QACpC5B,UAAU;QACVC,QAAQ;QACRC;MACF,CAAC,CAAC;MAEFE,aAAa,CAAC,IAAIW,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACtB,SAAS,CAAC,CAAC;EAEf,MAAMoC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,GAAGA,OAAO,GAAG;IACtC,MAAMC,KAAK,GAAGlB,IAAI,CAACmB,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACtC,IAAIC,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,KAAKD,OAAO,GAAG,EAAE,GAAG;IACnD,MAAMG,IAAI,GAAGpB,IAAI,CAACmB,KAAK,CAACD,KAAK,GAAG,EAAE,CAAC;IACnC,OAAO,GAAGE,IAAI,KAAKF,KAAK,GAAG,EAAE,GAAG;EAClC,CAAC;EAED,MAAMG,mBAAmB,GAAG,MAAAA,CAAOzB,MAAM,EAAE0B,SAAS,GAAG,KAAK,KAAK;IAC/D,IAAI;MACF,MAAMzB,QAAQ,GAAGf,SAAS,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,MAAM,KAAKA,MAAM,CAAC;MACzD,MAAMY,IAAI,GAAGc,SAAS,GAAGzB,QAAQ,CAACW,IAAI,GAAG,CAAC,GAAG,IAAI;MAEjD,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,EAAE;QACvEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBnC,MAAM;UACNY,IAAI;UACJwB,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAIP,QAAQ,CAACQ,EAAE,EAAE;QAAA,IAAAC,WAAA;QACf,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;QACpCC,KAAK,CAAC,YAAYzC,MAAM,IAAI0B,SAAS,GAAG,YAAY,GAAG,EAAE,kBAAAY,WAAA,GAAiBC,MAAM,CAACG,GAAG,cAAAJ,WAAA,uBAAVA,WAAA,CAAYK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;MACrG,CAAC,MAAM;QACLF,KAAK,CAAC,0BAA0B,CAAC;MACnC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CH,KAAK,CAAC,wBAAwB,CAAC;IACjC;EACF,CAAC;EAED,oBACEtH,OAAA,CAACC,kBAAkB;IAAA0H,QAAA,gBACjB3H,OAAA,CAACI,eAAe;MAAAuH,QAAA,GAAC,+BAEf,EAACpD,UAAU,iBACTvE,OAAA;QAAK4H,KAAK,EAAE;UACVC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE;QACd,CAAE;QAAAJ,QAAA,GAAC,WACQ,EAACpD,UAAU,CAACyD,kBAAkB,CAAC,CAAC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBpI,OAAA,CAAC0C,WAAW;MAAAiF,QAAA,gBACV3H,OAAA,CAAC4C,WAAW;QAAA+E,QAAA,gBACV3H,OAAA,CAAC8C,YAAY;UAAA6E,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAC5CpI,OAAA,CAACgD,YAAY;UAAA2E,QAAA,EAAE1D,OAAO,CAACE;QAAc;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACdpI,OAAA,CAAC4C,WAAW;QAAA+E,QAAA,gBACV3H,OAAA,CAAC8C,YAAY;UAAA6E,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACxCpI,OAAA,CAACgD,YAAY;UAAA2E,QAAA,GAAC,GAAC,EAAC1D,OAAO,CAACG,UAAU,CAACoD,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACdpI,OAAA,CAAC4C,WAAW;QAAA+E,QAAA,gBACV3H,OAAA,CAAC8C,YAAY;UAAA6E,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACtCpI,OAAA,CAACgD,YAAY;UACXC,SAAS,EAAEgB,OAAO,CAACI,QAAQ,GAAG,CAAE;UAChCnB,SAAS,EAAEe,OAAO,CAACI,QAAQ,GAAG,CAAE;UAAAsD,QAAA,GACjC,GACE,EAAC1D,OAAO,CAACI,QAAQ,CAACmD,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACdpI,OAAA,CAAC4C,WAAW;QAAA+E,QAAA,gBACV3H,OAAA,CAAC8C,YAAY;UAAA6E,QAAA,EAAC;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAClCpI,OAAA,CAACgD,YAAY;UACXC,SAAS,EAAEgB,OAAO,CAACK,eAAe,GAAG,CAAE;UACvCpB,SAAS,EAAEe,OAAO,CAACK,eAAe,GAAG,CAAE;UAAAqD,QAAA,GAEtC1D,OAAO,CAACK,eAAe,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEL,OAAO,CAACK,eAAe,CAACkD,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/E;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGbrE,SAAS,CAACiC,MAAM,KAAK,CAAC,gBACrBhG,OAAA,CAACoD,UAAU;MAAAuE,QAAA,gBACT3H,OAAA,CAACsD,SAAS;QAAAqE,QAAA,EAAC;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACzBpI,OAAA,CAACwD,SAAS;QAAAmE,QAAA,EAAC;MAAmB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC1CpI,OAAA,CAAC0D,YAAY;QAAAiE,QAAA,EAAC;MAAwC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,gBAEbpI,OAAA,CAACO,aAAa;MAAAoH,QAAA,EACX5D,SAAS,CAACa,GAAG,CAAEE,QAAQ;QAAA,IAAAuD,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAAA,oBACtBvI,OAAA,CAACS,YAAY;UAAuBE,IAAI,EAAEmE,QAAQ,CAACgB,cAAe;UAAA6B,QAAA,gBAChE3H,OAAA,CAACa,cAAc;YAAA8G,QAAA,gBACb3H,OAAA,CAACe,UAAU;cAAA4G,QAAA,gBACT3H,OAAA,CAACiB,MAAM;gBAAA0G,QAAA,EAAE7C,QAAQ,CAACD;cAAM;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAClCpI,OAAA,CAACmB,YAAY;gBAACC,KAAK,EAAE0D,QAAQ,CAACU,YAAa;gBAAAmC,QAAA,EACxC7C,QAAQ,CAACU;cAAY;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACbpI,OAAA,CAACsB,UAAU;cAAAqG,QAAA,gBACT3H,OAAA,CAACwB,QAAQ;gBAACC,MAAM,EAAEqD,QAAQ,CAACgB,cAAe;gBAAA6B,QAAA,GAAC,GACxC,EAAC,EAAAU,qBAAA,GAAAvD,QAAQ,CAACgB,cAAc,cAAAuC,qBAAA,uBAAvBA,qBAAA,CAAyBb,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACXpI,OAAA,CAAC2B,aAAa;gBAACF,MAAM,EAAEqD,QAAQ,CAACO,UAAW;gBAAAsC,QAAA,GACxC7C,QAAQ,CAACO,UAAU,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEP,QAAQ,CAACO,UAAU,CAACmC,OAAO,CAAC,CAAC,CAAC,EAAC,GACvE;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEjBpI,OAAA,CAAC6B,eAAe;YAAA8F,QAAA,gBACd3H,OAAA,CAAC+B,UAAU;cAAA4F,QAAA,gBACT3H,OAAA,CAACiC,WAAW;gBAAA0F,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/BpI,OAAA,CAACmC,WAAW;gBAAAwF,QAAA,EAAE1C,IAAI,CAACuD,GAAG,CAAC1D,QAAQ,CAACW,IAAI,CAAC,CAAC+B,OAAO,CAAC,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACbpI,OAAA,CAAC+B,UAAU;cAAA4F,QAAA,gBACT3H,OAAA,CAACiC,WAAW;gBAAA0F,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtCpI,OAAA,CAACmC,WAAW;gBAAAwF,QAAA,GAAC,GAAC,GAAAW,qBAAA,GAACxD,QAAQ,CAACQ,WAAW,cAAAgD,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACbpI,OAAA,CAAC+B,UAAU;cAAA4F,QAAA,gBACT3H,OAAA,CAACiC,WAAW;gBAAA0F,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxCpI,OAAA,CAACmC,WAAW;gBAAAwF,QAAA,GAAC,GAAC,GAAAY,qBAAA,GAACzD,QAAQ,CAACS,aAAa,cAAAgD,qBAAA,uBAAtBA,qBAAA,CAAwBf,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACbpI,OAAA,CAAC+B,UAAU;cAAA4F,QAAA,gBACT3H,OAAA,CAACiC,WAAW;gBAAA0F,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnCpI,OAAA,CAACmC,WAAW;gBAAAwF,QAAA,EAAE1B,cAAc,CAACnB,QAAQ,CAACC,QAAQ;cAAC;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAElBpI,OAAA,CAACqC,eAAe;YAAAsF,QAAA,gBACd3H,OAAA,CAACuC,YAAY;cACXkG,SAAS,EAAC,OAAO;cACjBC,OAAO,EAAEA,CAAA,KAAMpC,mBAAmB,CAACxB,QAAQ,CAACD,MAAM,EAAE,KAAK,CAAE;cAAA8C,QAAA,EAC5D;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACfpI,OAAA,CAACuC,YAAY;cACXkG,SAAS,EAAC,SAAS;cACnBC,OAAO,EAAEA,CAAA,KAAMpC,mBAAmB,CAACxB,QAAQ,CAACD,MAAM,EAAE,IAAI,CAAE;cAAA8C,QAAA,EAC3D;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACfpI,OAAA,CAACuC,YAAY;cACXkG,SAAS,EAAC,MAAM;cAChBC,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;gBACb,MAAMC,IAAI,GAAG,aAAahE,QAAQ,CAACD,MAAM,WAAWC,QAAQ,CAACU,YAAY,WAAWP,IAAI,CAACuD,GAAG,CAAC1D,QAAQ,CAACW,IAAI,CAAC,CAAC+B,OAAO,CAAC,CAAC,CAAC,cAAAmB,sBAAA,GAAa7D,QAAQ,CAACQ,WAAW,cAAAqD,sBAAA,uBAApBA,sBAAA,CAAsBnB,OAAO,CAAC,CAAC,CAAC,gBAAAoB,sBAAA,GAAe9D,QAAQ,CAACS,aAAa,cAAAqD,sBAAA,uBAAtBA,sBAAA,CAAwBpB,OAAO,CAAC,CAAC,CAAC,YAAAqB,sBAAA,GAAW/D,QAAQ,CAACgB,cAAc,cAAA+C,sBAAA,uBAAvBA,sBAAA,CAAyBrB,OAAO,CAAC,CAAC,CAAC,KAAK1C,QAAQ,CAACO,UAAU,CAACmC,OAAO,CAAC,CAAC,CAAC,iBAAiBvB,cAAc,CAACnB,QAAQ,CAACC,QAAQ,CAAC,aAAaD,QAAQ,CAACY,WAAW,CAAC8B,OAAO,CAAC,CAAC,CAAC,EAAE;gBACrYF,KAAK,CAACwB,IAAI,CAAC;cACb,CAAE;cAAAnB,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GA3DDtD,QAAQ,CAACD,MAAM;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4DpB,CAAC;MAAA,CAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAChB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB,CAAC;AAEzB,CAAC;AAACtE,EAAA,CA5MIF,wBAAwB;AAAAmF,IAAA,GAAxBnF,wBAAwB;AA8M9B,eAAeA,wBAAwB;AAAC,IAAAzD,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAoF,IAAA;AAAAC,YAAA,CAAA7I,EAAA;AAAA6I,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAlH,IAAA;AAAAkH,YAAA,CAAAhH,IAAA;AAAAgH,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAvG,IAAA;AAAAuG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}