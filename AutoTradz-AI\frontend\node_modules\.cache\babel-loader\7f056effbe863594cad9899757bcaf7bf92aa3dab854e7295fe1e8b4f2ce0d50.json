{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\CryptoPairSelector.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport styled, { keyframes } from 'styled-components';\n\n// Animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst slideDown = keyframes`\n  from { opacity: 0; transform: translateY(-10px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\nconst pulse = keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.02); }\n  100% { transform: scale(1); }\n`;\n\n// Styled Components\nconst SelectorContainer = styled.div`\n  position: relative;\n  display: inline-block;\n  min-width: 200px;\n`;\n_c = SelectorContainer;\nconst SelectorButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 12px 16px;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);\n  border: 2px solid ${props => props.$isOpen ? '#4bffb5' : '#333'};\n  border-radius: 12px;\n  color: #fff;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  &:hover {\n    border-color: #4bffb5;\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(75, 255, 181, 0.2);\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(75, 255, 181, 0.1), transparent);\n    transition: left 0.5s ease;\n  }\n\n  &:hover::before {\n    left: 100%;\n  }\n`;\n_c2 = SelectorButton;\nconst PairInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n_c3 = PairInfo;\nconst PairIcon = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  color: white;\n`;\n_c4 = PairIcon;\nconst PairDetails = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n_c5 = PairDetails;\nconst PairSymbol = styled.span`\n  font-size: 16px;\n  font-weight: 700;\n  color: #4bffb5;\n`;\n_c6 = PairSymbol;\nconst PairName = styled.span`\n  font-size: 12px;\n  color: #888;\n  margin-top: -2px;\n`;\n_c7 = PairName;\nconst DropdownArrow = styled.div`\n  width: 0;\n  height: 0;\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  border-top: 6px solid #4bffb5;\n  transition: transform 0.3s ease;\n  transform: ${props => props.$isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};\n`;\n_c8 = DropdownArrow;\nconst DropdownMenu = styled.div`\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: #1a1a1a;\n  border: 2px solid #333;\n  border-radius: 12px;\n  margin-top: 4px;\n  max-height: 400px;\n  overflow-y: auto;\n  z-index: 1000;\n  animation: ${slideDown} 0.3s ease;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);\n\n  /* Custom scrollbar */\n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n\n  &::-webkit-scrollbar-track {\n    background: #2a2a2a;\n    border-radius: 3px;\n  }\n\n  &::-webkit-scrollbar-thumb {\n    background: #4bffb5;\n    border-radius: 3px;\n  }\n`;\n_c9 = DropdownMenu;\nconst CategoryHeader = styled.div`\n  padding: 12px 16px 8px;\n  font-size: 12px;\n  font-weight: 700;\n  color: #4bffb5;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  border-bottom: 1px solid #333;\n  background: #2a2a2a;\n  \n  &:first-child {\n    border-top-left-radius: 10px;\n    border-top-right-radius: 10px;\n  }\n`;\n_c0 = CategoryHeader;\nconst PairOption = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-bottom: 1px solid #2a2a2a;\n\n  &:hover {\n    background: rgba(75, 255, 181, 0.1);\n    transform: translateX(4px);\n  }\n\n  &:last-child {\n    border-bottom: none;\n    border-bottom-left-radius: 10px;\n    border-bottom-right-radius: 10px;\n  }\n`;\n_c1 = PairOption;\nconst OptionIcon = styled.div`\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n_c10 = OptionIcon;\nconst OptionDetails = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;\n_c11 = OptionDetails;\nconst OptionSymbol = styled.div`\n  font-size: 14px;\n  font-weight: 600;\n  color: #fff;\n`;\n_c12 = OptionSymbol;\nconst OptionName = styled.div`\n  font-size: 12px;\n  color: #888;\n  margin-top: 2px;\n`;\n_c13 = OptionName;\nconst OptionMeta = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 4px;\n`;\n_c14 = OptionMeta;\nconst VolatilityBadge = styled.span`\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  font-weight: 600;\n  background: ${props => {\n  switch (props.$level) {\n    case 'low':\n      return 'rgba(34, 197, 94, 0.2)';\n    case 'medium':\n      return 'rgba(251, 191, 36, 0.2)';\n    case 'high':\n      return 'rgba(239, 68, 68, 0.2)';\n    default:\n      return 'rgba(156, 163, 175, 0.2)';\n  }\n}};\n  color: ${props => {\n  switch (props.$level) {\n    case 'low':\n      return '#22c55e';\n    case 'medium':\n      return '#fbbf24';\n    case 'high':\n      return '#ef4444';\n    default:\n      return '#9ca3af';\n  }\n}};\n`;\n_c15 = VolatilityBadge;\nconst RankBadge = styled.span`\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  font-weight: 600;\n  background: rgba(75, 255, 181, 0.2);\n  color: #4bffb5;\n`;\n_c16 = RankBadge;\nconst SearchInput = styled.input`\n  width: 100%;\n  padding: 12px 16px;\n  background: #2a2a2a;\n  border: none;\n  border-bottom: 1px solid #333;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    background: #333;\n  }\n  \n  &::placeholder {\n    color: #666;\n  }\n`;\n_c17 = SearchInput;\nconst CryptoPairSelector = ({\n  currentPair,\n  onPairChange,\n  availablePairs = []\n}) => {\n  _s();\n  var _currentPairData$base;\n  const [isOpen, setIsOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [pairs, setPairs] = useState([]);\n  const dropdownRef = useRef(null);\n\n  // Fetch available pairs on component mount\n  useEffect(() => {\n    fetchAvailablePairs();\n  }, []);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  const fetchAvailablePairs = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/crypto-pairs');\n      const data = await response.json();\n      setPairs(data.pairs || []);\n    } catch (error) {\n      console.error('Failed to fetch crypto pairs:', error);\n      // Fallback to provided pairs or default\n      setPairs(availablePairs);\n    }\n  };\n  const filteredPairs = pairs.filter(pair => pair.symbol.toLowerCase().includes(searchTerm.toLowerCase()) || pair.display_name.toLowerCase().includes(searchTerm.toLowerCase()));\n  const groupedPairs = filteredPairs.reduce((groups, pair) => {\n    const category = pair.category || 'other';\n    if (!groups[category]) {\n      groups[category] = [];\n    }\n    groups[category].push(pair);\n    return groups;\n  }, {});\n  const categoryOrder = ['major', 'defi', 'layer1', 'layer2', 'meme', 'other'];\n  const categoryNames = {\n    major: '🏆 Major Cryptocurrencies',\n    defi: '🔄 DeFi Tokens',\n    layer1: '🏗️ Layer 1 Blockchains',\n    layer2: '⚡ Layer 2 Solutions',\n    meme: '🐕 Meme Coins',\n    other: '📊 Other Assets'\n  };\n  const handlePairSelect = pair => {\n    onPairChange(pair.symbol);\n    setIsOpen(false);\n    setSearchTerm('');\n  };\n  const currentPairData = pairs.find(p => p.symbol === currentPair) || {\n    symbol: currentPair || 'XRP/USD',\n    display_name: 'XRP',\n    base_asset: 'XRP'\n  };\n  return /*#__PURE__*/_jsxDEV(SelectorContainer, {\n    ref: dropdownRef,\n    children: [/*#__PURE__*/_jsxDEV(SelectorButton, {\n      onClick: () => setIsOpen(!isOpen),\n      $isOpen: isOpen,\n      children: [/*#__PURE__*/_jsxDEV(PairInfo, {\n        children: [/*#__PURE__*/_jsxDEV(PairIcon, {\n          children: ((_currentPairData$base = currentPairData.base_asset) === null || _currentPairData$base === void 0 ? void 0 : _currentPairData$base.charAt(0)) || 'X'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PairDetails, {\n          children: [/*#__PURE__*/_jsxDEV(PairSymbol, {\n            children: currentPairData.symbol\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PairName, {\n            children: currentPairData.display_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DropdownArrow, {\n        $isOpen: isOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(DropdownMenu, {\n      children: [/*#__PURE__*/_jsxDEV(SearchInput, {\n        type: \"text\",\n        placeholder: \"Search crypto pairs...\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value),\n        autoFocus: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 11\n      }, this), categoryOrder.map(category => {\n        const categoryPairs = groupedPairs[category];\n        if (!categoryPairs || categoryPairs.length === 0) return null;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(CategoryHeader, {\n            children: categoryNames[category] || category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 17\n          }, this), categoryPairs.map(pair => {\n            var _pair$base_asset;\n            return /*#__PURE__*/_jsxDEV(PairOption, {\n              onClick: () => handlePairSelect(pair),\n              children: [/*#__PURE__*/_jsxDEV(OptionIcon, {\n                children: ((_pair$base_asset = pair.base_asset) === null || _pair$base_asset === void 0 ? void 0 : _pair$base_asset.charAt(0)) || '?'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(OptionDetails, {\n                children: [/*#__PURE__*/_jsxDEV(OptionSymbol, {\n                  children: pair.symbol\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(OptionName, {\n                  children: pair.display_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(OptionMeta, {\n                  children: [/*#__PURE__*/_jsxDEV(VolatilityBadge, {\n                    $level: pair.volatility,\n                    children: [pair.volatility, \" vol\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(RankBadge, {\n                    children: [\"#\", pair.rank]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this)]\n            }, pair.symbol, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this);\n          })]\n        }, category, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 15\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 339,\n    columnNumber: 5\n  }, this);\n};\n_s(CryptoPairSelector, \"HdR3+um+SlnVTIKy3npOl3kBXf8=\");\n_c18 = CryptoPairSelector;\nexport default CryptoPairSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"SelectorContainer\");\n$RefreshReg$(_c2, \"SelectorButton\");\n$RefreshReg$(_c3, \"PairInfo\");\n$RefreshReg$(_c4, \"PairIcon\");\n$RefreshReg$(_c5, \"PairDetails\");\n$RefreshReg$(_c6, \"PairSymbol\");\n$RefreshReg$(_c7, \"PairName\");\n$RefreshReg$(_c8, \"DropdownArrow\");\n$RefreshReg$(_c9, \"DropdownMenu\");\n$RefreshReg$(_c0, \"CategoryHeader\");\n$RefreshReg$(_c1, \"PairOption\");\n$RefreshReg$(_c10, \"OptionIcon\");\n$RefreshReg$(_c11, \"OptionDetails\");\n$RefreshReg$(_c12, \"OptionSymbol\");\n$RefreshReg$(_c13, \"OptionName\");\n$RefreshReg$(_c14, \"OptionMeta\");\n$RefreshReg$(_c15, \"VolatilityBadge\");\n$RefreshReg$(_c16, \"RankBadge\");\n$RefreshReg$(_c17, \"SearchInput\");\n$RefreshReg$(_c18, \"CryptoPairSelector\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "styled", "keyframes", "jsxDEV", "_jsxDEV", "slideDown", "pulse", "SelectorContainer", "div", "_c", "Selector<PERSON><PERSON><PERSON>", "button", "props", "$isOpen", "_c2", "PairInfo", "_c3", "PairIcon", "_c4", "PairDetails", "_c5", "PairSymbol", "span", "_c6", "PairName", "_c7", "DropdownArrow", "_c8", "DropdownMenu", "_c9", "CategoryHeader", "_c0", "PairOption", "_c1", "OptionIcon", "_c10", "OptionDetails", "_c11", "OptionSymbol", "_c12", "OptionName", "_c13", "OptionMeta", "_c14", "VolatilityBadge", "$level", "_c15", "RankBadge", "_c16", "SearchInput", "input", "_c17", "CryptoPairSelector", "currentPair", "onPairChange", "availablePairs", "_s", "_currentPairData$base", "isOpen", "setIsOpen", "searchTerm", "setSearchTerm", "pairs", "setPairs", "dropdownRef", "fetchAvailablePairs", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "response", "fetch", "data", "json", "error", "console", "filteredPairs", "filter", "pair", "symbol", "toLowerCase", "includes", "display_name", "groupedPairs", "reduce", "groups", "category", "push", "categoryOrder", "categoryNames", "major", "defi", "layer1", "layer2", "meme", "other", "handlePairSelect", "currentPairData", "find", "p", "base_asset", "ref", "children", "onClick", "char<PERSON>t", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "autoFocus", "map", "categoryPairs", "length", "_pair$base_asset", "volatility", "rank", "_c18", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/CryptoPairSelector.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport styled, { keyframes } from 'styled-components';\n\n// Animations\nconst slideDown = keyframes`\n  from { opacity: 0; transform: translateY(-10px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\n\nconst pulse = keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.02); }\n  100% { transform: scale(1); }\n`;\n\n// Styled Components\nconst SelectorContainer = styled.div`\n  position: relative;\n  display: inline-block;\n  min-width: 200px;\n`;\n\nconst SelectorButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 12px 16px;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);\n  border: 2px solid ${props => props.$isOpen ? '#4bffb5' : '#333'};\n  border-radius: 12px;\n  color: #fff;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  &:hover {\n    border-color: #4bffb5;\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(75, 255, 181, 0.2);\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(75, 255, 181, 0.1), transparent);\n    transition: left 0.5s ease;\n  }\n\n  &:hover::before {\n    left: 100%;\n  }\n`;\n\nconst PairInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n\nconst PairIcon = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  color: white;\n`;\n\nconst PairDetails = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n\nconst PairSymbol = styled.span`\n  font-size: 16px;\n  font-weight: 700;\n  color: #4bffb5;\n`;\n\nconst PairName = styled.span`\n  font-size: 12px;\n  color: #888;\n  margin-top: -2px;\n`;\n\nconst DropdownArrow = styled.div`\n  width: 0;\n  height: 0;\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  border-top: 6px solid #4bffb5;\n  transition: transform 0.3s ease;\n  transform: ${props => props.$isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};\n`;\n\nconst DropdownMenu = styled.div`\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: #1a1a1a;\n  border: 2px solid #333;\n  border-radius: 12px;\n  margin-top: 4px;\n  max-height: 400px;\n  overflow-y: auto;\n  z-index: 1000;\n  animation: ${slideDown} 0.3s ease;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);\n\n  /* Custom scrollbar */\n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n\n  &::-webkit-scrollbar-track {\n    background: #2a2a2a;\n    border-radius: 3px;\n  }\n\n  &::-webkit-scrollbar-thumb {\n    background: #4bffb5;\n    border-radius: 3px;\n  }\n`;\n\nconst CategoryHeader = styled.div`\n  padding: 12px 16px 8px;\n  font-size: 12px;\n  font-weight: 700;\n  color: #4bffb5;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  border-bottom: 1px solid #333;\n  background: #2a2a2a;\n  \n  &:first-child {\n    border-top-left-radius: 10px;\n    border-top-right-radius: 10px;\n  }\n`;\n\nconst PairOption = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-bottom: 1px solid #2a2a2a;\n\n  &:hover {\n    background: rgba(75, 255, 181, 0.1);\n    transform: translateX(4px);\n  }\n\n  &:last-child {\n    border-bottom: none;\n    border-bottom-left-radius: 10px;\n    border-bottom-right-radius: 10px;\n  }\n`;\n\nconst OptionIcon = styled.div`\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n\nconst OptionDetails = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst OptionSymbol = styled.div`\n  font-size: 14px;\n  font-weight: 600;\n  color: #fff;\n`;\n\nconst OptionName = styled.div`\n  font-size: 12px;\n  color: #888;\n  margin-top: 2px;\n`;\n\nconst OptionMeta = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 4px;\n`;\n\nconst VolatilityBadge = styled.span`\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  font-weight: 600;\n  background: ${props => {\n    switch(props.$level) {\n      case 'low': return 'rgba(34, 197, 94, 0.2)';\n      case 'medium': return 'rgba(251, 191, 36, 0.2)';\n      case 'high': return 'rgba(239, 68, 68, 0.2)';\n      default: return 'rgba(156, 163, 175, 0.2)';\n    }\n  }};\n  color: ${props => {\n    switch(props.$level) {\n      case 'low': return '#22c55e';\n      case 'medium': return '#fbbf24';\n      case 'high': return '#ef4444';\n      default: return '#9ca3af';\n    }\n  }};\n`;\n\nconst RankBadge = styled.span`\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  font-weight: 600;\n  background: rgba(75, 255, 181, 0.2);\n  color: #4bffb5;\n`;\n\nconst SearchInput = styled.input`\n  width: 100%;\n  padding: 12px 16px;\n  background: #2a2a2a;\n  border: none;\n  border-bottom: 1px solid #333;\n  color: #fff;\n  font-size: 14px;\n  \n  &:focus {\n    outline: none;\n    background: #333;\n  }\n  \n  &::placeholder {\n    color: #666;\n  }\n`;\n\nconst CryptoPairSelector = ({ currentPair, onPairChange, availablePairs = [] }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [pairs, setPairs] = useState([]);\n  const dropdownRef = useRef(null);\n\n  // Fetch available pairs on component mount\n  useEffect(() => {\n    fetchAvailablePairs();\n  }, []);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const fetchAvailablePairs = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/crypto-pairs');\n      const data = await response.json();\n      setPairs(data.pairs || []);\n    } catch (error) {\n      console.error('Failed to fetch crypto pairs:', error);\n      // Fallback to provided pairs or default\n      setPairs(availablePairs);\n    }\n  };\n\n  const filteredPairs = pairs.filter(pair =>\n    pair.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    pair.display_name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const groupedPairs = filteredPairs.reduce((groups, pair) => {\n    const category = pair.category || 'other';\n    if (!groups[category]) {\n      groups[category] = [];\n    }\n    groups[category].push(pair);\n    return groups;\n  }, {});\n\n  const categoryOrder = ['major', 'defi', 'layer1', 'layer2', 'meme', 'other'];\n  const categoryNames = {\n    major: '🏆 Major Cryptocurrencies',\n    defi: '🔄 DeFi Tokens',\n    layer1: '🏗️ Layer 1 Blockchains',\n    layer2: '⚡ Layer 2 Solutions',\n    meme: '🐕 Meme Coins',\n    other: '📊 Other Assets'\n  };\n\n  const handlePairSelect = (pair) => {\n    onPairChange(pair.symbol);\n    setIsOpen(false);\n    setSearchTerm('');\n  };\n\n  const currentPairData = pairs.find(p => p.symbol === currentPair) || {\n    symbol: currentPair || 'XRP/USD',\n    display_name: 'XRP',\n    base_asset: 'XRP'\n  };\n\n  return (\n    <SelectorContainer ref={dropdownRef}>\n      <SelectorButton\n        onClick={() => setIsOpen(!isOpen)}\n        $isOpen={isOpen}\n      >\n        <PairInfo>\n          <PairIcon>\n            {currentPairData.base_asset?.charAt(0) || 'X'}\n          </PairIcon>\n          <PairDetails>\n            <PairSymbol>{currentPairData.symbol}</PairSymbol>\n            <PairName>{currentPairData.display_name}</PairName>\n          </PairDetails>\n        </PairInfo>\n        <DropdownArrow $isOpen={isOpen} />\n      </SelectorButton>\n\n      {isOpen && (\n        <DropdownMenu>\n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search crypto pairs...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            autoFocus\n          />\n          \n          {categoryOrder.map(category => {\n            const categoryPairs = groupedPairs[category];\n            if (!categoryPairs || categoryPairs.length === 0) return null;\n\n            return (\n              <div key={category}>\n                <CategoryHeader>\n                  {categoryNames[category] || category}\n                </CategoryHeader>\n                {categoryPairs.map(pair => (\n                  <PairOption\n                    key={pair.symbol}\n                    onClick={() => handlePairSelect(pair)}\n                  >\n                    <OptionIcon>\n                      {pair.base_asset?.charAt(0) || '?'}\n                    </OptionIcon>\n                    <OptionDetails>\n                      <OptionSymbol>{pair.symbol}</OptionSymbol>\n                      <OptionName>{pair.display_name}</OptionName>\n                      <OptionMeta>\n                        <VolatilityBadge $level={pair.volatility}>\n                          {pair.volatility} vol\n                        </VolatilityBadge>\n                        <RankBadge>#{pair.rank}</RankBadge>\n                      </OptionMeta>\n                    </OptionDetails>\n                  </PairOption>\n                ))}\n              </div>\n            );\n          })}\n        </DropdownMenu>\n      )}\n    </SelectorContainer>\n  );\n};\n\nexport default CryptoPairSelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAS,GAAGH,SAAS;AAC3B;AACA;AACA,CAAC;AAED,MAAMI,KAAK,GAAGJ,SAAS;AACvB;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMK,iBAAiB,GAAGN,MAAM,CAACO,GAAG;AACpC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,iBAAiB;AAMvB,MAAMG,cAAc,GAAGT,MAAM,CAACU,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GArCIJ,cAAc;AAuCpB,MAAMK,QAAQ,GAAGd,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGhB,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAXID,QAAQ;AAad,MAAME,WAAW,GAAGlB,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAJID,WAAW;AAMjB,MAAME,UAAU,GAAGpB,MAAM,CAACqB,IAAI;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,QAAQ,GAAGvB,MAAM,CAACqB,IAAI;AAC5B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,QAAQ;AAMd,MAAME,aAAa,GAAGzB,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,eAAeI,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,gBAAgB,GAAG,cAAc;AACzE,CAAC;AAACc,GAAA,GARID,aAAa;AAUnB,MAAME,YAAY,GAAG3B,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GA7BID,YAAY;AA+BlB,MAAME,cAAc,GAAG7B,MAAM,CAACO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GAdID,cAAc;AAgBpB,MAAME,UAAU,GAAG/B,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GAnBID,UAAU;AAqBhB,MAAME,UAAU,GAAGjC,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2B,IAAA,GAZID,UAAU;AAchB,MAAME,aAAa,GAAGnC,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGrC,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GAJID,YAAY;AAMlB,MAAME,UAAU,GAAGvC,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAGzC,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GALID,UAAU;AAOhB,MAAME,eAAe,GAAG3C,MAAM,CAACqB,IAAI;AACnC;AACA;AACA;AACA;AACA,gBAAgBV,KAAK,IAAI;EACrB,QAAOA,KAAK,CAACiC,MAAM;IACjB,KAAK,KAAK;MAAE,OAAO,wBAAwB;IAC3C,KAAK,QAAQ;MAAE,OAAO,yBAAyB;IAC/C,KAAK,MAAM;MAAE,OAAO,wBAAwB;IAC5C;MAAS,OAAO,0BAA0B;EAC5C;AACF,CAAC;AACH,WAAWjC,KAAK,IAAI;EAChB,QAAOA,KAAK,CAACiC,MAAM;IACjB,KAAK,KAAK;MAAE,OAAO,SAAS;IAC5B,KAAK,QAAQ;MAAE,OAAO,SAAS;IAC/B,KAAK,MAAM;MAAE,OAAO,SAAS;IAC7B;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AACH,CAAC;AAACC,IAAA,GArBIF,eAAe;AAuBrB,MAAMG,SAAS,GAAG9C,MAAM,CAACqB,IAAI;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,IAAA,GAPID,SAAS;AASf,MAAME,WAAW,GAAGhD,MAAM,CAACiD,KAAK;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAjBIF,WAAW;AAmBjB,MAAMG,kBAAkB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,cAAc,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACjF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgE,KAAK,EAAEC,QAAQ,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMkE,WAAW,GAAGhE,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAD,SAAS,CAAC,MAAM;IACdkE,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlE,SAAS,CAAC,MAAM;IACd,MAAMmE,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEX,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDY,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,CAAC;MACtE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCd,QAAQ,CAACa,IAAI,CAACd,KAAK,IAAI,EAAE,CAAC;IAC5B,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACAf,QAAQ,CAACR,cAAc,CAAC;IAC1B;EACF,CAAC;EAED,MAAMyB,aAAa,GAAGlB,KAAK,CAACmB,MAAM,CAACC,IAAI,IACrCA,IAAI,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CAAC,IAC5DF,IAAI,CAACI,YAAY,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CACnE,CAAC;EAED,MAAMG,YAAY,GAAGP,aAAa,CAACQ,MAAM,CAAC,CAACC,MAAM,EAAEP,IAAI,KAAK;IAC1D,MAAMQ,QAAQ,GAAGR,IAAI,CAACQ,QAAQ,IAAI,OAAO;IACzC,IAAI,CAACD,MAAM,CAACC,QAAQ,CAAC,EAAE;MACrBD,MAAM,CAACC,QAAQ,CAAC,GAAG,EAAE;IACvB;IACAD,MAAM,CAACC,QAAQ,CAAC,CAACC,IAAI,CAACT,IAAI,CAAC;IAC3B,OAAOO,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMG,aAAa,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;EAC5E,MAAMC,aAAa,GAAG;IACpBC,KAAK,EAAE,2BAA2B;IAClCC,IAAI,EAAE,gBAAgB;IACtBC,MAAM,EAAE,yBAAyB;IACjCC,MAAM,EAAE,qBAAqB;IAC7BC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,gBAAgB,GAAIlB,IAAI,IAAK;IACjC5B,YAAY,CAAC4B,IAAI,CAACC,MAAM,CAAC;IACzBxB,SAAS,CAAC,KAAK,CAAC;IAChBE,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMwC,eAAe,GAAGvC,KAAK,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,MAAM,KAAK9B,WAAW,CAAC,IAAI;IACnE8B,MAAM,EAAE9B,WAAW,IAAI,SAAS;IAChCiC,YAAY,EAAE,KAAK;IACnBkB,UAAU,EAAE;EACd,CAAC;EAED,oBACEpG,OAAA,CAACG,iBAAiB;IAACkG,GAAG,EAAEzC,WAAY;IAAA0C,QAAA,gBAClCtG,OAAA,CAACM,cAAc;MACbiG,OAAO,EAAEA,CAAA,KAAMhD,SAAS,CAAC,CAACD,MAAM,CAAE;MAClC7C,OAAO,EAAE6C,MAAO;MAAAgD,QAAA,gBAEhBtG,OAAA,CAACW,QAAQ;QAAA2F,QAAA,gBACPtG,OAAA,CAACa,QAAQ;UAAAyF,QAAA,EACN,EAAAjD,qBAAA,GAAA4C,eAAe,CAACG,UAAU,cAAA/C,qBAAA,uBAA1BA,qBAAA,CAA4BmD,MAAM,CAAC,CAAC,CAAC,KAAI;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACX5G,OAAA,CAACe,WAAW;UAAAuF,QAAA,gBACVtG,OAAA,CAACiB,UAAU;YAAAqF,QAAA,EAAEL,eAAe,CAAClB;UAAM;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACjD5G,OAAA,CAACoB,QAAQ;YAAAkF,QAAA,EAAEL,eAAe,CAACf;UAAY;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACX5G,OAAA,CAACsB,aAAa;QAACb,OAAO,EAAE6C;MAAO;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,EAEhBtD,MAAM,iBACLtD,OAAA,CAACwB,YAAY;MAAA8E,QAAA,gBACXtG,OAAA,CAAC6C,WAAW;QACVgE,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,wBAAwB;QACpCC,KAAK,EAAEvD,UAAW;QAClBwD,QAAQ,EAAGC,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAAC/C,MAAM,CAAC6C,KAAK,CAAE;QAC/CG,SAAS;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAEDpB,aAAa,CAAC2B,GAAG,CAAC7B,QAAQ,IAAI;QAC7B,MAAM8B,aAAa,GAAGjC,YAAY,CAACG,QAAQ,CAAC;QAC5C,IAAI,CAAC8B,aAAa,IAAIA,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAE7D,oBACErH,OAAA;UAAAsG,QAAA,gBACEtG,OAAA,CAAC0B,cAAc;YAAA4E,QAAA,EACZb,aAAa,CAACH,QAAQ,CAAC,IAAIA;UAAQ;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,EAChBQ,aAAa,CAACD,GAAG,CAACrC,IAAI;YAAA,IAAAwC,gBAAA;YAAA,oBACrBtH,OAAA,CAAC4B,UAAU;cAET2E,OAAO,EAAEA,CAAA,KAAMP,gBAAgB,CAAClB,IAAI,CAAE;cAAAwB,QAAA,gBAEtCtG,OAAA,CAAC8B,UAAU;gBAAAwE,QAAA,EACR,EAAAgB,gBAAA,GAAAxC,IAAI,CAACsB,UAAU,cAAAkB,gBAAA,uBAAfA,gBAAA,CAAiBd,MAAM,CAAC,CAAC,CAAC,KAAI;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACb5G,OAAA,CAACgC,aAAa;gBAAAsE,QAAA,gBACZtG,OAAA,CAACkC,YAAY;kBAAAoE,QAAA,EAAExB,IAAI,CAACC;gBAAM;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC1C5G,OAAA,CAACoC,UAAU;kBAAAkE,QAAA,EAAExB,IAAI,CAACI;gBAAY;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC5C5G,OAAA,CAACsC,UAAU;kBAAAgE,QAAA,gBACTtG,OAAA,CAACwC,eAAe;oBAACC,MAAM,EAAEqC,IAAI,CAACyC,UAAW;oBAAAjB,QAAA,GACtCxB,IAAI,CAACyC,UAAU,EAAC,MACnB;kBAAA;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiB,CAAC,eAClB5G,OAAA,CAAC2C,SAAS;oBAAA2D,QAAA,GAAC,GAAC,EAACxB,IAAI,CAAC0C,IAAI;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAfX9B,IAAI,CAACC,MAAM;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBN,CAAC;UAAA,CACd,CAAC;QAAA,GAvBMtB,QAAQ;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBb,CAAC;MAEV,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CACf;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAExB,CAAC;AAACxD,EAAA,CAvIIJ,kBAAkB;AAAAyE,IAAA,GAAlBzE,kBAAkB;AAyIxB,eAAeA,kBAAkB;AAAC,IAAA3C,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAA0E,IAAA;AAAAC,YAAA,CAAArH,EAAA;AAAAqH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}