{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\AdvancedChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport styled from 'styled-components';\nimport { createChart, ColorType } from 'lightweight-charts';\n\n// Simple cache for market data to speed up chart loading\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst advancedDataCache = new Map();\nconst CACHE_DURATION = 30000; // 30 seconds\n\nconst getCacheKey = (symbol, timeframe) => `advanced-${symbol}-${timeframe}`;\nconst getCachedData = (symbol, timeframe) => {\n  const key = getCacheKey(symbol, timeframe);\n  const cached = advancedDataCache.get(key);\n  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n    console.log(`📦 AdvancedChart: Using cached data for ${symbol} ${timeframe}`);\n    return cached.data;\n  }\n  return null;\n};\nconst setCachedData = (symbol, timeframe, data) => {\n  const key = getCacheKey(symbol, timeframe);\n  advancedDataCache.set(key, {\n    data,\n    timestamp: Date.now()\n  });\n  console.log(`💾 AdvancedChart: Cached data for ${symbol} ${timeframe}: ${data.length} points`);\n};\nconst ChartContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #333;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n`;\n_c = ChartContainer;\nconst ChartHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n  gap: 12px;\n`;\n_c2 = ChartHeader;\nconst Title = styled.h3`\n  color: #4bffb5;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n`;\n_c3 = Title;\nconst ControlsContainer = styled.div`\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  align-items: center;\n`;\n_c4 = ControlsContainer;\nconst TimeframeButton = styled.button`\n  background: ${props => props.$active ? '#4bffb5' : '#333'};\n  color: ${props => props.$active ? '#000' : '#ccc'};\n  border: none;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#3de89f' : '#444'};\n  }\n`;\n_c5 = TimeframeButton;\nconst IndicatorButton = styled.button`\n  background: ${props => props.$active ? '#ff4976' : '#333'};\n  color: ${props => props.$active ? '#fff' : '#ccc'};\n  border: none;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#e63946' : '#444'};\n  }\n`;\n_c6 = IndicatorButton;\nconst ChartArea = styled.div`\n  flex: 1;\n  position: relative;\n  height: 500px;\n  background: #0a0a0a;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n_c7 = ChartArea;\nconst IndicatorInfo = styled.div`\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  background: rgba(26, 26, 26, 0.9);\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 11px;\n  color: #ccc;\n  border: 1px solid #333;\n  z-index: 10;\n  max-width: 300px;\n`;\n_c8 = IndicatorInfo;\nconst StatusBar = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background: rgba(26, 26, 26, 0.8);\n  border-radius: 6px;\n  margin-top: 8px;\n  font-size: 11px;\n  color: #888;\n`;\n_c9 = StatusBar;\nconst StatusItem = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 4px;\n`;\n_c0 = StatusItem;\nconst AdvancedChart = ({\n  marketData,\n  signals,\n  symbol = 'XRP-USD'\n}) => {\n  _s();\n  const chartContainerRef = useRef(null);\n  const chart = useRef(null);\n  const candlestickSeries = useRef(null);\n  const volumeSeries = useRef(null);\n  const indicatorSeries = useRef({});\n  const [timeframe, setTimeframe] = useState('1H');\n  const [activeIndicators, setActiveIndicators] = useState(['SMA20', 'SMA50', 'RSI', 'MACD']);\n  const [chartData, setChartData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [currentPrice, setCurrentPrice] = useState(0);\n  const [priceChange, setPriceChange] = useState(0);\n  const timeframes = [{\n    value: '1m',\n    label: '1M'\n  }, {\n    value: '5m',\n    label: '5M'\n  }, {\n    value: '15m',\n    label: '15M'\n  }, {\n    value: '1h',\n    label: '1H'\n  }, {\n    value: '4h',\n    label: '4H'\n  }, {\n    value: '1d',\n    label: '1D'\n  }];\n  const availableIndicators = [{\n    id: 'SMA20',\n    name: 'SMA 20',\n    color: '#ffa726'\n  }, {\n    id: 'SMA50',\n    name: 'SMA 50',\n    color: '#9c27b0'\n  }, {\n    id: 'EMA12',\n    name: 'EMA 12',\n    color: '#2196f3'\n  }, {\n    id: 'EMA26',\n    name: 'EMA 26',\n    color: '#ff5722'\n  }, {\n    id: 'RSI',\n    name: 'RSI',\n    color: '#4caf50'\n  }, {\n    id: 'MACD',\n    name: 'MACD',\n    color: '#ff9800'\n  }, {\n    id: 'BB',\n    name: 'Bollinger Bands',\n    color: '#e91e63'\n  }, {\n    id: 'ATR',\n    name: 'ATR',\n    color: '#795548'\n  }, {\n    id: 'STOCH',\n    name: 'Stochastic',\n    color: '#607d8b'\n  }, {\n    id: 'ADX',\n    name: 'ADX',\n    color: '#3f51b5'\n  }];\n\n  // Initialize chart\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n    try {\n      console.log('Initializing AdvancedChart with TradingView...');\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          background: {\n            type: ColorType.Solid,\n            color: '#0a0a0a'\n          },\n          textColor: '#ffffff'\n        },\n        grid: {\n          vertLines: {\n            color: '#2B2B43'\n          },\n          horzLines: {\n            color: '#2B2B43'\n          }\n        },\n        crosshair: {\n          mode: 1\n        },\n        rightPriceScale: {\n          borderColor: '#485c7b'\n        },\n        timeScale: {\n          borderColor: '#485c7b',\n          timeVisible: true,\n          secondsVisible: false\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: 500\n      });\n\n      // Add candlestick series with TradingView colors (green bullish, red bearish)\n      candlestickSeries.current = chart.current.addCandlestickSeries({\n        upColor: '#26a69a',\n        // Green for bullish candles (TradingView style)\n        downColor: '#ef5350',\n        // Red for bearish candles (TradingView style)\n        borderDownColor: '#ef5350',\n        // Red border for bearish\n        borderUpColor: '#26a69a',\n        // Green border for bullish\n        wickDownColor: '#ef5350',\n        // Red wicks for bearish\n        wickUpColor: '#26a69a',\n        // Green wicks for bullish\n        priceLineVisible: false // Hide price line for cleaner look\n      });\n\n      // Add volume series\n      volumeSeries.current = chart.current.addHistogramSeries({\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume'\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0\n        }\n      });\n      console.log('AdvancedChart initialized successfully');\n    } catch (error) {\n      console.error('Error initializing AdvancedChart:', error);\n    }\n    return () => {\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, []);\n\n  // Use marketData prop directly and listen for real-time updates\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      console.log(`📊 AdvancedChart: Received ${marketData.length} data points for ${symbol}`);\n      setChartData(marketData);\n    }\n\n    // Listen for real-time market data updates\n    const handleMarketDataUpdate = event => {\n      const {\n        symbol: updateSymbol,\n        candlestick\n      } = event.detail;\n\n      // Only update if this chart is showing the same symbol\n      if (updateSymbol === symbol && candlestickSeries.current) {\n        try {\n          // Update the chart with new candlestick data\n          candlestickSeries.current.update(candlestick);\n\n          // Update volume if available\n          if (volumeSeries.current && candlestick.volume) {\n            volumeSeries.current.update({\n              time: candlestick.time,\n              value: candlestick.volume,\n              color: candlestick.close >= candlestick.open ? '#26a69a44' : '#ef535044'\n            });\n          }\n          console.log('📊 AdvancedChart real-time update:', updateSymbol, '@', candlestick.close);\n          setCurrentPrice(candlestick.close);\n          setLastUpdate(new Date());\n          setIsUpdating(true);\n          setTimeout(() => setIsUpdating(false), 500);\n        } catch (error) {\n          console.error('Error updating AdvancedChart with real-time data:', error);\n        }\n      }\n    };\n\n    // Add event listener for real-time updates\n    window.addEventListener('marketDataUpdate', handleMarketDataUpdate);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('marketDataUpdate', handleMarketDataUpdate);\n    };\n  }, [marketData, symbol]);\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (chart.current) {\n        chart.current.applyOptions({\n          width: chartContainerRef.current.clientWidth\n        });\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Real-time updates from live data\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      const latest = marketData[marketData.length - 1];\n      if (latest && typeof latest.close === 'number') {\n        setCurrentPrice(latest.close);\n        if (marketData.length > 1) {\n          const previous = marketData[marketData.length - 2];\n          if (previous && typeof previous.close === 'number') {\n            setPriceChange((latest.close - previous.close) / previous.close * 100);\n          }\n        }\n\n        // Real-time chart update with proper data deduplication and ordering\n        if (candlestickSeries.current && volumeSeries.current) {\n          try {\n            // Process and deduplicate data\n            const processedData = [...marketData].map(item => ({\n              time: Math.floor(new Date(item.timestamp).getTime() / 1000),\n              open: parseFloat(item.open),\n              high: parseFloat(item.high),\n              low: parseFloat(item.low),\n              close: parseFloat(item.close),\n              volume: parseFloat(item.volume)\n            })).filter(item => !isNaN(item.time) && !isNaN(item.close)) // Remove invalid data\n            .sort((a, b) => a.time - b.time); // Sort by time ascending\n\n            // Remove duplicates by keeping the latest data for each timestamp\n            const uniqueData = [];\n            const timeMap = new Map();\n            processedData.forEach(item => {\n              if (!timeMap.has(item.time) || timeMap.get(item.time).close !== item.close) {\n                timeMap.set(item.time, item);\n              }\n            });\n\n            // Convert map back to array and sort again\n            const finalData = Array.from(timeMap.values()).sort((a, b) => a.time - b.time);\n\n            // Prepare candlestick data\n            const candleData = finalData.map(item => ({\n              time: item.time,\n              open: item.open,\n              high: item.high,\n              low: item.low,\n              close: item.close\n            }));\n\n            // Prepare volume data\n            const volumeData = finalData.map(item => ({\n              time: item.time,\n              value: item.volume,\n              color: item.close >= item.open ? '#4bffb544' : '#ff497644'\n            }));\n\n            // Update charts with clean, ordered data\n            candlestickSeries.current.setData(candleData);\n            volumeSeries.current.setData(volumeData);\n            console.log(`📊 AdvancedChart real-time update: ${latest.close} (${finalData.length} unique points)`);\n            setLastUpdate(new Date());\n            setIsUpdating(true);\n            setTimeout(() => setIsUpdating(false), 500);\n          } catch (error) {\n            console.error('Error updating AdvancedChart in real-time:', error);\n          }\n        }\n      }\n    }\n  }, [marketData]);\n\n  // Update chart data and add technical indicators\n  useEffect(() => {\n    if (!candlestickSeries.current || !volumeSeries.current || !Array.isArray(chartData) || chartData.length === 0) {\n      return;\n    }\n    try {\n      setIsUpdating(true);\n      setLastUpdate(new Date());\n      const formattedData = chartData.filter(item => item && item.timestamp && typeof item.open === 'number').map(item => ({\n        time: new Date(item.timestamp).getTime() / 1000,\n        open: item.open,\n        high: item.high,\n        low: item.low,\n        close: item.close\n      })).sort((a, b) => a.time - b.time) // Sort by time ascending\n      .filter((item, index, array) => {\n        // Remove duplicate timestamps\n        return index === 0 || item.time !== array[index - 1].time;\n      });\n      const volumeData = chartData.filter(item => item && item.timestamp && typeof item.volume === 'number').map(item => ({\n        time: new Date(item.timestamp).getTime() / 1000,\n        value: item.volume,\n        color: item.close >= item.open ? '#4bffb544' : '#ff497644'\n      })).sort((a, b) => a.time - b.time) // Sort by time ascending\n      .filter((item, index, array) => {\n        // Remove duplicate timestamps\n        return index === 0 || item.time !== array[index - 1].time;\n      });\n      console.log('📊 AdvancedChart updated with', formattedData.length, 'candles');\n      if (formattedData.length > 0) {\n        candlestickSeries.current.setData(formattedData);\n      }\n      if (volumeData.length > 0) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // Add technical indicators\n      addTechnicalIndicators(formattedData);\n      setTimeout(() => setIsUpdating(false), 500);\n    } catch (error) {\n      console.error('Error updating AdvancedChart data:', error);\n      setIsUpdating(false);\n    }\n  }, [chartData, activeIndicators]);\n\n  // Add technical indicators to chart\n  const addTechnicalIndicators = candleData => {\n    if (!chart.current || candleData.length < 50) return;\n\n    // Clear existing indicator series\n    Object.values(indicatorSeries.current).forEach(series => {\n      if (series) {\n        chart.current.removeSeries(series);\n      }\n    });\n    indicatorSeries.current = {};\n    const closes = candleData.map(d => d.close);\n    const highs = candleData.map(d => d.high);\n    const lows = candleData.map(d => d.low);\n\n    // Add SMA indicators\n    if (activeIndicators.includes('SMA20')) {\n      const sma20Data = calculateSMA(closes, 20);\n      const sma20Series = chart.current.addLineSeries({\n        color: '#ffa726',\n        lineWidth: 2,\n        title: 'SMA 20'\n      });\n      const sma20FormattedData = sma20Data.map((value, index) => value !== null ? {\n        time: candleData[index].time,\n        value\n      } : null).filter(item => item !== null);\n      sma20Series.setData(sma20FormattedData);\n      indicatorSeries.current.SMA20 = sma20Series;\n    }\n    if (activeIndicators.includes('SMA50')) {\n      const sma50Data = calculateSMA(closes, 50);\n      const sma50Series = chart.current.addLineSeries({\n        color: '#9c27b0',\n        lineWidth: 2,\n        title: 'SMA 50'\n      });\n      const sma50FormattedData = sma50Data.map((value, index) => value !== null ? {\n        time: candleData[index].time,\n        value\n      } : null).filter(item => item !== null);\n      sma50Series.setData(sma50FormattedData);\n      indicatorSeries.current.SMA50 = sma50Series;\n    }\n\n    // Add EMA indicators\n    if (activeIndicators.includes('EMA12')) {\n      const ema12Data = calculateEMA(closes, 12);\n      const ema12Series = chart.current.addLineSeries({\n        color: '#2196f3',\n        lineWidth: 2,\n        title: 'EMA 12'\n      });\n      const ema12FormattedData = ema12Data.map((value, index) => ({\n        time: candleData[index].time,\n        value\n      }));\n      ema12Series.setData(ema12FormattedData);\n      indicatorSeries.current.EMA12 = ema12Series;\n    }\n    if (activeIndicators.includes('EMA26')) {\n      const ema26Data = calculateEMA(closes, 26);\n      const ema26Series = chart.current.addLineSeries({\n        color: '#ff5722',\n        lineWidth: 2,\n        title: 'EMA 26'\n      });\n      const ema26FormattedData = ema26Data.map((value, index) => ({\n        time: candleData[index].time,\n        value\n      }));\n      ema26Series.setData(ema26FormattedData);\n      indicatorSeries.current.EMA26 = ema26Series;\n    }\n\n    // Add Bollinger Bands\n    if (activeIndicators.includes('BB')) {\n      const bbData = calculateBollingerBands(closes, 20, 2);\n      const bbUpperSeries = chart.current.addLineSeries({\n        color: '#e91e63',\n        lineWidth: 1,\n        title: 'BB Upper'\n      });\n      const bbLowerSeries = chart.current.addLineSeries({\n        color: '#e91e63',\n        lineWidth: 1,\n        title: 'BB Lower'\n      });\n      const bbUpperData = bbData.upper.map((value, index) => value !== null ? {\n        time: candleData[index].time,\n        value\n      } : null).filter(item => item !== null);\n      const bbLowerData = bbData.lower.map((value, index) => value !== null ? {\n        time: candleData[index].time,\n        value\n      } : null).filter(item => item !== null);\n      bbUpperSeries.setData(bbUpperData);\n      bbLowerSeries.setData(bbLowerData);\n      indicatorSeries.current.BBUpper = bbUpperSeries;\n      indicatorSeries.current.BBLower = bbLowerSeries;\n    }\n  };\n  const calculateSMA = (data, period) => {\n    const sma = [];\n    for (let i = 0; i < data.length; i++) {\n      if (i < period - 1) {\n        sma.push(null);\n      } else {\n        const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);\n        sma.push(sum / period);\n      }\n    }\n    return sma;\n  };\n  const calculateEMA = (data, period) => {\n    const ema = [];\n    const multiplier = 2 / (period + 1);\n    for (let i = 0; i < data.length; i++) {\n      if (i === 0) {\n        ema.push(data[i]);\n      } else {\n        ema.push(data[i] * multiplier + ema[i - 1] * (1 - multiplier));\n      }\n    }\n    return ema;\n  };\n  const calculateBollingerBands = (data, period, stdDev) => {\n    const sma = calculateSMA(data, period);\n    const upper = [];\n    const lower = [];\n    for (let i = 0; i < data.length; i++) {\n      if (i < period - 1) {\n        upper.push(null);\n        lower.push(null);\n      } else {\n        const slice = data.slice(i - period + 1, i + 1);\n        const mean = sma[i];\n        const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;\n        const standardDeviation = Math.sqrt(variance);\n        upper.push(mean + standardDeviation * stdDev);\n        lower.push(mean - standardDeviation * stdDev);\n      }\n    }\n    return {\n      upper,\n      middle: sma,\n      lower\n    };\n  };\n\n  // Add signal markers\n  useEffect(() => {\n    if (!candlestickSeries.current || !Array.isArray(signals) || signals.length === 0) return;\n    try {\n      const markers = signals.filter(signal => signal && signal.signal && signal.signal !== 'HOLD' && signal.timestamp).map(signal => {\n        const isBuy = signal.signal === 'BUY';\n        return {\n          time: new Date(signal.timestamp).getTime() / 1000,\n          position: isBuy ? 'belowBar' : 'aboveBar',\n          color: isBuy ? '#4bffb5' : '#ff4976',\n          shape: isBuy ? 'arrowUp' : 'arrowDown',\n          text: `${signal.signal} ${signal.price ? `@$${signal.price.toFixed(4)}` : ''} (${((signal.confidence || 0) * 100).toFixed(1)}%)`,\n          size: 2\n        };\n      });\n      candlestickSeries.current.setMarkers(markers);\n      console.log('📍 AdvancedChart updated markers:', markers.length, 'total markers');\n    } catch (error) {\n      console.error('Error setting AdvancedChart signal markers:', error);\n    }\n  }, [signals]);\n  const toggleIndicator = indicatorId => {\n    setActiveIndicators(prev => prev.includes(indicatorId) ? prev.filter(id => id !== indicatorId) : [...prev, indicatorId]);\n  };\n  const handleTimeframeChange = newTimeframe => {\n    console.log(`🕐 AdvancedChart: Switching timeframe from ${timeframe} to ${newTimeframe} for ${symbol}`);\n    setTimeframe(newTimeframe);\n    // Clear current data to show loading state\n    setChartData([]);\n    // Fetch new data for the timeframe with force refresh\n    fetchMarketData(newTimeframe, true);\n  };\n  const fetchMarketData = async (timeframe, forceRefresh = false) => {\n    setLoading(true);\n    try {\n      console.log(`📊 AdvancedChart: Fetching ${timeframe} data for ${symbol}${forceRefresh ? ' (force refresh)' : ''}`);\n\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const cachedData = getCachedData(symbol, timeframe);\n        if (cachedData && cachedData.length > 0) {\n          setChartData(cachedData);\n          setLoading(false);\n          return;\n        }\n      }\n\n      // First try to get historical data for better chart initialization\n      const pairSymbol = symbol.replace('/', ''); // Convert \"XRP/USD\" to \"XRPUSD\"\n      const historicalResponse = await fetch(`http://localhost:8000/api/crypto-pairs/${pairSymbol}/historical?timeframe=${timeframe}&limit=1000`);\n      let historicalData = [];\n      if (historicalResponse.ok) {\n        const histResult = await historicalResponse.json();\n        if (histResult.success && histResult.data.length > 0) {\n          historicalData = histResult.data;\n          console.log(`📈 AdvancedChart: Retrieved ${historicalData.length} historical ${timeframe} candles for ${symbol}`);\n        }\n      }\n\n      // Then get live market data to combine with historical\n      const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n      const liveResponse = await fetch(`http://localhost:8000/market-data?limit=300&timeframe=${timeframe}${pairParam}`);\n      let liveData = [];\n      if (liveResponse.ok) {\n        liveData = await liveResponse.json();\n        console.log(`📡 AdvancedChart: Retrieved ${liveData.length} live ${timeframe} data points for ${symbol}`);\n      }\n\n      // Combine historical and live data, removing duplicates\n      const combinedData = [...historicalData, ...liveData];\n      const uniqueData = [];\n      const seenTimestamps = new Set();\n\n      // Sort by timestamp and remove duplicates (keep latest)\n      combinedData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)).forEach(item => {\n        const timestamp = new Date(item.timestamp).getTime();\n        if (!seenTimestamps.has(timestamp)) {\n          seenTimestamps.add(timestamp);\n          uniqueData.push(item);\n        }\n      });\n      console.log(`📊 AdvancedChart: Combined data: ${uniqueData.length} unique ${timeframe} candles for ${symbol}`);\n      setChartData(uniqueData);\n\n      // Cache the combined data for faster future access\n      if (uniqueData.length > 0) {\n        setCachedData(symbol, timeframe, uniqueData);\n      }\n    } catch (error) {\n      console.error('Error fetching AdvancedChart data:', error);\n      // Fallback to live data only if historical fails\n      try {\n        const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n        const response = await fetch(`http://localhost:8000/market-data?limit=300&timeframe=${timeframe}${pairParam}`);\n        if (response.ok) {\n          const data = await response.json();\n          console.log(`📊 AdvancedChart Fallback: ${data.length} ${timeframe} data points for ${symbol}`);\n          setChartData(data);\n        }\n      } catch (fallbackError) {\n        console.error('AdvancedChart fallback data fetch failed:', fallbackError);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ChartContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ChartHeader, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: [\"\\uD83D\\uDCCA \", symbol, \" Advanced Chart\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '8px',\n              height: '8px',\n              borderRadius: '50%',\n              backgroundColor: isUpdating ? '#4bffb5' : '#666',\n              animation: isUpdating ? 'pulse 1s infinite' : 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px',\n              color: '#888',\n              fontFamily: 'monospace'\n            },\n            children: lastUpdate ? `Last: ${lastUpdate.toLocaleTimeString()}` : 'Waiting...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlsContainer, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#888',\n              fontSize: '11px'\n            },\n            children: \"Timeframe:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this), timeframes.map(tf => /*#__PURE__*/_jsxDEV(TimeframeButton, {\n            $active: timeframe === tf.value,\n            onClick: () => handleTimeframeChange(tf.value),\n            children: tf.label\n          }, tf.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#888',\n              fontSize: '11px'\n            },\n            children: \"Indicators:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this), availableIndicators.slice(0, 6).map(indicator => /*#__PURE__*/_jsxDEV(IndicatorButton, {\n            $active: activeIndicators.includes(indicator.id),\n            onClick: () => toggleIndicator(indicator.id),\n            title: indicator.name,\n            children: indicator.id\n          }, indicator.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChartArea, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: '100%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 9\n      }, this), activeIndicators.length > 0 && /*#__PURE__*/_jsxDEV(IndicatorInfo, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '4px',\n            color: '#4bffb5'\n          },\n          children: [\"Active Indicators (\", activeIndicators.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: activeIndicators.map(id => {\n            const indicator = availableIndicators.find(ind => ind.id === id);\n            return indicator ? /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'inline-block',\n                marginRight: '8px',\n                color: indicator.color\n              },\n              children: indicator.name\n            }, id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 19\n            }, this) : null;\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 776,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          background: 'rgba(0, 0, 0, 0.8)',\n          color: '#fff',\n          padding: '12px 24px',\n          borderRadius: '8px',\n          fontSize: '14px',\n          zIndex: 10\n        },\n        children: [\"Loading \", timeframe, \" data...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatusBar, {\n      children: [/*#__PURE__*/_jsxDEV(StatusItem, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDE80 TradingView Advanced Chart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#4bffb5'\n          },\n          children: \"\\u2022 Fully Interactive\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 812,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusItem, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Current: $\", (currentPrice === null || currentPrice === void 0 ? void 0 : currentPrice.toFixed(4)) || '0.0000']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: priceChange >= 0 ? '#4bffb5' : '#ff4976'\n          },\n          children: [priceChange >= 0 ? '+' : '', priceChange.toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusItem, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCC8 \", chartData.length, \" candles \\u2022 \", activeIndicators.length, \" indicators\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 811,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 716,\n    columnNumber: 5\n  }, this);\n};\n_s(AdvancedChart, \"k9qhliziazvS9KCMlYm8Nr1b96g=\");\n_c1 = AdvancedChart;\nexport default AdvancedChart;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"ChartContainer\");\n$RefreshReg$(_c2, \"ChartHeader\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"ControlsContainer\");\n$RefreshReg$(_c5, \"TimeframeButton\");\n$RefreshReg$(_c6, \"IndicatorButton\");\n$RefreshReg$(_c7, \"ChartArea\");\n$RefreshReg$(_c8, \"IndicatorInfo\");\n$RefreshReg$(_c9, \"StatusBar\");\n$RefreshReg$(_c0, \"StatusItem\");\n$RefreshReg$(_c1, \"AdvancedChart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "styled", "createChart", "ColorType", "jsxDEV", "_jsxDEV", "advancedDataCache", "Map", "CACHE_DURATION", "get<PERSON><PERSON><PERSON><PERSON>", "symbol", "timeframe", "getCachedData", "key", "cached", "get", "Date", "now", "timestamp", "console", "log", "data", "setCachedData", "set", "length", "ChartContainer", "div", "_c", "ChartHeader", "_c2", "Title", "h3", "_c3", "ControlsContainer", "_c4", "TimeframeButton", "button", "props", "$active", "_c5", "Indicator<PERSON><PERSON><PERSON>", "_c6", "ChartArea", "_c7", "IndicatorInfo", "_c8", "StatusBar", "_c9", "StatusItem", "_c0", "AdvancedChart", "marketData", "signals", "_s", "chartContainerRef", "chart", "candlestickSeries", "volumeSeries", "indicatorSeries", "setTimeframe", "activeIndicators", "setActiveIndicators", "chartData", "setChartData", "loading", "setLoading", "lastUpdate", "setLastUpdate", "isUpdating", "setIsUpdating", "currentPrice", "setCurrentPrice", "priceChange", "setPriceChange", "timeframes", "value", "label", "availableIndicators", "id", "name", "color", "current", "layout", "background", "type", "Solid", "textColor", "grid", "vertLines", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "width", "clientWidth", "height", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "priceLineVisible", "addHistogramSeries", "priceFormat", "priceScaleId", "scale<PERSON>argins", "top", "bottom", "error", "remove", "Array", "isArray", "handleMarketDataUpdate", "event", "updateSymbol", "candlestick", "detail", "update", "volume", "time", "close", "open", "setTimeout", "window", "addEventListener", "removeEventListener", "handleResize", "applyOptions", "latest", "previous", "processedData", "map", "item", "Math", "floor", "getTime", "parseFloat", "high", "low", "filter", "isNaN", "sort", "a", "b", "uniqueData", "timeMap", "for<PERSON>ach", "has", "finalData", "from", "values", "candleData", "volumeData", "setData", "formattedData", "index", "array", "addTechnicalIndicators", "Object", "series", "removeSeries", "closes", "d", "highs", "lows", "includes", "sma20Data", "calculateSMA", "sma20Series", "addLineSeries", "lineWidth", "title", "sma20FormattedData", "SMA20", "sma50Data", "sma50Series", "sma50FormattedData", "SMA50", "ema12Data", "calculateEMA", "ema12Series", "ema12FormattedData", "EMA12", "ema26Data", "ema26Series", "ema26FormattedData", "EMA26", "bbData", "calculateBollingerBands", "bbUpperSeries", "bbLowerSeries", "bbUpperData", "upper", "bbLowerData", "lower", "BBUpper", "BBLower", "period", "sma", "i", "push", "sum", "slice", "reduce", "ema", "multiplier", "stdDev", "mean", "variance", "val", "pow", "standardDeviation", "sqrt", "middle", "markers", "signal", "isBuy", "position", "shape", "text", "price", "toFixed", "confidence", "size", "setMarkers", "toggleIndicator", "indicatorId", "prev", "handleTimeframeChange", "newTimeframe", "fetchMarketData", "forceRefresh", "cachedData", "pairSymbol", "replace", "historicalResponse", "fetch", "historicalData", "ok", "histResult", "json", "success", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "liveResponse", "liveData", "combinedData", "seenTimestamps", "Set", "add", "response", "fallback<PERSON><PERSON>r", "children", "style", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderRadius", "backgroundColor", "animation", "fontSize", "fontFamily", "toLocaleTimeString", "tf", "onClick", "indicator", "ref", "fontWeight", "marginBottom", "find", "ind", "marginRight", "left", "transform", "padding", "zIndex", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/AdvancedChart.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport styled from 'styled-components';\nimport { createChart, ColorType } from 'lightweight-charts';\n\n// Simple cache for market data to speed up chart loading\nconst advancedDataCache = new Map();\nconst CACHE_DURATION = 30000; // 30 seconds\n\nconst getCacheKey = (symbol, timeframe) => `advanced-${symbol}-${timeframe}`;\n\nconst getCachedData = (symbol, timeframe) => {\n  const key = getCacheKey(symbol, timeframe);\n  const cached = advancedDataCache.get(key);\n  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n    console.log(`📦 AdvancedChart: Using cached data for ${symbol} ${timeframe}`);\n    return cached.data;\n  }\n  return null;\n};\n\nconst setCachedData = (symbol, timeframe, data) => {\n  const key = getCacheKey(symbol, timeframe);\n  advancedDataCache.set(key, {\n    data,\n    timestamp: Date.now()\n  });\n  console.log(`💾 AdvancedChart: Cached data for ${symbol} ${timeframe}: ${data.length} points`);\n};\n\nconst ChartContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #333;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst ChartHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n  gap: 12px;\n`;\n\nconst Title = styled.h3`\n  color: #4bffb5;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n`;\n\nconst ControlsContainer = styled.div`\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  align-items: center;\n`;\n\nconst TimeframeButton = styled.button`\n  background: ${props => props.$active ? '#4bffb5' : '#333'};\n  color: ${props => props.$active ? '#000' : '#ccc'};\n  border: none;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#3de89f' : '#444'};\n  }\n`;\n\nconst IndicatorButton = styled.button`\n  background: ${props => props.$active ? '#ff4976' : '#333'};\n  color: ${props => props.$active ? '#fff' : '#ccc'};\n  border: none;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$active ? '#e63946' : '#444'};\n  }\n`;\n\nconst ChartArea = styled.div`\n  flex: 1;\n  position: relative;\n  height: 500px;\n  background: #0a0a0a;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n\nconst IndicatorInfo = styled.div`\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  background: rgba(26, 26, 26, 0.9);\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 11px;\n  color: #ccc;\n  border: 1px solid #333;\n  z-index: 10;\n  max-width: 300px;\n`;\n\nconst StatusBar = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background: rgba(26, 26, 26, 0.8);\n  border-radius: 6px;\n  margin-top: 8px;\n  font-size: 11px;\n  color: #888;\n`;\n\nconst StatusItem = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 4px;\n`;\n\nconst AdvancedChart = ({ marketData, signals, symbol = 'XRP-USD' }) => {\n  const chartContainerRef = useRef(null);\n  const chart = useRef(null);\n  const candlestickSeries = useRef(null);\n  const volumeSeries = useRef(null);\n  const indicatorSeries = useRef({});\n\n  const [timeframe, setTimeframe] = useState('1H');\n  const [activeIndicators, setActiveIndicators] = useState(['SMA20', 'SMA50', 'RSI', 'MACD']);\n  const [chartData, setChartData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [currentPrice, setCurrentPrice] = useState(0);\n  const [priceChange, setPriceChange] = useState(0);\n\n  const timeframes = [\n    { value: '1m', label: '1M' },\n    { value: '5m', label: '5M' },\n    { value: '15m', label: '15M' },\n    { value: '1h', label: '1H' },\n    { value: '4h', label: '4H' },\n    { value: '1d', label: '1D' }\n  ];\n\n  const availableIndicators = [\n    { id: 'SMA20', name: 'SMA 20', color: '#ffa726' },\n    { id: 'SMA50', name: 'SMA 50', color: '#9c27b0' },\n    { id: 'EMA12', name: 'EMA 12', color: '#2196f3' },\n    { id: 'EMA26', name: 'EMA 26', color: '#ff5722' },\n    { id: 'RSI', name: 'RSI', color: '#4caf50' },\n    { id: 'MACD', name: 'MACD', color: '#ff9800' },\n    { id: 'BB', name: 'Bollinger Bands', color: '#e91e63' },\n    { id: 'ATR', name: 'ATR', color: '#795548' },\n    { id: 'STOCH', name: 'Stochastic', color: '#607d8b' },\n    { id: 'ADX', name: 'ADX', color: '#3f51b5' }\n  ];\n\n  // Initialize chart\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    try {\n      console.log('Initializing AdvancedChart with TradingView...');\n\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          background: { type: ColorType.Solid, color: '#0a0a0a' },\n          textColor: '#ffffff',\n        },\n        grid: {\n          vertLines: { color: '#2B2B43' },\n          horzLines: { color: '#2B2B43' },\n        },\n        crosshair: {\n          mode: 1,\n        },\n        rightPriceScale: {\n          borderColor: '#485c7b',\n        },\n        timeScale: {\n          borderColor: '#485c7b',\n          timeVisible: true,\n          secondsVisible: false,\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: 500,\n      });\n\n      // Add candlestick series with TradingView colors (green bullish, red bearish)\n      candlestickSeries.current = chart.current.addCandlestickSeries({\n        upColor: '#26a69a',        // Green for bullish candles (TradingView style)\n        downColor: '#ef5350',      // Red for bearish candles (TradingView style)\n        borderDownColor: '#ef5350', // Red border for bearish\n        borderUpColor: '#26a69a',   // Green border for bullish\n        wickDownColor: '#ef5350',   // Red wicks for bearish\n        wickUpColor: '#26a69a',     // Green wicks for bullish\n        priceLineVisible: false,    // Hide price line for cleaner look\n      });\n\n      // Add volume series\n      volumeSeries.current = chart.current.addHistogramSeries({\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume',\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0,\n        },\n      });\n\n      console.log('AdvancedChart initialized successfully');\n\n    } catch (error) {\n      console.error('Error initializing AdvancedChart:', error);\n    }\n\n    return () => {\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, []);\n\n  // Use marketData prop directly and listen for real-time updates\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      console.log(`📊 AdvancedChart: Received ${marketData.length} data points for ${symbol}`);\n      setChartData(marketData);\n    }\n\n    // Listen for real-time market data updates\n    const handleMarketDataUpdate = (event) => {\n      const { symbol: updateSymbol, candlestick } = event.detail;\n\n      // Only update if this chart is showing the same symbol\n      if (updateSymbol === symbol && candlestickSeries.current) {\n        try {\n          // Update the chart with new candlestick data\n          candlestickSeries.current.update(candlestick);\n\n          // Update volume if available\n          if (volumeSeries.current && candlestick.volume) {\n            volumeSeries.current.update({\n              time: candlestick.time,\n              value: candlestick.volume,\n              color: candlestick.close >= candlestick.open ? '#26a69a44' : '#ef535044'\n            });\n          }\n\n          console.log('📊 AdvancedChart real-time update:', updateSymbol, '@', candlestick.close);\n          setCurrentPrice(candlestick.close);\n          setLastUpdate(new Date());\n          setIsUpdating(true);\n          setTimeout(() => setIsUpdating(false), 500);\n        } catch (error) {\n          console.error('Error updating AdvancedChart with real-time data:', error);\n        }\n      }\n    };\n\n    // Add event listener for real-time updates\n    window.addEventListener('marketDataUpdate', handleMarketDataUpdate);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('marketDataUpdate', handleMarketDataUpdate);\n    };\n  }, [marketData, symbol]);\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (chart.current) {\n        chart.current.applyOptions({\n          width: chartContainerRef.current.clientWidth,\n        });\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Real-time updates from live data\n  useEffect(() => {\n    if (Array.isArray(marketData) && marketData.length > 0) {\n      const latest = marketData[marketData.length - 1];\n      if (latest && typeof latest.close === 'number') {\n        setCurrentPrice(latest.close);\n        if (marketData.length > 1) {\n          const previous = marketData[marketData.length - 2];\n          if (previous && typeof previous.close === 'number') {\n            setPriceChange(((latest.close - previous.close) / previous.close) * 100);\n          }\n        }\n\n        // Real-time chart update with proper data deduplication and ordering\n        if (candlestickSeries.current && volumeSeries.current) {\n          try {\n            // Process and deduplicate data\n            const processedData = [...marketData]\n              .map(item => ({\n                time: Math.floor(new Date(item.timestamp).getTime() / 1000),\n                open: parseFloat(item.open),\n                high: parseFloat(item.high),\n                low: parseFloat(item.low),\n                close: parseFloat(item.close),\n                volume: parseFloat(item.volume)\n              }))\n              .filter(item => !isNaN(item.time) && !isNaN(item.close)) // Remove invalid data\n              .sort((a, b) => a.time - b.time); // Sort by time ascending\n\n            // Remove duplicates by keeping the latest data for each timestamp\n            const uniqueData = [];\n            const timeMap = new Map();\n\n            processedData.forEach(item => {\n              if (!timeMap.has(item.time) || timeMap.get(item.time).close !== item.close) {\n                timeMap.set(item.time, item);\n              }\n            });\n\n            // Convert map back to array and sort again\n            const finalData = Array.from(timeMap.values()).sort((a, b) => a.time - b.time);\n\n            // Prepare candlestick data\n            const candleData = finalData.map(item => ({\n              time: item.time,\n              open: item.open,\n              high: item.high,\n              low: item.low,\n              close: item.close\n            }));\n\n            // Prepare volume data\n            const volumeData = finalData.map(item => ({\n              time: item.time,\n              value: item.volume,\n              color: item.close >= item.open ? '#4bffb544' : '#ff497644'\n            }));\n\n            // Update charts with clean, ordered data\n            candlestickSeries.current.setData(candleData);\n            volumeSeries.current.setData(volumeData);\n\n            console.log(`📊 AdvancedChart real-time update: ${latest.close} (${finalData.length} unique points)`);\n            setLastUpdate(new Date());\n            setIsUpdating(true);\n            setTimeout(() => setIsUpdating(false), 500);\n\n          } catch (error) {\n            console.error('Error updating AdvancedChart in real-time:', error);\n          }\n        }\n      }\n    }\n  }, [marketData]);\n\n  // Update chart data and add technical indicators\n  useEffect(() => {\n    if (!candlestickSeries.current || !volumeSeries.current || !Array.isArray(chartData) || chartData.length === 0) {\n      return;\n    }\n\n    try {\n      setIsUpdating(true);\n      setLastUpdate(new Date());\n\n      const formattedData = chartData\n        .filter(item => item && item.timestamp && typeof item.open === 'number')\n        .map(item => ({\n          time: new Date(item.timestamp).getTime() / 1000,\n          open: item.open,\n          high: item.high,\n          low: item.low,\n          close: item.close,\n        }))\n        .sort((a, b) => a.time - b.time) // Sort by time ascending\n        .filter((item, index, array) => {\n          // Remove duplicate timestamps\n          return index === 0 || item.time !== array[index - 1].time;\n        });\n\n      const volumeData = chartData\n        .filter(item => item && item.timestamp && typeof item.volume === 'number')\n        .map(item => ({\n          time: new Date(item.timestamp).getTime() / 1000,\n          value: item.volume,\n          color: item.close >= item.open ? '#4bffb544' : '#ff497644',\n        }))\n        .sort((a, b) => a.time - b.time) // Sort by time ascending\n        .filter((item, index, array) => {\n          // Remove duplicate timestamps\n          return index === 0 || item.time !== array[index - 1].time;\n        });\n\n      console.log('📊 AdvancedChart updated with', formattedData.length, 'candles');\n\n      if (formattedData.length > 0) {\n        candlestickSeries.current.setData(formattedData);\n      }\n\n      if (volumeData.length > 0) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // Add technical indicators\n      addTechnicalIndicators(formattedData);\n\n      setTimeout(() => setIsUpdating(false), 500);\n\n    } catch (error) {\n      console.error('Error updating AdvancedChart data:', error);\n      setIsUpdating(false);\n    }\n  }, [chartData, activeIndicators]);\n\n  // Add technical indicators to chart\n  const addTechnicalIndicators = (candleData) => {\n    if (!chart.current || candleData.length < 50) return;\n\n    // Clear existing indicator series\n    Object.values(indicatorSeries.current).forEach(series => {\n      if (series) {\n        chart.current.removeSeries(series);\n      }\n    });\n    indicatorSeries.current = {};\n\n    const closes = candleData.map(d => d.close);\n    const highs = candleData.map(d => d.high);\n    const lows = candleData.map(d => d.low);\n\n    // Add SMA indicators\n    if (activeIndicators.includes('SMA20')) {\n      const sma20Data = calculateSMA(closes, 20);\n      const sma20Series = chart.current.addLineSeries({\n        color: '#ffa726',\n        lineWidth: 2,\n        title: 'SMA 20',\n      });\n      const sma20FormattedData = sma20Data\n        .map((value, index) => value !== null ? { time: candleData[index].time, value } : null)\n        .filter(item => item !== null);\n      sma20Series.setData(sma20FormattedData);\n      indicatorSeries.current.SMA20 = sma20Series;\n    }\n\n    if (activeIndicators.includes('SMA50')) {\n      const sma50Data = calculateSMA(closes, 50);\n      const sma50Series = chart.current.addLineSeries({\n        color: '#9c27b0',\n        lineWidth: 2,\n        title: 'SMA 50',\n      });\n      const sma50FormattedData = sma50Data\n        .map((value, index) => value !== null ? { time: candleData[index].time, value } : null)\n        .filter(item => item !== null);\n      sma50Series.setData(sma50FormattedData);\n      indicatorSeries.current.SMA50 = sma50Series;\n    }\n\n    // Add EMA indicators\n    if (activeIndicators.includes('EMA12')) {\n      const ema12Data = calculateEMA(closes, 12);\n      const ema12Series = chart.current.addLineSeries({\n        color: '#2196f3',\n        lineWidth: 2,\n        title: 'EMA 12',\n      });\n      const ema12FormattedData = ema12Data\n        .map((value, index) => ({ time: candleData[index].time, value }));\n      ema12Series.setData(ema12FormattedData);\n      indicatorSeries.current.EMA12 = ema12Series;\n    }\n\n    if (activeIndicators.includes('EMA26')) {\n      const ema26Data = calculateEMA(closes, 26);\n      const ema26Series = chart.current.addLineSeries({\n        color: '#ff5722',\n        lineWidth: 2,\n        title: 'EMA 26',\n      });\n      const ema26FormattedData = ema26Data\n        .map((value, index) => ({ time: candleData[index].time, value }));\n      ema26Series.setData(ema26FormattedData);\n      indicatorSeries.current.EMA26 = ema26Series;\n    }\n\n    // Add Bollinger Bands\n    if (activeIndicators.includes('BB')) {\n      const bbData = calculateBollingerBands(closes, 20, 2);\n\n      const bbUpperSeries = chart.current.addLineSeries({\n        color: '#e91e63',\n        lineWidth: 1,\n        title: 'BB Upper',\n      });\n      const bbLowerSeries = chart.current.addLineSeries({\n        color: '#e91e63',\n        lineWidth: 1,\n        title: 'BB Lower',\n      });\n\n      const bbUpperData = bbData.upper\n        .map((value, index) => value !== null ? { time: candleData[index].time, value } : null)\n        .filter(item => item !== null);\n      const bbLowerData = bbData.lower\n        .map((value, index) => value !== null ? { time: candleData[index].time, value } : null)\n        .filter(item => item !== null);\n\n      bbUpperSeries.setData(bbUpperData);\n      bbLowerSeries.setData(bbLowerData);\n      indicatorSeries.current.BBUpper = bbUpperSeries;\n      indicatorSeries.current.BBLower = bbLowerSeries;\n    }\n  };\n\n  const calculateSMA = (data, period) => {\n    const sma = [];\n    for (let i = 0; i < data.length; i++) {\n      if (i < period - 1) {\n        sma.push(null);\n      } else {\n        const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);\n        sma.push(sum / period);\n      }\n    }\n    return sma;\n  };\n\n  const calculateEMA = (data, period) => {\n    const ema = [];\n    const multiplier = 2 / (period + 1);\n\n    for (let i = 0; i < data.length; i++) {\n      if (i === 0) {\n        ema.push(data[i]);\n      } else {\n        ema.push((data[i] * multiplier) + (ema[i - 1] * (1 - multiplier)));\n      }\n    }\n    return ema;\n  };\n\n  const calculateBollingerBands = (data, period, stdDev) => {\n    const sma = calculateSMA(data, period);\n    const upper = [];\n    const lower = [];\n\n    for (let i = 0; i < data.length; i++) {\n      if (i < period - 1) {\n        upper.push(null);\n        lower.push(null);\n      } else {\n        const slice = data.slice(i - period + 1, i + 1);\n        const mean = sma[i];\n        const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;\n        const standardDeviation = Math.sqrt(variance);\n\n        upper.push(mean + (standardDeviation * stdDev));\n        lower.push(mean - (standardDeviation * stdDev));\n      }\n    }\n\n    return { upper, middle: sma, lower };\n  };\n\n  // Add signal markers\n  useEffect(() => {\n    if (!candlestickSeries.current || !Array.isArray(signals) || signals.length === 0) return;\n\n    try {\n      const markers = signals\n        .filter(signal => signal && signal.signal && signal.signal !== 'HOLD' && signal.timestamp)\n        .map(signal => {\n          const isBuy = signal.signal === 'BUY';\n          return {\n            time: new Date(signal.timestamp).getTime() / 1000,\n            position: isBuy ? 'belowBar' : 'aboveBar',\n            color: isBuy ? '#4bffb5' : '#ff4976',\n            shape: isBuy ? 'arrowUp' : 'arrowDown',\n            text: `${signal.signal} ${signal.price ? `@$${signal.price.toFixed(4)}` : ''} (${((signal.confidence || 0) * 100).toFixed(1)}%)`,\n            size: 2,\n          };\n        });\n\n      candlestickSeries.current.setMarkers(markers);\n      console.log('📍 AdvancedChart updated markers:', markers.length, 'total markers');\n    } catch (error) {\n      console.error('Error setting AdvancedChart signal markers:', error);\n    }\n  }, [signals]);\n\n  const toggleIndicator = (indicatorId) => {\n    setActiveIndicators(prev =>\n      prev.includes(indicatorId)\n        ? prev.filter(id => id !== indicatorId)\n        : [...prev, indicatorId]\n    );\n  };\n\n  const handleTimeframeChange = (newTimeframe) => {\n    console.log(`🕐 AdvancedChart: Switching timeframe from ${timeframe} to ${newTimeframe} for ${symbol}`);\n    setTimeframe(newTimeframe);\n    // Clear current data to show loading state\n    setChartData([]);\n    // Fetch new data for the timeframe with force refresh\n    fetchMarketData(newTimeframe, true);\n  };\n\n  const fetchMarketData = async (timeframe, forceRefresh = false) => {\n    setLoading(true);\n    try {\n      console.log(`📊 AdvancedChart: Fetching ${timeframe} data for ${symbol}${forceRefresh ? ' (force refresh)' : ''}`);\n\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const cachedData = getCachedData(symbol, timeframe);\n        if (cachedData && cachedData.length > 0) {\n          setChartData(cachedData);\n          setLoading(false);\n          return;\n        }\n      }\n\n      // First try to get historical data for better chart initialization\n      const pairSymbol = symbol.replace('/', '');  // Convert \"XRP/USD\" to \"XRPUSD\"\n      const historicalResponse = await fetch(\n        `http://localhost:8000/api/crypto-pairs/${pairSymbol}/historical?timeframe=${timeframe}&limit=1000`\n      );\n\n      let historicalData = [];\n      if (historicalResponse.ok) {\n        const histResult = await historicalResponse.json();\n        if (histResult.success && histResult.data.length > 0) {\n          historicalData = histResult.data;\n          console.log(`📈 AdvancedChart: Retrieved ${historicalData.length} historical ${timeframe} candles for ${symbol}`);\n        }\n      }\n\n      // Then get live market data to combine with historical\n      const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n      const liveResponse = await fetch(\n        `http://localhost:8000/market-data?limit=300&timeframe=${timeframe}${pairParam}`\n      );\n\n      let liveData = [];\n      if (liveResponse.ok) {\n        liveData = await liveResponse.json();\n        console.log(`📡 AdvancedChart: Retrieved ${liveData.length} live ${timeframe} data points for ${symbol}`);\n      }\n\n      // Combine historical and live data, removing duplicates\n      const combinedData = [...historicalData, ...liveData];\n      const uniqueData = [];\n      const seenTimestamps = new Set();\n\n      // Sort by timestamp and remove duplicates (keep latest)\n      combinedData\n        .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))\n        .forEach(item => {\n          const timestamp = new Date(item.timestamp).getTime();\n          if (!seenTimestamps.has(timestamp)) {\n            seenTimestamps.add(timestamp);\n            uniqueData.push(item);\n          }\n        });\n\n      console.log(`📊 AdvancedChart: Combined data: ${uniqueData.length} unique ${timeframe} candles for ${symbol}`);\n      setChartData(uniqueData);\n\n      // Cache the combined data for faster future access\n      if (uniqueData.length > 0) {\n        setCachedData(symbol, timeframe, uniqueData);\n      }\n\n    } catch (error) {\n      console.error('Error fetching AdvancedChart data:', error);\n      // Fallback to live data only if historical fails\n      try {\n        const pairParam = symbol ? `&pair=${encodeURIComponent(symbol)}` : '';\n        const response = await fetch(`http://localhost:8000/market-data?limit=300&timeframe=${timeframe}${pairParam}`);\n        if (response.ok) {\n          const data = await response.json();\n          console.log(`📊 AdvancedChart Fallback: ${data.length} ${timeframe} data points for ${symbol}`);\n          setChartData(data);\n        }\n      } catch (fallbackError) {\n        console.error('AdvancedChart fallback data fetch failed:', fallbackError);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <ChartContainer>\n      <ChartHeader>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <Title>📊 {symbol} Advanced Chart</Title>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            <div style={{\n              width: '8px',\n              height: '8px',\n              borderRadius: '50%',\n              backgroundColor: isUpdating ? '#4bffb5' : '#666',\n              animation: isUpdating ? 'pulse 1s infinite' : 'none'\n            }} />\n            <span style={{\n              fontSize: '12px',\n              color: '#888',\n              fontFamily: 'monospace'\n            }}>\n              {lastUpdate ? `Last: ${lastUpdate.toLocaleTimeString()}` : 'Waiting...'}\n            </span>\n          </div>\n        </div>\n\n        <ControlsContainer>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            <span style={{ color: '#888', fontSize: '11px' }}>Timeframe:</span>\n            {timeframes.map(tf => (\n              <TimeframeButton\n                key={tf.value}\n                $active={timeframe === tf.value}\n                onClick={() => handleTimeframeChange(tf.value)}\n              >\n                {tf.label}\n              </TimeframeButton>\n            ))}\n          </div>\n\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            <span style={{ color: '#888', fontSize: '11px' }}>Indicators:</span>\n            {availableIndicators.slice(0, 6).map(indicator => (\n              <IndicatorButton\n                key={indicator.id}\n                $active={activeIndicators.includes(indicator.id)}\n                onClick={() => toggleIndicator(indicator.id)}\n                title={indicator.name}\n              >\n                {indicator.id}\n              </IndicatorButton>\n            ))}\n          </div>\n        </ControlsContainer>\n      </ChartHeader>\n\n      <ChartArea>\n        <div ref={chartContainerRef} style={{ width: '100%', height: '100%' }} />\n\n        {activeIndicators.length > 0 && (\n          <IndicatorInfo>\n            <div style={{ fontWeight: 'bold', marginBottom: '4px', color: '#4bffb5' }}>\n              Active Indicators ({activeIndicators.length})\n            </div>\n            <div style={{ fontSize: '10px', color: '#888' }}>\n              {activeIndicators.map(id => {\n                const indicator = availableIndicators.find(ind => ind.id === id);\n                return indicator ? (\n                  <span key={id} style={{\n                    display: 'inline-block',\n                    marginRight: '8px',\n                    color: indicator.color\n                  }}>\n                    {indicator.name}\n                  </span>\n                ) : null;\n              })}\n            </div>\n          </IndicatorInfo>\n        )}\n\n        {loading && (\n          <div style={{\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            background: 'rgba(0, 0, 0, 0.8)',\n            color: '#fff',\n            padding: '12px 24px',\n            borderRadius: '8px',\n            fontSize: '14px',\n            zIndex: 10\n          }}>\n            Loading {timeframe} data...\n          </div>\n        )}\n      </ChartArea>\n\n      <StatusBar>\n        <StatusItem>\n          <span>🚀 TradingView Advanced Chart</span>\n          <span style={{ color: '#4bffb5' }}>• Fully Interactive</span>\n        </StatusItem>\n        <StatusItem>\n          <span>Current: ${currentPrice?.toFixed(4) || '0.0000'}</span>\n          <span style={{ color: priceChange >= 0 ? '#4bffb5' : '#ff4976' }}>\n            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}%\n          </span>\n        </StatusItem>\n        <StatusItem>\n          <span>📈 {chartData.length} candles • {activeIndicators.length} indicators</span>\n        </StatusItem>\n      </StatusBar>\n    </ChartContainer>\n  );\n};\n\n\n\n\n\nexport default AdvancedChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,WAAW,EAAEC,SAAS,QAAQ,oBAAoB;;AAE3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACnC,MAAMC,cAAc,GAAG,KAAK,CAAC,CAAC;;AAE9B,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK,YAAYD,MAAM,IAAIC,SAAS,EAAE;AAE5E,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,SAAS,KAAK;EAC3C,MAAME,GAAG,GAAGJ,WAAW,CAACC,MAAM,EAAEC,SAAS,CAAC;EAC1C,MAAMG,MAAM,GAAGR,iBAAiB,CAACS,GAAG,CAACF,GAAG,CAAC;EACzC,IAAIC,MAAM,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACI,SAAS,GAAGV,cAAc,EAAE;IAC5DW,OAAO,CAACC,GAAG,CAAC,2CAA2CV,MAAM,IAAIC,SAAS,EAAE,CAAC;IAC7E,OAAOG,MAAM,CAACO,IAAI;EACpB;EACA,OAAO,IAAI;AACb,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACZ,MAAM,EAAEC,SAAS,EAAEU,IAAI,KAAK;EACjD,MAAMR,GAAG,GAAGJ,WAAW,CAACC,MAAM,EAAEC,SAAS,CAAC;EAC1CL,iBAAiB,CAACiB,GAAG,CAACV,GAAG,EAAE;IACzBQ,IAAI;IACJH,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC;EACtB,CAAC,CAAC;EACFE,OAAO,CAACC,GAAG,CAAC,qCAAqCV,MAAM,IAAIC,SAAS,KAAKU,IAAI,CAACG,MAAM,SAAS,CAAC;AAChG,CAAC;AAED,MAAMC,cAAc,GAAGxB,MAAM,CAACyB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,cAAc;AAUpB,MAAMG,WAAW,GAAG3B,MAAM,CAACyB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,WAAW;AASjB,MAAME,KAAK,GAAG7B,MAAM,CAAC8B,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,iBAAiB,GAAGhC,MAAM,CAACyB,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GALID,iBAAiB;AAOvB,MAAME,eAAe,GAAGlC,MAAM,CAACmC,MAAM;AACrC,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AAC3D,WAAWD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,MAAM,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AAC7D;AACA,CAAC;AAACC,GAAA,GAdIJ,eAAe;AAgBrB,MAAMK,eAAe,GAAGvC,MAAM,CAACmC,MAAM;AACrC,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AAC3D,WAAWD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,MAAM,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,OAAO,GAAG,SAAS,GAAG,MAAM;AAC7D;AACA,CAAC;AAACG,GAAA,GAdID,eAAe;AAgBrB,MAAME,SAAS,GAAGzC,MAAM,CAACyB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAPID,SAAS;AASf,MAAME,aAAa,GAAG3C,MAAM,CAACyB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAZID,aAAa;AAcnB,MAAME,SAAS,GAAG7C,MAAM,CAACyB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAVID,SAAS;AAYf,MAAME,UAAU,GAAG/C,MAAM,CAACyB,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GAJID,UAAU;AAMhB,MAAME,aAAa,GAAGA,CAAC;EAAEC,UAAU;EAAEC,OAAO;EAAE1C,MAAM,GAAG;AAAU,CAAC,KAAK;EAAA2C,EAAA;EACrE,MAAMC,iBAAiB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMuD,KAAK,GAAGvD,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMwD,iBAAiB,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMyD,YAAY,GAAGzD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM0D,eAAe,GAAG1D,MAAM,CAAC,CAAC,CAAC,CAAC;EAElC,MAAM,CAACW,SAAS,EAAEgD,YAAY,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;EAC3F,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkE,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAM4E,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,CAC7B;EAED,MAAMC,mBAAmB,GAAG,CAC1B;IAAEC,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5C;IAAEF,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9C;IAAEF,EAAE,EAAE,IAAI;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACvD;IAAEF,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5C;IAAEF,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrD;IAAEF,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC7C;;EAED;EACAjF,SAAS,CAAC,MAAM;IACd,IAAI,CAACuD,iBAAiB,CAAC2B,OAAO,EAAE;IAEhC,IAAI;MACF9D,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7DmC,KAAK,CAAC0B,OAAO,GAAG/E,WAAW,CAACoD,iBAAiB,CAAC2B,OAAO,EAAE;QACrDC,MAAM,EAAE;UACNC,UAAU,EAAE;YAAEC,IAAI,EAAEjF,SAAS,CAACkF,KAAK;YAAEL,KAAK,EAAE;UAAU,CAAC;UACvDM,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAE;UACJC,SAAS,EAAE;YAAER,KAAK,EAAE;UAAU,CAAC;UAC/BS,SAAS,EAAE;YAAET,KAAK,EAAE;UAAU;QAChC,CAAC;QACDU,SAAS,EAAE;UACTC,IAAI,EAAE;QACR,CAAC;QACDC,eAAe,EAAE;UACfC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTD,WAAW,EAAE,SAAS;UACtBE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE;QAClB,CAAC;QACDC,KAAK,EAAE3C,iBAAiB,CAAC2B,OAAO,CAACiB,WAAW;QAC5CC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA3C,iBAAiB,CAACyB,OAAO,GAAG1B,KAAK,CAAC0B,OAAO,CAACmB,oBAAoB,CAAC;QAC7DC,OAAO,EAAE,SAAS;QAAS;QAC3BC,SAAS,EAAE,SAAS;QAAO;QAC3BC,eAAe,EAAE,SAAS;QAAE;QAC5BC,aAAa,EAAE,SAAS;QAAI;QAC5BC,aAAa,EAAE,SAAS;QAAI;QAC5BC,WAAW,EAAE,SAAS;QAAM;QAC5BC,gBAAgB,EAAE,KAAK,CAAK;MAC9B,CAAC,CAAC;;MAEF;MACAlD,YAAY,CAACwB,OAAO,GAAG1B,KAAK,CAAC0B,OAAO,CAAC2B,kBAAkB,CAAC;QACtD5B,KAAK,EAAE,SAAS;QAChB6B,WAAW,EAAE;UACXzB,IAAI,EAAE;QACR,CAAC;QACD0B,YAAY,EAAE,QAAQ;QACtBC,YAAY,EAAE;UACZC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE;QACV;MACF,CAAC,CAAC;MAEF9F,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAEvD,CAAC,CAAC,OAAO8F,KAAK,EAAE;MACd/F,OAAO,CAAC+F,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;IAEA,OAAO,MAAM;MACX,IAAI3D,KAAK,CAAC0B,OAAO,EAAE;QACjB1B,KAAK,CAAC0B,OAAO,CAACkC,MAAM,CAAC,CAAC;MACxB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApH,SAAS,CAAC,MAAM;IACd,IAAIqH,KAAK,CAACC,OAAO,CAAClE,UAAU,CAAC,IAAIA,UAAU,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACtDL,OAAO,CAACC,GAAG,CAAC,8BAA8B+B,UAAU,CAAC3B,MAAM,oBAAoBd,MAAM,EAAE,CAAC;MACxFqD,YAAY,CAACZ,UAAU,CAAC;IAC1B;;IAEA;IACA,MAAMmE,sBAAsB,GAAIC,KAAK,IAAK;MACxC,MAAM;QAAE7G,MAAM,EAAE8G,YAAY;QAAEC;MAAY,CAAC,GAAGF,KAAK,CAACG,MAAM;;MAE1D;MACA,IAAIF,YAAY,KAAK9G,MAAM,IAAI8C,iBAAiB,CAACyB,OAAO,EAAE;QACxD,IAAI;UACF;UACAzB,iBAAiB,CAACyB,OAAO,CAAC0C,MAAM,CAACF,WAAW,CAAC;;UAE7C;UACA,IAAIhE,YAAY,CAACwB,OAAO,IAAIwC,WAAW,CAACG,MAAM,EAAE;YAC9CnE,YAAY,CAACwB,OAAO,CAAC0C,MAAM,CAAC;cAC1BE,IAAI,EAAEJ,WAAW,CAACI,IAAI;cACtBlD,KAAK,EAAE8C,WAAW,CAACG,MAAM;cACzB5C,KAAK,EAAEyC,WAAW,CAACK,KAAK,IAAIL,WAAW,CAACM,IAAI,GAAG,WAAW,GAAG;YAC/D,CAAC,CAAC;UACJ;UAEA5G,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoG,YAAY,EAAE,GAAG,EAAEC,WAAW,CAACK,KAAK,CAAC;UACvFvD,eAAe,CAACkD,WAAW,CAACK,KAAK,CAAC;UAClC3D,aAAa,CAAC,IAAInD,IAAI,CAAC,CAAC,CAAC;UACzBqD,aAAa,CAAC,IAAI,CAAC;UACnB2D,UAAU,CAAC,MAAM3D,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;QAC7C,CAAC,CAAC,OAAO6C,KAAK,EAAE;UACd/F,OAAO,CAAC+F,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QAC3E;MACF;IACF,CAAC;;IAED;IACAe,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEZ,sBAAsB,CAAC;;IAEnE;IACA,OAAO,MAAM;MACXW,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAEb,sBAAsB,CAAC;IACxE,CAAC;EACH,CAAC,EAAE,CAACnE,UAAU,EAAEzC,MAAM,CAAC,CAAC;;EAExB;EACAX,SAAS,CAAC,MAAM;IACd,MAAMqI,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI7E,KAAK,CAAC0B,OAAO,EAAE;QACjB1B,KAAK,CAAC0B,OAAO,CAACoD,YAAY,CAAC;UACzBpC,KAAK,EAAE3C,iBAAiB,CAAC2B,OAAO,CAACiB;QACnC,CAAC,CAAC;MACJ;IACF,CAAC;IAED+B,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEE,YAAY,CAAC;IAC/C,OAAO,MAAMH,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEC,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArI,SAAS,CAAC,MAAM;IACd,IAAIqH,KAAK,CAACC,OAAO,CAAClE,UAAU,CAAC,IAAIA,UAAU,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACtD,MAAM8G,MAAM,GAAGnF,UAAU,CAACA,UAAU,CAAC3B,MAAM,GAAG,CAAC,CAAC;MAChD,IAAI8G,MAAM,IAAI,OAAOA,MAAM,CAACR,KAAK,KAAK,QAAQ,EAAE;QAC9CvD,eAAe,CAAC+D,MAAM,CAACR,KAAK,CAAC;QAC7B,IAAI3E,UAAU,CAAC3B,MAAM,GAAG,CAAC,EAAE;UACzB,MAAM+G,QAAQ,GAAGpF,UAAU,CAACA,UAAU,CAAC3B,MAAM,GAAG,CAAC,CAAC;UAClD,IAAI+G,QAAQ,IAAI,OAAOA,QAAQ,CAACT,KAAK,KAAK,QAAQ,EAAE;YAClDrD,cAAc,CAAE,CAAC6D,MAAM,CAACR,KAAK,GAAGS,QAAQ,CAACT,KAAK,IAAIS,QAAQ,CAACT,KAAK,GAAI,GAAG,CAAC;UAC1E;QACF;;QAEA;QACA,IAAItE,iBAAiB,CAACyB,OAAO,IAAIxB,YAAY,CAACwB,OAAO,EAAE;UACrD,IAAI;YACF;YACA,MAAMuD,aAAa,GAAG,CAAC,GAAGrF,UAAU,CAAC,CAClCsF,GAAG,CAACC,IAAI,KAAK;cACZb,IAAI,EAAEc,IAAI,CAACC,KAAK,CAAC,IAAI5H,IAAI,CAAC0H,IAAI,CAACxH,SAAS,CAAC,CAAC2H,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;cAC3Dd,IAAI,EAAEe,UAAU,CAACJ,IAAI,CAACX,IAAI,CAAC;cAC3BgB,IAAI,EAAED,UAAU,CAACJ,IAAI,CAACK,IAAI,CAAC;cAC3BC,GAAG,EAAEF,UAAU,CAACJ,IAAI,CAACM,GAAG,CAAC;cACzBlB,KAAK,EAAEgB,UAAU,CAACJ,IAAI,CAACZ,KAAK,CAAC;cAC7BF,MAAM,EAAEkB,UAAU,CAACJ,IAAI,CAACd,MAAM;YAChC,CAAC,CAAC,CAAC,CACFqB,MAAM,CAACP,IAAI,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACb,IAAI,CAAC,IAAI,CAACqB,KAAK,CAACR,IAAI,CAACZ,KAAK,CAAC,CAAC,CAAC;YAAA,CACxDqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvB,IAAI,GAAGwB,CAAC,CAACxB,IAAI,CAAC,CAAC,CAAC;;YAEpC;YACA,MAAMyB,UAAU,GAAG,EAAE;YACrB,MAAMC,OAAO,GAAG,IAAIhJ,GAAG,CAAC,CAAC;YAEzBiI,aAAa,CAACgB,OAAO,CAACd,IAAI,IAAI;cAC5B,IAAI,CAACa,OAAO,CAACE,GAAG,CAACf,IAAI,CAACb,IAAI,CAAC,IAAI0B,OAAO,CAACxI,GAAG,CAAC2H,IAAI,CAACb,IAAI,CAAC,CAACC,KAAK,KAAKY,IAAI,CAACZ,KAAK,EAAE;gBAC1EyB,OAAO,CAAChI,GAAG,CAACmH,IAAI,CAACb,IAAI,EAAEa,IAAI,CAAC;cAC9B;YACF,CAAC,CAAC;;YAEF;YACA,MAAMgB,SAAS,GAAGtC,KAAK,CAACuC,IAAI,CAACJ,OAAO,CAACK,MAAM,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvB,IAAI,GAAGwB,CAAC,CAACxB,IAAI,CAAC;;YAE9E;YACA,MAAMgC,UAAU,GAAGH,SAAS,CAACjB,GAAG,CAACC,IAAI,KAAK;cACxCb,IAAI,EAAEa,IAAI,CAACb,IAAI;cACfE,IAAI,EAAEW,IAAI,CAACX,IAAI;cACfgB,IAAI,EAAEL,IAAI,CAACK,IAAI;cACfC,GAAG,EAAEN,IAAI,CAACM,GAAG;cACblB,KAAK,EAAEY,IAAI,CAACZ;YACd,CAAC,CAAC,CAAC;;YAEH;YACA,MAAMgC,UAAU,GAAGJ,SAAS,CAACjB,GAAG,CAACC,IAAI,KAAK;cACxCb,IAAI,EAAEa,IAAI,CAACb,IAAI;cACflD,KAAK,EAAE+D,IAAI,CAACd,MAAM;cAClB5C,KAAK,EAAE0D,IAAI,CAACZ,KAAK,IAAIY,IAAI,CAACX,IAAI,GAAG,WAAW,GAAG;YACjD,CAAC,CAAC,CAAC;;YAEH;YACAvE,iBAAiB,CAACyB,OAAO,CAAC8E,OAAO,CAACF,UAAU,CAAC;YAC7CpG,YAAY,CAACwB,OAAO,CAAC8E,OAAO,CAACD,UAAU,CAAC;YAExC3I,OAAO,CAACC,GAAG,CAAC,sCAAsCkH,MAAM,CAACR,KAAK,KAAK4B,SAAS,CAAClI,MAAM,iBAAiB,CAAC;YACrG2C,aAAa,CAAC,IAAInD,IAAI,CAAC,CAAC,CAAC;YACzBqD,aAAa,CAAC,IAAI,CAAC;YACnB2D,UAAU,CAAC,MAAM3D,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;UAE7C,CAAC,CAAC,OAAO6C,KAAK,EAAE;YACd/F,OAAO,CAAC+F,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UACpE;QACF;MACF;IACF;EACF,CAAC,EAAE,CAAC/D,UAAU,CAAC,CAAC;;EAEhB;EACApD,SAAS,CAAC,MAAM;IACd,IAAI,CAACyD,iBAAiB,CAACyB,OAAO,IAAI,CAACxB,YAAY,CAACwB,OAAO,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACvD,SAAS,CAAC,IAAIA,SAAS,CAACtC,MAAM,KAAK,CAAC,EAAE;MAC9G;IACF;IAEA,IAAI;MACF6C,aAAa,CAAC,IAAI,CAAC;MACnBF,aAAa,CAAC,IAAInD,IAAI,CAAC,CAAC,CAAC;MAEzB,MAAMgJ,aAAa,GAAGlG,SAAS,CAC5BmF,MAAM,CAACP,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACxH,SAAS,IAAI,OAAOwH,IAAI,CAACX,IAAI,KAAK,QAAQ,CAAC,CACvEU,GAAG,CAACC,IAAI,KAAK;QACZb,IAAI,EAAE,IAAI7G,IAAI,CAAC0H,IAAI,CAACxH,SAAS,CAAC,CAAC2H,OAAO,CAAC,CAAC,GAAG,IAAI;QAC/Cd,IAAI,EAAEW,IAAI,CAACX,IAAI;QACfgB,IAAI,EAAEL,IAAI,CAACK,IAAI;QACfC,GAAG,EAAEN,IAAI,CAACM,GAAG;QACblB,KAAK,EAAEY,IAAI,CAACZ;MACd,CAAC,CAAC,CAAC,CACFqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvB,IAAI,GAAGwB,CAAC,CAACxB,IAAI,CAAC,CAAC;MAAA,CAChCoB,MAAM,CAAC,CAACP,IAAI,EAAEuB,KAAK,EAAEC,KAAK,KAAK;QAC9B;QACA,OAAOD,KAAK,KAAK,CAAC,IAAIvB,IAAI,CAACb,IAAI,KAAKqC,KAAK,CAACD,KAAK,GAAG,CAAC,CAAC,CAACpC,IAAI;MAC3D,CAAC,CAAC;MAEJ,MAAMiC,UAAU,GAAGhG,SAAS,CACzBmF,MAAM,CAACP,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACxH,SAAS,IAAI,OAAOwH,IAAI,CAACd,MAAM,KAAK,QAAQ,CAAC,CACzEa,GAAG,CAACC,IAAI,KAAK;QACZb,IAAI,EAAE,IAAI7G,IAAI,CAAC0H,IAAI,CAACxH,SAAS,CAAC,CAAC2H,OAAO,CAAC,CAAC,GAAG,IAAI;QAC/ClE,KAAK,EAAE+D,IAAI,CAACd,MAAM;QAClB5C,KAAK,EAAE0D,IAAI,CAACZ,KAAK,IAAIY,IAAI,CAACX,IAAI,GAAG,WAAW,GAAG;MACjD,CAAC,CAAC,CAAC,CACFoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvB,IAAI,GAAGwB,CAAC,CAACxB,IAAI,CAAC,CAAC;MAAA,CAChCoB,MAAM,CAAC,CAACP,IAAI,EAAEuB,KAAK,EAAEC,KAAK,KAAK;QAC9B;QACA,OAAOD,KAAK,KAAK,CAAC,IAAIvB,IAAI,CAACb,IAAI,KAAKqC,KAAK,CAACD,KAAK,GAAG,CAAC,CAAC,CAACpC,IAAI;MAC3D,CAAC,CAAC;MAEJ1G,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE4I,aAAa,CAACxI,MAAM,EAAE,SAAS,CAAC;MAE7E,IAAIwI,aAAa,CAACxI,MAAM,GAAG,CAAC,EAAE;QAC5BgC,iBAAiB,CAACyB,OAAO,CAAC8E,OAAO,CAACC,aAAa,CAAC;MAClD;MAEA,IAAIF,UAAU,CAACtI,MAAM,GAAG,CAAC,EAAE;QACzBiC,YAAY,CAACwB,OAAO,CAAC8E,OAAO,CAACD,UAAU,CAAC;MAC1C;;MAEA;MACAK,sBAAsB,CAACH,aAAa,CAAC;MAErChC,UAAU,CAAC,MAAM3D,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;IAE7C,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACd/F,OAAO,CAAC+F,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D7C,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACP,SAAS,EAAEF,gBAAgB,CAAC,CAAC;;EAEjC;EACA,MAAMuG,sBAAsB,GAAIN,UAAU,IAAK;IAC7C,IAAI,CAACtG,KAAK,CAAC0B,OAAO,IAAI4E,UAAU,CAACrI,MAAM,GAAG,EAAE,EAAE;;IAE9C;IACA4I,MAAM,CAACR,MAAM,CAAClG,eAAe,CAACuB,OAAO,CAAC,CAACuE,OAAO,CAACa,MAAM,IAAI;MACvD,IAAIA,MAAM,EAAE;QACV9G,KAAK,CAAC0B,OAAO,CAACqF,YAAY,CAACD,MAAM,CAAC;MACpC;IACF,CAAC,CAAC;IACF3G,eAAe,CAACuB,OAAO,GAAG,CAAC,CAAC;IAE5B,MAAMsF,MAAM,GAAGV,UAAU,CAACpB,GAAG,CAAC+B,CAAC,IAAIA,CAAC,CAAC1C,KAAK,CAAC;IAC3C,MAAM2C,KAAK,GAAGZ,UAAU,CAACpB,GAAG,CAAC+B,CAAC,IAAIA,CAAC,CAACzB,IAAI,CAAC;IACzC,MAAM2B,IAAI,GAAGb,UAAU,CAACpB,GAAG,CAAC+B,CAAC,IAAIA,CAAC,CAACxB,GAAG,CAAC;;IAEvC;IACA,IAAIpF,gBAAgB,CAAC+G,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtC,MAAMC,SAAS,GAAGC,YAAY,CAACN,MAAM,EAAE,EAAE,CAAC;MAC1C,MAAMO,WAAW,GAAGvH,KAAK,CAAC0B,OAAO,CAAC8F,aAAa,CAAC;QAC9C/F,KAAK,EAAE,SAAS;QAChBgG,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,MAAMC,kBAAkB,GAAGN,SAAS,CACjCnC,GAAG,CAAC,CAAC9D,KAAK,EAAEsF,KAAK,KAAKtF,KAAK,KAAK,IAAI,GAAG;QAAEkD,IAAI,EAAEgC,UAAU,CAACI,KAAK,CAAC,CAACpC,IAAI;QAAElD;MAAM,CAAC,GAAG,IAAI,CAAC,CACtFsE,MAAM,CAACP,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;MAChCoC,WAAW,CAACf,OAAO,CAACmB,kBAAkB,CAAC;MACvCxH,eAAe,CAACuB,OAAO,CAACkG,KAAK,GAAGL,WAAW;IAC7C;IAEA,IAAIlH,gBAAgB,CAAC+G,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtC,MAAMS,SAAS,GAAGP,YAAY,CAACN,MAAM,EAAE,EAAE,CAAC;MAC1C,MAAMc,WAAW,GAAG9H,KAAK,CAAC0B,OAAO,CAAC8F,aAAa,CAAC;QAC9C/F,KAAK,EAAE,SAAS;QAChBgG,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,MAAMK,kBAAkB,GAAGF,SAAS,CACjC3C,GAAG,CAAC,CAAC9D,KAAK,EAAEsF,KAAK,KAAKtF,KAAK,KAAK,IAAI,GAAG;QAAEkD,IAAI,EAAEgC,UAAU,CAACI,KAAK,CAAC,CAACpC,IAAI;QAAElD;MAAM,CAAC,GAAG,IAAI,CAAC,CACtFsE,MAAM,CAACP,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;MAChC2C,WAAW,CAACtB,OAAO,CAACuB,kBAAkB,CAAC;MACvC5H,eAAe,CAACuB,OAAO,CAACsG,KAAK,GAAGF,WAAW;IAC7C;;IAEA;IACA,IAAIzH,gBAAgB,CAAC+G,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtC,MAAMa,SAAS,GAAGC,YAAY,CAAClB,MAAM,EAAE,EAAE,CAAC;MAC1C,MAAMmB,WAAW,GAAGnI,KAAK,CAAC0B,OAAO,CAAC8F,aAAa,CAAC;QAC9C/F,KAAK,EAAE,SAAS;QAChBgG,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,MAAMU,kBAAkB,GAAGH,SAAS,CACjC/C,GAAG,CAAC,CAAC9D,KAAK,EAAEsF,KAAK,MAAM;QAAEpC,IAAI,EAAEgC,UAAU,CAACI,KAAK,CAAC,CAACpC,IAAI;QAAElD;MAAM,CAAC,CAAC,CAAC;MACnE+G,WAAW,CAAC3B,OAAO,CAAC4B,kBAAkB,CAAC;MACvCjI,eAAe,CAACuB,OAAO,CAAC2G,KAAK,GAAGF,WAAW;IAC7C;IAEA,IAAI9H,gBAAgB,CAAC+G,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtC,MAAMkB,SAAS,GAAGJ,YAAY,CAAClB,MAAM,EAAE,EAAE,CAAC;MAC1C,MAAMuB,WAAW,GAAGvI,KAAK,CAAC0B,OAAO,CAAC8F,aAAa,CAAC;QAC9C/F,KAAK,EAAE,SAAS;QAChBgG,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,MAAMc,kBAAkB,GAAGF,SAAS,CACjCpD,GAAG,CAAC,CAAC9D,KAAK,EAAEsF,KAAK,MAAM;QAAEpC,IAAI,EAAEgC,UAAU,CAACI,KAAK,CAAC,CAACpC,IAAI;QAAElD;MAAM,CAAC,CAAC,CAAC;MACnEmH,WAAW,CAAC/B,OAAO,CAACgC,kBAAkB,CAAC;MACvCrI,eAAe,CAACuB,OAAO,CAAC+G,KAAK,GAAGF,WAAW;IAC7C;;IAEA;IACA,IAAIlI,gBAAgB,CAAC+G,QAAQ,CAAC,IAAI,CAAC,EAAE;MACnC,MAAMsB,MAAM,GAAGC,uBAAuB,CAAC3B,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;MAErD,MAAM4B,aAAa,GAAG5I,KAAK,CAAC0B,OAAO,CAAC8F,aAAa,CAAC;QAChD/F,KAAK,EAAE,SAAS;QAChBgG,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,MAAMmB,aAAa,GAAG7I,KAAK,CAAC0B,OAAO,CAAC8F,aAAa,CAAC;QAChD/F,KAAK,EAAE,SAAS;QAChBgG,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,MAAMoB,WAAW,GAAGJ,MAAM,CAACK,KAAK,CAC7B7D,GAAG,CAAC,CAAC9D,KAAK,EAAEsF,KAAK,KAAKtF,KAAK,KAAK,IAAI,GAAG;QAAEkD,IAAI,EAAEgC,UAAU,CAACI,KAAK,CAAC,CAACpC,IAAI;QAAElD;MAAM,CAAC,GAAG,IAAI,CAAC,CACtFsE,MAAM,CAACP,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;MAChC,MAAM6D,WAAW,GAAGN,MAAM,CAACO,KAAK,CAC7B/D,GAAG,CAAC,CAAC9D,KAAK,EAAEsF,KAAK,KAAKtF,KAAK,KAAK,IAAI,GAAG;QAAEkD,IAAI,EAAEgC,UAAU,CAACI,KAAK,CAAC,CAACpC,IAAI;QAAElD;MAAM,CAAC,GAAG,IAAI,CAAC,CACtFsE,MAAM,CAACP,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;MAEhCyD,aAAa,CAACpC,OAAO,CAACsC,WAAW,CAAC;MAClCD,aAAa,CAACrC,OAAO,CAACwC,WAAW,CAAC;MAClC7I,eAAe,CAACuB,OAAO,CAACwH,OAAO,GAAGN,aAAa;MAC/CzI,eAAe,CAACuB,OAAO,CAACyH,OAAO,GAAGN,aAAa;IACjD;EACF,CAAC;EAED,MAAMvB,YAAY,GAAGA,CAACxJ,IAAI,EAAEsL,MAAM,KAAK;IACrC,MAAMC,GAAG,GAAG,EAAE;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxL,IAAI,CAACG,MAAM,EAAEqL,CAAC,EAAE,EAAE;MACpC,IAAIA,CAAC,GAAGF,MAAM,GAAG,CAAC,EAAE;QAClBC,GAAG,CAACE,IAAI,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL,MAAMC,GAAG,GAAG1L,IAAI,CAAC2L,KAAK,CAACH,CAAC,GAAGF,MAAM,GAAG,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC7D,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;QACxEuD,GAAG,CAACE,IAAI,CAACC,GAAG,GAAGJ,MAAM,CAAC;MACxB;IACF;IACA,OAAOC,GAAG;EACZ,CAAC;EAED,MAAMnB,YAAY,GAAGA,CAACpK,IAAI,EAAEsL,MAAM,KAAK;IACrC,MAAMO,GAAG,GAAG,EAAE;IACd,MAAMC,UAAU,GAAG,CAAC,IAAIR,MAAM,GAAG,CAAC,CAAC;IAEnC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxL,IAAI,CAACG,MAAM,EAAEqL,CAAC,EAAE,EAAE;MACpC,IAAIA,CAAC,KAAK,CAAC,EAAE;QACXK,GAAG,CAACJ,IAAI,CAACzL,IAAI,CAACwL,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACLK,GAAG,CAACJ,IAAI,CAAEzL,IAAI,CAACwL,CAAC,CAAC,GAAGM,UAAU,GAAKD,GAAG,CAACL,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGM,UAAU,CAAE,CAAC;MACpE;IACF;IACA,OAAOD,GAAG;EACZ,CAAC;EAED,MAAMhB,uBAAuB,GAAGA,CAAC7K,IAAI,EAAEsL,MAAM,EAAES,MAAM,KAAK;IACxD,MAAMR,GAAG,GAAG/B,YAAY,CAACxJ,IAAI,EAAEsL,MAAM,CAAC;IACtC,MAAML,KAAK,GAAG,EAAE;IAChB,MAAME,KAAK,GAAG,EAAE;IAEhB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxL,IAAI,CAACG,MAAM,EAAEqL,CAAC,EAAE,EAAE;MACpC,IAAIA,CAAC,GAAGF,MAAM,GAAG,CAAC,EAAE;QAClBL,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;QAChBN,KAAK,CAACM,IAAI,CAAC,IAAI,CAAC;MAClB,CAAC,MAAM;QACL,MAAME,KAAK,GAAG3L,IAAI,CAAC2L,KAAK,CAACH,CAAC,GAAGF,MAAM,GAAG,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAMQ,IAAI,GAAGT,GAAG,CAACC,CAAC,CAAC;QACnB,MAAMS,QAAQ,GAAGN,KAAK,CAACC,MAAM,CAAC,CAACF,GAAG,EAAEQ,GAAG,KAAKR,GAAG,GAAGpE,IAAI,CAAC6E,GAAG,CAACD,GAAG,GAAGF,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGV,MAAM;QACtF,MAAMc,iBAAiB,GAAG9E,IAAI,CAAC+E,IAAI,CAACJ,QAAQ,CAAC;QAE7ChB,KAAK,CAACQ,IAAI,CAACO,IAAI,GAAII,iBAAiB,GAAGL,MAAO,CAAC;QAC/CZ,KAAK,CAACM,IAAI,CAACO,IAAI,GAAII,iBAAiB,GAAGL,MAAO,CAAC;MACjD;IACF;IAEA,OAAO;MAAEd,KAAK;MAAEqB,MAAM,EAAEf,GAAG;MAAEJ;IAAM,CAAC;EACtC,CAAC;;EAED;EACAzM,SAAS,CAAC,MAAM;IACd,IAAI,CAACyD,iBAAiB,CAACyB,OAAO,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACjE,OAAO,CAAC,IAAIA,OAAO,CAAC5B,MAAM,KAAK,CAAC,EAAE;IAEnF,IAAI;MACF,MAAMoM,OAAO,GAAGxK,OAAO,CACpB6F,MAAM,CAAC4E,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACA,MAAM,KAAK,MAAM,IAAIA,MAAM,CAAC3M,SAAS,CAAC,CACzFuH,GAAG,CAACoF,MAAM,IAAI;QACb,MAAMC,KAAK,GAAGD,MAAM,CAACA,MAAM,KAAK,KAAK;QACrC,OAAO;UACLhG,IAAI,EAAE,IAAI7G,IAAI,CAAC6M,MAAM,CAAC3M,SAAS,CAAC,CAAC2H,OAAO,CAAC,CAAC,GAAG,IAAI;UACjDkF,QAAQ,EAAED,KAAK,GAAG,UAAU,GAAG,UAAU;UACzC9I,KAAK,EAAE8I,KAAK,GAAG,SAAS,GAAG,SAAS;UACpCE,KAAK,EAAEF,KAAK,GAAG,SAAS,GAAG,WAAW;UACtCG,IAAI,EAAE,GAAGJ,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACK,KAAK,GAAG,KAAKL,MAAM,CAACK,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAACN,MAAM,CAACO,UAAU,IAAI,CAAC,IAAI,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,IAAI;UAChIE,IAAI,EAAE;QACR,CAAC;MACH,CAAC,CAAC;MAEJ7K,iBAAiB,CAACyB,OAAO,CAACqJ,UAAU,CAACV,OAAO,CAAC;MAC7CzM,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEwM,OAAO,CAACpM,MAAM,EAAE,eAAe,CAAC;IACnF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd/F,OAAO,CAAC+F,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACrE;EACF,CAAC,EAAE,CAAC9D,OAAO,CAAC,CAAC;EAEb,MAAMmL,eAAe,GAAIC,WAAW,IAAK;IACvC3K,mBAAmB,CAAC4K,IAAI,IACtBA,IAAI,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC,GACtBC,IAAI,CAACxF,MAAM,CAACnE,EAAE,IAAIA,EAAE,KAAK0J,WAAW,CAAC,GACrC,CAAC,GAAGC,IAAI,EAAED,WAAW,CAC3B,CAAC;EACH,CAAC;EAED,MAAME,qBAAqB,GAAIC,YAAY,IAAK;IAC9CxN,OAAO,CAACC,GAAG,CAAC,8CAA8CT,SAAS,OAAOgO,YAAY,QAAQjO,MAAM,EAAE,CAAC;IACvGiD,YAAY,CAACgL,YAAY,CAAC;IAC1B;IACA5K,YAAY,CAAC,EAAE,CAAC;IAChB;IACA6K,eAAe,CAACD,YAAY,EAAE,IAAI,CAAC;EACrC,CAAC;EAED,MAAMC,eAAe,GAAG,MAAAA,CAAOjO,SAAS,EAAEkO,YAAY,GAAG,KAAK,KAAK;IACjE5K,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF9C,OAAO,CAACC,GAAG,CAAC,8BAA8BT,SAAS,aAAaD,MAAM,GAAGmO,YAAY,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC;;MAElH;MACA,IAAI,CAACA,YAAY,EAAE;QACjB,MAAMC,UAAU,GAAGlO,aAAa,CAACF,MAAM,EAAEC,SAAS,CAAC;QACnD,IAAImO,UAAU,IAAIA,UAAU,CAACtN,MAAM,GAAG,CAAC,EAAE;UACvCuC,YAAY,CAAC+K,UAAU,CAAC;UACxB7K,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA,MAAM8K,UAAU,GAAGrO,MAAM,CAACsO,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAE;MAC7C,MAAMC,kBAAkB,GAAG,MAAMC,KAAK,CACpC,0CAA0CH,UAAU,yBAAyBpO,SAAS,aACxF,CAAC;MAED,IAAIwO,cAAc,GAAG,EAAE;MACvB,IAAIF,kBAAkB,CAACG,EAAE,EAAE;QACzB,MAAMC,UAAU,GAAG,MAAMJ,kBAAkB,CAACK,IAAI,CAAC,CAAC;QAClD,IAAID,UAAU,CAACE,OAAO,IAAIF,UAAU,CAAChO,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;UACpD2N,cAAc,GAAGE,UAAU,CAAChO,IAAI;UAChCF,OAAO,CAACC,GAAG,CAAC,+BAA+B+N,cAAc,CAAC3N,MAAM,eAAeb,SAAS,gBAAgBD,MAAM,EAAE,CAAC;QACnH;MACF;;MAEA;MACA,MAAM8O,SAAS,GAAG9O,MAAM,GAAG,SAAS+O,kBAAkB,CAAC/O,MAAM,CAAC,EAAE,GAAG,EAAE;MACrE,MAAMgP,YAAY,GAAG,MAAMR,KAAK,CAC9B,yDAAyDvO,SAAS,GAAG6O,SAAS,EAChF,CAAC;MAED,IAAIG,QAAQ,GAAG,EAAE;MACjB,IAAID,YAAY,CAACN,EAAE,EAAE;QACnBO,QAAQ,GAAG,MAAMD,YAAY,CAACJ,IAAI,CAAC,CAAC;QACpCnO,OAAO,CAACC,GAAG,CAAC,+BAA+BuO,QAAQ,CAACnO,MAAM,SAASb,SAAS,oBAAoBD,MAAM,EAAE,CAAC;MAC3G;;MAEA;MACA,MAAMkP,YAAY,GAAG,CAAC,GAAGT,cAAc,EAAE,GAAGQ,QAAQ,CAAC;MACrD,MAAMrG,UAAU,GAAG,EAAE;MACrB,MAAMuG,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;;MAEhC;MACAF,YAAY,CACTzG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrI,IAAI,CAACoI,CAAC,CAAClI,SAAS,CAAC,GAAG,IAAIF,IAAI,CAACqI,CAAC,CAACnI,SAAS,CAAC,CAAC,CAC7DsI,OAAO,CAACd,IAAI,IAAI;QACf,MAAMxH,SAAS,GAAG,IAAIF,IAAI,CAAC0H,IAAI,CAACxH,SAAS,CAAC,CAAC2H,OAAO,CAAC,CAAC;QACpD,IAAI,CAACgH,cAAc,CAACpG,GAAG,CAACvI,SAAS,CAAC,EAAE;UAClC2O,cAAc,CAACE,GAAG,CAAC7O,SAAS,CAAC;UAC7BoI,UAAU,CAACwD,IAAI,CAACpE,IAAI,CAAC;QACvB;MACF,CAAC,CAAC;MAEJvH,OAAO,CAACC,GAAG,CAAC,oCAAoCkI,UAAU,CAAC9H,MAAM,WAAWb,SAAS,gBAAgBD,MAAM,EAAE,CAAC;MAC9GqD,YAAY,CAACuF,UAAU,CAAC;;MAExB;MACA,IAAIA,UAAU,CAAC9H,MAAM,GAAG,CAAC,EAAE;QACzBF,aAAa,CAACZ,MAAM,EAAEC,SAAS,EAAE2I,UAAU,CAAC;MAC9C;IAEF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd/F,OAAO,CAAC+F,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D;MACA,IAAI;QACF,MAAMsI,SAAS,GAAG9O,MAAM,GAAG,SAAS+O,kBAAkB,CAAC/O,MAAM,CAAC,EAAE,GAAG,EAAE;QACrE,MAAMsP,QAAQ,GAAG,MAAMd,KAAK,CAAC,yDAAyDvO,SAAS,GAAG6O,SAAS,EAAE,CAAC;QAC9G,IAAIQ,QAAQ,CAACZ,EAAE,EAAE;UACf,MAAM/N,IAAI,GAAG,MAAM2O,QAAQ,CAACV,IAAI,CAAC,CAAC;UAClCnO,OAAO,CAACC,GAAG,CAAC,8BAA8BC,IAAI,CAACG,MAAM,IAAIb,SAAS,oBAAoBD,MAAM,EAAE,CAAC;UAC/FqD,YAAY,CAAC1C,IAAI,CAAC;QACpB;MACF,CAAC,CAAC,OAAO4O,aAAa,EAAE;QACtB9O,OAAO,CAAC+F,KAAK,CAAC,2CAA2C,EAAE+I,aAAa,CAAC;MAC3E;IACF,CAAC,SAAS;MACRhM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE5D,OAAA,CAACoB,cAAc;IAAAyO,QAAA,gBACb7P,OAAA,CAACuB,WAAW;MAAAsO,QAAA,gBACV7P,OAAA;QAAK8P,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjE7P,OAAA,CAACyB,KAAK;UAAAoO,QAAA,GAAC,eAAG,EAACxP,MAAM,EAAC,iBAAe;QAAA;UAAA6P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzCrQ,OAAA;UAAK8P,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAJ,QAAA,gBAChE7P,OAAA;YAAK8P,KAAK,EAAE;cACVlK,KAAK,EAAE,KAAK;cACZE,MAAM,EAAE,KAAK;cACbwK,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAExM,UAAU,GAAG,SAAS,GAAG,MAAM;cAChDyM,SAAS,EAAEzM,UAAU,GAAG,mBAAmB,GAAG;YAChD;UAAE;YAAAmM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACLrQ,OAAA;YAAM8P,KAAK,EAAE;cACXW,QAAQ,EAAE,MAAM;cAChB9L,KAAK,EAAE,MAAM;cACb+L,UAAU,EAAE;YACd,CAAE;YAAAb,QAAA,EACChM,UAAU,GAAG,SAASA,UAAU,CAAC8M,kBAAkB,CAAC,CAAC,EAAE,GAAG;UAAY;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrQ,OAAA,CAAC4B,iBAAiB;QAAAiO,QAAA,gBAChB7P,OAAA;UAAK8P,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAJ,QAAA,gBAChE7P,OAAA;YAAM8P,KAAK,EAAE;cAAEnL,KAAK,EAAE,MAAM;cAAE8L,QAAQ,EAAE;YAAO,CAAE;YAAAZ,QAAA,EAAC;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAClEhM,UAAU,CAAC+D,GAAG,CAACwI,EAAE,iBAChB5Q,OAAA,CAAC8B,eAAe;YAEdG,OAAO,EAAE3B,SAAS,KAAKsQ,EAAE,CAACtM,KAAM;YAChCuM,OAAO,EAAEA,CAAA,KAAMxC,qBAAqB,CAACuC,EAAE,CAACtM,KAAK,CAAE;YAAAuL,QAAA,EAE9Ce,EAAE,CAACrM;UAAK,GAJJqM,EAAE,CAACtM,KAAK;YAAA4L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKE,CAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrQ,OAAA;UAAK8P,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAJ,QAAA,gBAChE7P,OAAA;YAAM8P,KAAK,EAAE;cAAEnL,KAAK,EAAE,MAAM;cAAE8L,QAAQ,EAAE;YAAO,CAAE;YAAAZ,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACnE7L,mBAAmB,CAACmI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACvE,GAAG,CAAC0I,SAAS,iBAC5C9Q,OAAA,CAACmC,eAAe;YAEdF,OAAO,EAAEsB,gBAAgB,CAAC+G,QAAQ,CAACwG,SAAS,CAACrM,EAAE,CAAE;YACjDoM,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC4C,SAAS,CAACrM,EAAE,CAAE;YAC7CmG,KAAK,EAAEkG,SAAS,CAACpM,IAAK;YAAAmL,QAAA,EAErBiB,SAAS,CAACrM;UAAE,GALRqM,SAAS,CAACrM,EAAE;YAAAyL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMF,CAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEdrQ,OAAA,CAACqC,SAAS;MAAAwN,QAAA,gBACR7P,OAAA;QAAK+Q,GAAG,EAAE9N,iBAAkB;QAAC6M,KAAK,EAAE;UAAElK,KAAK,EAAE,MAAM;UAAEE,MAAM,EAAE;QAAO;MAAE;QAAAoK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAExE9M,gBAAgB,CAACpC,MAAM,GAAG,CAAC,iBAC1BnB,OAAA,CAACuC,aAAa;QAAAsN,QAAA,gBACZ7P,OAAA;UAAK8P,KAAK,EAAE;YAAEkB,UAAU,EAAE,MAAM;YAAEC,YAAY,EAAE,KAAK;YAAEtM,KAAK,EAAE;UAAU,CAAE;UAAAkL,QAAA,GAAC,qBACtD,EAACtM,gBAAgB,CAACpC,MAAM,EAAC,GAC9C;QAAA;UAAA+O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrQ,OAAA;UAAK8P,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAE9L,KAAK,EAAE;UAAO,CAAE;UAAAkL,QAAA,EAC7CtM,gBAAgB,CAAC6E,GAAG,CAAC3D,EAAE,IAAI;YAC1B,MAAMqM,SAAS,GAAGtM,mBAAmB,CAAC0M,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1M,EAAE,KAAKA,EAAE,CAAC;YAChE,OAAOqM,SAAS,gBACd9Q,OAAA;cAAe8P,KAAK,EAAE;gBACpBC,OAAO,EAAE,cAAc;gBACvBqB,WAAW,EAAE,KAAK;gBAClBzM,KAAK,EAAEmM,SAAS,CAACnM;cACnB,CAAE;cAAAkL,QAAA,EACCiB,SAAS,CAACpM;YAAI,GALND,EAAE;cAAAyL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMP,CAAC,GACL,IAAI;UACV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAChB,EAEA1M,OAAO,iBACN3D,OAAA;QAAK8P,KAAK,EAAE;UACVpC,QAAQ,EAAE,UAAU;UACpB/G,GAAG,EAAE,KAAK;UACV0K,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE,uBAAuB;UAClCxM,UAAU,EAAE,oBAAoB;UAChCH,KAAK,EAAE,MAAM;UACb4M,OAAO,EAAE,WAAW;UACpBjB,YAAY,EAAE,KAAK;UACnBG,QAAQ,EAAE,MAAM;UAChBe,MAAM,EAAE;QACV,CAAE;QAAA3B,QAAA,GAAC,UACO,EAACvP,SAAS,EAAC,UACrB;MAAA;QAAA4P,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAEZrQ,OAAA,CAACyC,SAAS;MAAAoN,QAAA,gBACR7P,OAAA,CAAC2C,UAAU;QAAAkN,QAAA,gBACT7P,OAAA;UAAA6P,QAAA,EAAM;QAA6B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CrQ,OAAA;UAAM8P,KAAK,EAAE;YAAEnL,KAAK,EAAE;UAAU,CAAE;UAAAkL,QAAA,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACbrQ,OAAA,CAAC2C,UAAU;QAAAkN,QAAA,gBACT7P,OAAA;UAAA6P,QAAA,GAAM,YAAU,EAAC,CAAA5L,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6J,OAAO,CAAC,CAAC,CAAC,KAAI,QAAQ;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7DrQ,OAAA;UAAM8P,KAAK,EAAE;YAAEnL,KAAK,EAAER,WAAW,IAAI,CAAC,GAAG,SAAS,GAAG;UAAU,CAAE;UAAA0L,QAAA,GAC9D1L,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,WAAW,CAAC2J,OAAO,CAAC,CAAC,CAAC,EAAC,GACvD;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbrQ,OAAA,CAAC2C,UAAU;QAAAkN,QAAA,eACT7P,OAAA;UAAA6P,QAAA,GAAM,eAAG,EAACpM,SAAS,CAACtC,MAAM,EAAC,kBAAW,EAACoC,gBAAgB,CAACpC,MAAM,EAAC,aAAW;QAAA;UAAA+O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAACrN,EAAA,CAprBIH,aAAa;AAAA4O,GAAA,GAAb5O,aAAa;AA0rBnB,eAAeA,aAAa;AAAC,IAAAvB,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA6O,GAAA;AAAAC,YAAA,CAAApQ,EAAA;AAAAoQ,YAAA,CAAAlQ,GAAA;AAAAkQ,YAAA,CAAA/P,GAAA;AAAA+P,YAAA,CAAA7P,GAAA;AAAA6P,YAAA,CAAAxP,GAAA;AAAAwP,YAAA,CAAAtP,GAAA;AAAAsP,YAAA,CAAApP,GAAA;AAAAoP,YAAA,CAAAlP,GAAA;AAAAkP,YAAA,CAAAhP,GAAA;AAAAgP,YAAA,CAAA9O,GAAA;AAAA8O,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}