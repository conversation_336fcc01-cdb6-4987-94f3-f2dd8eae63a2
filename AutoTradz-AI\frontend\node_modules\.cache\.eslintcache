[{"C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ChartComponent.jsx": "3", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\DecisionPanel.jsx": "4", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\PortfolioPanel.jsx": "5", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ControlPanel.jsx": "6", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\PerformancePanel.jsx": "7", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ApiConfigPanel.jsx": "8", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\SimpleChart.jsx": "9", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ErrorBoundary.jsx": "10", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\RiskDashboard.jsx": "11", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ActivePositionsDashboard.jsx": "12", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\MobileNavigation.jsx": "13", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\NotificationPanel.jsx": "14", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\MultiAssetDashboard.jsx": "15", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AdvancedChart.jsx": "16", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\BacktestingDashboard.jsx": "17", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\MarketIntelligenceDashboard.jsx": "18", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AuthContext.jsx": "19", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\LoginPage.jsx": "20", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\CleanDashboard.jsx": "21", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\UserProfile.jsx": "22", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\CryptoPairSelector.jsx": "23", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\PairAnalytics.jsx": "24", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AlpacaMarketData.jsx": "25", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AlpacaAccount.jsx": "26", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\App.js": "28", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ErrorBoundary.jsx": "29", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AuthContext.jsx": "30", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\LoginPage.jsx": "31", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\CleanDashboard.jsx": "32", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\UserProfile.jsx": "33", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\CryptoPairSelector.jsx": "34", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\PairAnalytics.jsx": "35", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\SimpleChart.jsx": "36", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\DecisionPanel.jsx": "37", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AdvancedChart.jsx": "38", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\PortfolioPanel.jsx": "39", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\PerformancePanel.jsx": "40", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ActivePositionsDashboard.jsx": "41", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ControlPanel.jsx": "42", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\RiskDashboard.jsx": "43", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ApiConfigPanel.jsx": "44", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AlpacaMarketData.jsx": "45", "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AlpacaAccount.jsx": "46"}, {"size": 232, "mtime": *************, "results": "47", "hashOfConfig": "48"}, {"size": 17458, "mtime": *************, "results": "49", "hashOfConfig": "48"}, {"size": 14381, "mtime": *************, "results": "50", "hashOfConfig": "48"}, {"size": 6854, "mtime": *************, "results": "51", "hashOfConfig": "48"}, {"size": 11194, "mtime": *************, "results": "52", "hashOfConfig": "48"}, {"size": 13039, "mtime": *************, "results": "53", "hashOfConfig": "48"}, {"size": 9061, "mtime": *************, "results": "54", "hashOfConfig": "48"}, {"size": 22318, "mtime": *************, "results": "55", "hashOfConfig": "48"}, {"size": 19826, "mtime": *************, "results": "56", "hashOfConfig": "48"}, {"size": 1891, "mtime": *************, "results": "57", "hashOfConfig": "48"}, {"size": 13994, "mtime": *************, "results": "58", "hashOfConfig": "48"}, {"size": 11229, "mtime": *************, "results": "59", "hashOfConfig": "48"}, {"size": 9228, "mtime": 1750306929838, "results": "60", "hashOfConfig": "48"}, {"size": 11623, "mtime": 1750307465156, "results": "61", "hashOfConfig": "48"}, {"size": 11850, "mtime": 1750307992115, "results": "62", "hashOfConfig": "48"}, {"size": 27454, "mtime": 1750469236629, "results": "63", "hashOfConfig": "48"}, {"size": 15290, "mtime": 1750308516952, "results": "64", "hashOfConfig": "48"}, {"size": 14068, "mtime": 1750308758129, "results": "65", "hashOfConfig": "48"}, {"size": 1821, "mtime": 1750436629104, "results": "66", "hashOfConfig": "48"}, {"size": 8449, "mtime": 1750436609454, "results": "67", "hashOfConfig": "48"}, {"size": 11507, "mtime": 1750477008080, "results": "68", "hashOfConfig": "48"}, {"size": 7522, "mtime": 1750458942805, "results": "69", "hashOfConfig": "48"}, {"size": 9698, "mtime": 1750451193268, "results": "70", "hashOfConfig": "48"}, {"size": 11191, "mtime": 1750470811768, "results": "71", "hashOfConfig": "48"}, {"size": 16740, "mtime": 1750472523580, "results": "72", "hashOfConfig": "48"}, {"size": 8387, "mtime": 1750451147641, "results": "73", "hashOfConfig": "48"}, {"size": 232, "mtime": *************, "results": "74", "hashOfConfig": "75"}, {"size": 17458, "mtime": *************, "results": "76", "hashOfConfig": "75"}, {"size": 1891, "mtime": *************, "results": "77", "hashOfConfig": "75"}, {"size": 1821, "mtime": 1750436629104, "results": "78", "hashOfConfig": "75"}, {"size": 8449, "mtime": 1750436609454, "results": "79", "hashOfConfig": "75"}, {"size": 11507, "mtime": 1750477008080, "results": "80", "hashOfConfig": "75"}, {"size": 7522, "mtime": 1750458942805, "results": "81", "hashOfConfig": "75"}, {"size": 9698, "mtime": 1750451193268, "results": "82", "hashOfConfig": "75"}, {"size": 11191, "mtime": 1750470811768, "results": "83", "hashOfConfig": "75"}, {"size": 19826, "mtime": *************, "results": "84", "hashOfConfig": "75"}, {"size": 6854, "mtime": *************, "results": "85", "hashOfConfig": "75"}, {"size": 27454, "mtime": 1750469236629, "results": "86", "hashOfConfig": "75"}, {"size": 11194, "mtime": *************, "results": "87", "hashOfConfig": "75"}, {"size": 9061, "mtime": *************, "results": "88", "hashOfConfig": "75"}, {"size": 11229, "mtime": *************, "results": "89", "hashOfConfig": "75"}, {"size": 13039, "mtime": *************, "results": "90", "hashOfConfig": "75"}, {"size": 13994, "mtime": *************, "results": "91", "hashOfConfig": "75"}, {"size": 22318, "mtime": *************, "results": "92", "hashOfConfig": "75"}, {"size": 16740, "mtime": 1750472523580, "results": "93", "hashOfConfig": "75"}, {"size": 8387, "mtime": 1750451147641, "results": "94", "hashOfConfig": "75"}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l7q2s1", {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1r0itl9", {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\App.js", ["233", "234", "235", "236", "237", "238", "239"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ChartComponent.jsx", ["240"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\DecisionPanel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\PortfolioPanel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ControlPanel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\PerformancePanel.jsx", ["241", "242", "243"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ApiConfigPanel.jsx", ["244"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\SimpleChart.jsx", ["245", "246"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\RiskDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\ActivePositionsDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\MobileNavigation.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\NotificationPanel.jsx", ["247"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\MultiAssetDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AdvancedChart.jsx", ["248", "249", "250", "251"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\BacktestingDashboard.jsx", ["252", "253", "254", "255"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\MarketIntelligenceDashboard.jsx", ["256"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\LoginPage.jsx", ["257"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\CleanDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\UserProfile.jsx", ["258"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\CryptoPairSelector.jsx", ["259", "260"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\PairAnalytics.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AlpacaMarketData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\frontend\\src\\AlpacaAccount.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\App.js", ["261", "262", "263", "264", "265", "266", "267"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\LoginPage.jsx", ["268"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\CleanDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\UserProfile.jsx", ["269"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\CryptoPairSelector.jsx", ["270", "271"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\PairAnalytics.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\SimpleChart.jsx", ["272", "273"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\DecisionPanel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AdvancedChart.jsx", ["274", "275", "276", "277"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\PortfolioPanel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\PerformancePanel.jsx", ["278", "279", "280"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ActivePositionsDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ControlPanel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\RiskDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\ApiConfigPanel.jsx", ["281"], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AlpacaMarketData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\projects\\AutoTradz AI\\AutoTradz-AI\\frontend\\src\\AlpacaAccount.jsx", [], [], {"ruleId": "282", "severity": 1, "message": "283", "line": 136, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 136, "endColumn": 18}, {"ruleId": "282", "severity": 1, "message": "286", "line": 160, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 160, "endColumn": 19}, {"ruleId": "282", "severity": 1, "message": "287", "line": 175, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 175, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "288", "line": 189, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 189, "endColumn": 19}, {"ruleId": "282", "severity": 1, "message": "289", "line": 213, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 213, "endColumn": 18}, {"ruleId": "282", "severity": 1, "message": "290", "line": 270, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 270, "endColumn": 19}, {"ruleId": "291", "severity": 1, "message": "292", "line": 473, "column": 6, "nodeType": "293", "endLine": 473, "endColumn": 8, "suggestions": "294"}, {"ruleId": "291", "severity": 1, "message": "295", "line": 201, "column": 6, "nodeType": "293", "endLine": 201, "endColumn": 25, "suggestions": "296"}, {"ruleId": "282", "severity": 1, "message": "297", "line": 151, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 151, "endColumn": 20}, {"ruleId": "282", "severity": 1, "message": "298", "line": 152, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 152, "endColumn": 20}, {"ruleId": "282", "severity": 1, "message": "299", "line": 153, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 153, "endColumn": 21}, {"ruleId": "291", "severity": 1, "message": "300", "line": 283, "column": 6, "nodeType": "293", "endLine": 283, "endColumn": 8, "suggestions": "301"}, {"ruleId": "291", "severity": 1, "message": "302", "line": 310, "column": 6, "nodeType": "293", "endLine": 310, "endColumn": 33, "suggestions": "303"}, {"ruleId": "282", "severity": 1, "message": "304", "line": 342, "column": 19, "nodeType": "284", "messageId": "285", "endLine": 342, "endColumn": 29}, {"ruleId": "282", "severity": 1, "message": "305", "line": 221, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 221, "endColumn": 15}, {"ruleId": "282", "severity": 1, "message": "304", "line": 332, "column": 19, "nodeType": "284", "messageId": "285", "endLine": 332, "endColumn": 29}, {"ruleId": "291", "severity": 1, "message": "306", "line": 434, "column": 6, "nodeType": "293", "endLine": 434, "endColumn": 35, "suggestions": "307"}, {"ruleId": "282", "severity": 1, "message": "308", "line": 449, "column": 11, "nodeType": "284", "messageId": "285", "endLine": 449, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "309", "line": 450, "column": 11, "nodeType": "284", "messageId": "285", "endLine": 450, "endColumn": 15}, {"ruleId": "282", "severity": 1, "message": "310", "line": 88, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 88, "endColumn": 13}, {"ruleId": "282", "severity": 1, "message": "311", "line": 304, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 304, "endColumn": 23}, {"ruleId": "282", "severity": 1, "message": "312", "line": 424, "column": 11, "nodeType": "284", "messageId": "285", "endLine": 424, "endColumn": 24}, {"ruleId": "282", "severity": 1, "message": "313", "line": 425, "column": 11, "nodeType": "284", "messageId": "285", "endLine": 425, "endColumn": 22}, {"ruleId": "291", "severity": 1, "message": "314", "line": 242, "column": 6, "nodeType": "293", "endLine": 242, "endColumn": 14, "suggestions": "315"}, {"ruleId": "282", "severity": 1, "message": "316", "line": 1, "column": 27, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 36}, {"ruleId": "282", "severity": 1, "message": "317", "line": 127, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 127, "endColumn": 20}, {"ruleId": "282", "severity": 1, "message": "318", "line": 10, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 10, "endColumn": 12}, {"ruleId": "291", "severity": 1, "message": "319", "line": 276, "column": 6, "nodeType": "293", "endLine": 276, "endColumn": 8, "suggestions": "320"}, {"ruleId": "282", "severity": 1, "message": "283", "line": 136, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 136, "endColumn": 18}, {"ruleId": "282", "severity": 1, "message": "286", "line": 160, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 160, "endColumn": 19}, {"ruleId": "282", "severity": 1, "message": "287", "line": 175, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 175, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "288", "line": 189, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 189, "endColumn": 19}, {"ruleId": "282", "severity": 1, "message": "289", "line": 213, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 213, "endColumn": 18}, {"ruleId": "282", "severity": 1, "message": "290", "line": 270, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 270, "endColumn": 19}, {"ruleId": "291", "severity": 1, "message": "292", "line": 473, "column": 6, "nodeType": "293", "endLine": 473, "endColumn": 8, "suggestions": "321"}, {"ruleId": "282", "severity": 1, "message": "316", "line": 1, "column": 27, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 36}, {"ruleId": "282", "severity": 1, "message": "317", "line": 127, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 127, "endColumn": 20}, {"ruleId": "282", "severity": 1, "message": "318", "line": 10, "column": 7, "nodeType": "284", "messageId": "285", "endLine": 10, "endColumn": 12}, {"ruleId": "291", "severity": 1, "message": "319", "line": 276, "column": 6, "nodeType": "293", "endLine": 276, "endColumn": 8, "suggestions": "322"}, {"ruleId": "291", "severity": 1, "message": "302", "line": 310, "column": 6, "nodeType": "293", "endLine": 310, "endColumn": 33, "suggestions": "323"}, {"ruleId": "282", "severity": 1, "message": "304", "line": 342, "column": 19, "nodeType": "284", "messageId": "285", "endLine": 342, "endColumn": 29}, {"ruleId": "282", "severity": 1, "message": "304", "line": 332, "column": 19, "nodeType": "284", "messageId": "285", "endLine": 332, "endColumn": 29}, {"ruleId": "291", "severity": 1, "message": "306", "line": 434, "column": 6, "nodeType": "293", "endLine": 434, "endColumn": 35, "suggestions": "324"}, {"ruleId": "282", "severity": 1, "message": "308", "line": 449, "column": 11, "nodeType": "284", "messageId": "285", "endLine": 449, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "309", "line": 450, "column": 11, "nodeType": "284", "messageId": "285", "endLine": 450, "endColumn": 15}, {"ruleId": "282", "severity": 1, "message": "297", "line": 151, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 151, "endColumn": 20}, {"ruleId": "282", "severity": 1, "message": "298", "line": 152, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 152, "endColumn": 20}, {"ruleId": "282", "severity": 1, "message": "299", "line": 153, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 153, "endColumn": 21}, {"ruleId": "291", "severity": 1, "message": "300", "line": 283, "column": 6, "nodeType": "293", "endLine": 283, "endColumn": 8, "suggestions": "325"}, "no-unused-vars", "'MainContent' is assigned a value but never used.", "Identifier", "unusedVar", "'ChartSection' is assigned a value but never used.", "'SidePanel' is assigned a value but never used.", "'BottomPanels' is assigned a value but never used.", "'RiskSection' is assigned a value but never used.", "'apiStatus' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'wsConnected'. Either include it or remove the dependency array.", "ArrayExpression", ["326"], "React Hook useEffect has a missing dependency: 'chartData'. Either include it or remove the dependency array.", ["327"], "'sharpeRatio' is assigned a value but never used.", "'maxDrawdown' is assigned a value but never used.", "'profitFactor' is assigned a value but never used.", "React Hook React.useEffect has missing dependencies: 'config.apiKey' and 'config.apiSecret'. Either include them or remove the dependency array.", ["328"], "React Hook useEffect has a missing dependency: 'fetchMarketData'. Either include it or remove the dependency array.", ["329"], "'uniqueData' is assigned a value but never used.", "'stats' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addTechnicalIndicators'. Either include it or remove the dependency array.", ["330"], "'highs' is assigned a value but never used.", "'lows' is assigned a value but never used.", "'Select' is assigned a value but never used.", "'formatCurrency' is assigned a value but never used.", "'tradeAnalysis' is assigned a value but never used.", "'riskMetrics' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMarketIntelligence'. Either include it or remove the dependency array.", ["331"], "'useEffect' is defined but never used.", "'ApiKeyDisplay' is assigned a value but never used.", "'pulse' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAvailablePairs'. Either include it or remove the dependency array.", ["332"], ["333"], ["334"], ["335"], ["336"], ["337"], {"desc": "338", "fix": "339"}, {"desc": "340", "fix": "341"}, {"desc": "342", "fix": "343"}, {"desc": "344", "fix": "345"}, {"desc": "346", "fix": "347"}, {"desc": "348", "fix": "349"}, {"desc": "350", "fix": "351"}, {"desc": "338", "fix": "352"}, {"desc": "350", "fix": "353"}, {"desc": "344", "fix": "354"}, {"desc": "346", "fix": "355"}, {"desc": "342", "fix": "356"}, "Update the dependencies array to be: [wsConnected]", {"range": "357", "text": "358"}, "Update the dependencies array to be: [chartData, selectedTimeframe]", {"range": "359", "text": "360"}, "Update the dependencies array to be: [config.api<PERSON><PERSON>, config.apiSecret]", {"range": "361", "text": "362"}, "Update the dependencies array to be: [fetchMarketData, selectedTimeframe, symbol]", {"range": "363", "text": "364"}, "Update the dependencies array to be: [chartData, activeIndicators, addTechnicalIndicators]", {"range": "365", "text": "366"}, "Update the dependencies array to be: [fetchMarketIntelligence, symbol]", {"range": "367", "text": "368"}, "Update the dependencies array to be: [fetchAvailablePairs]", {"range": "369", "text": "370"}, {"range": "371", "text": "358"}, {"range": "372", "text": "370"}, {"range": "373", "text": "364"}, {"range": "374", "text": "366"}, {"range": "375", "text": "362"}, [12862, 12864], "[wsConnected]", [5175, 5194], "[chartData, selectedTimeframe]", [6045, 6047], "[config.api<PERSON><PERSON>, config.apiSecret]", [10010, 10037], "[fetchMarketData, selectedTimeframe, symbol]", [13552, 13581], "[chartData, activeIndicators, addTechnicalIndicators]", [5191, 5199], "[fetchMarketIntelligence, symbol]", [5709, 5711], "[fetchAvailablePairs]", [12862, 12864], [5709, 5711], [10010, 10037], [13552, 13581], [6045, 6047]]