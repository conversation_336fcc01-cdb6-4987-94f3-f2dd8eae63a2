{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\AutoTradz AI\\\\AutoTradz-AI\\\\frontend\\\\src\\\\RiskDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n_c = DashboardContainer;\nconst DashboardHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c2 = DashboardHeader;\nconst RiskIcon = styled.div`\n  width: 20px;\n  height: 20px;\n  background: ${props => props.$status === 'critical' ? '#ff4976' : props.$status === 'warning' ? '#ffa726' : '#4bffb5'};\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #000;\n  font-weight: bold;\n`;\n_c3 = RiskIcon;\nconst MetricGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 20px;\n`;\n_c4 = MetricGrid;\nconst MetricCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  border-left: 4px solid ${props => props.$status === 'critical' ? '#ff4976' : props.$status === 'warning' ? '#ffa726' : '#4bffb5'};\n`;\n_c5 = MetricCard;\nconst MetricLabel = styled.div`\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n  font-weight: 500;\n`;\n_c6 = MetricLabel;\nconst MetricValue = styled.div`\n  color: ${props => props.$status === 'critical' ? '#ff4976' : props.$status === 'warning' ? '#ffa726' : props.$positive ? '#4bffb5' : props.$negative ? '#ff4976' : '#ffffff'};\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 4px;\n`;\n_c7 = MetricValue;\nconst MetricSubtext = styled.div`\n  color: #666;\n  font-size: 10px;\n  line-height: 1.3;\n`;\n_c8 = MetricSubtext;\nconst AlertSection = styled.div`\n  background: ${props => props.$level === 'critical' ? 'rgba(255, 73, 118, 0.1)' : props.$level === 'warning' ? 'rgba(255, 167, 38, 0.1)' : 'rgba(75, 255, 181, 0.1)'};\n  border: 1px solid ${props => props.$level === 'critical' ? 'rgba(255, 73, 118, 0.3)' : props.$level === 'warning' ? 'rgba(255, 167, 38, 0.3)' : 'rgba(75, 255, 181, 0.3)'};\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 16px;\n`;\n_c9 = AlertSection;\nconst AlertTitle = styled.div`\n  color: ${props => props.$level === 'critical' ? '#ff4976' : props.$level === 'warning' ? '#ffa726' : '#4bffb5'};\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 4px;\n`;\n_c0 = AlertTitle;\nconst AlertText = styled.div`\n  color: #ccc;\n  font-size: 12px;\n  line-height: 1.4;\n`;\n_c1 = AlertText;\nconst ControlButton = styled.button`\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-right: 8px;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.critical {\n    background: #ff4976;\n    color: #fff;\n    \n    &:hover:not(:disabled) {\n      background: #e63946;\n    }\n  }\n  \n  &.warning {\n    background: #ffa726;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #ff9800;\n    }\n  }\n  \n  &.normal {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n    }\n  }\n`;\n_c10 = ControlButton;\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 6px;\n  background: #333;\n  border-radius: 3px;\n  overflow: hidden;\n  margin-top: 8px;\n\n  .fill {\n    height: 100%;\n    background: ${props => {\n  const value = isNaN(props.$value) ? 0 : props.$value;\n  return value > 80 ? '#ff4976' : value > 60 ? '#ffa726' : '#4bffb5';\n}};\n    width: ${props => {\n  const value = isNaN(props.$value) ? 0 : props.$value;\n  return Math.min(Math.max(value, 0), 100);\n}}%;\n    transition: width 0.3s ease;\n  }\n`;\n_c11 = ProgressBar;\nconst RiskDashboard = () => {\n  _s();\n  const [riskData, setRiskData] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  // Fetch risk data\n  useEffect(() => {\n    const fetchRiskData = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/api/institutional-risk-report');\n        if (response.ok) {\n          const data = await response.json();\n          setRiskData(data);\n          setLastUpdate(new Date());\n        }\n      } catch (error) {\n        console.error('Error fetching risk data:', error);\n      }\n    };\n\n    // Initial fetch\n    fetchRiskData();\n\n    // Update every 10 seconds\n    const interval = setInterval(fetchRiskData, 10000);\n    return () => clearInterval(interval);\n  }, []);\n  const resetCircuitBreaker = async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/reset-circuit-breaker', {\n        method: 'POST'\n      });\n      if (response.ok) {\n        alert('Circuit breaker reset successfully!');\n        // Refresh risk data\n        const riskResponse = await fetch('http://localhost:8000/api/institutional-risk-report');\n        if (riskResponse.ok) {\n          const data = await riskResponse.json();\n          setRiskData(data);\n        }\n      } else {\n        alert('Failed to reset circuit breaker');\n      }\n    } catch (error) {\n      console.error('Error resetting circuit breaker:', error);\n      alert('Error resetting circuit breaker');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  if (!riskData) {\n    return /*#__PURE__*/_jsxDEV(DashboardContainer, {\n      children: [/*#__PURE__*/_jsxDEV(DashboardHeader, {\n        children: [/*#__PURE__*/_jsxDEV(RiskIcon, {\n          children: \"\\uD83D\\uDEE1\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), \"Risk Management Dashboard\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#888',\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: \"Loading risk data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this);\n  }\n  const {\n    risk_limits,\n    current_status,\n    performance_metrics,\n    portfolio_metrics\n  } = riskData;\n\n  // Determine overall risk status\n  const circuitBreakerActive = current_status.circuit_breaker_active;\n  const consecutiveLosses = current_status.consecutive_losses;\n  const sharpeRatio = performance_metrics.sharpe_ratio;\n  let overallStatus = 'normal';\n  if (circuitBreakerActive || consecutiveLosses >= 5) {\n    overallStatus = 'critical';\n  } else if (consecutiveLosses >= 3 || sharpeRatio < -0.5) {\n    overallStatus = 'warning';\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardContainer, {\n    children: [/*#__PURE__*/_jsxDEV(DashboardHeader, {\n      children: [/*#__PURE__*/_jsxDEV(RiskIcon, {\n        $status: overallStatus,\n        children: \"\\uD83D\\uDEE1\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), \"Institutional Risk Management\", lastUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#666',\n          marginLeft: 'auto',\n          fontFamily: 'monospace'\n        },\n        children: [\"Updated: \", lastUpdate.toLocaleTimeString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), circuitBreakerActive && /*#__PURE__*/_jsxDEV(AlertSection, {\n      $level: \"critical\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n        $level: \"critical\",\n        children: \"\\uD83D\\uDEA8 CIRCUIT BREAKER ACTIVE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AlertText, {\n        children: \"Trading has been automatically halted due to risk limits being exceeded. Manual intervention required to resume trading.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(ControlButton, {\n          className: \"critical\",\n          onClick: resetCircuitBreaker,\n          disabled: isLoading,\n          children: isLoading ? 'Resetting...' : '🔄 Reset Circuit Breaker'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this), consecutiveLosses >= 3 && !circuitBreakerActive && /*#__PURE__*/_jsxDEV(AlertSection, {\n      $level: \"warning\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n        $level: \"warning\",\n        children: \"\\u26A0\\uFE0F HIGH CONSECUTIVE LOSSES\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AlertText, {\n        children: [consecutiveLosses, \" consecutive losing trades detected. Risk parameters have been automatically adjusted.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(MetricGrid, {\n      children: [/*#__PURE__*/_jsxDEV(MetricCard, {\n        $status: circuitBreakerActive ? 'critical' : 'normal',\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Circuit Breaker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          $status: circuitBreakerActive ? 'critical' : 'normal',\n          children: circuitBreakerActive ? 'ACTIVE' : 'NORMAL'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n          children: \"Emergency stop mechanism for extreme risk events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n        $status: consecutiveLosses >= 3 ? 'warning' : 'normal',\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Consecutive Losses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          $status: consecutiveLosses >= 3 ? 'warning' : 'normal',\n          children: consecutiveLosses\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n          children: [\"Max: \", performance_metrics.max_consecutive_losses, \" | Threshold: 5\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Sharpe Ratio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          $positive: sharpeRatio > 1,\n          $negative: sharpeRatio < 0,\n          children: sharpeRatio.toFixed(2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n          children: \"Risk-adjusted return measure (annualized)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricCard, {\n        children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n          children: \"Sortino Ratio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n          $positive: performance_metrics.sortino_ratio > 1,\n          $negative: performance_metrics.sortino_ratio < 0,\n          children: performance_metrics.sortino_ratio.toFixed(2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n          children: \"Downside risk-adjusted returns\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: '#888',\n          fontSize: '14px',\n          marginBottom: '12px'\n        },\n        children: \"\\uD83C\\uDFAF Risk Limits & Current Exposure\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Max Position Size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            children: [(risk_limits.max_position_size * 100).toFixed(1), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            $value: current_status.max_position_size * 100,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fill\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n            children: [\"Current: \", (current_status.max_position_size * 100).toFixed(1), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Risk Per Trade\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            children: [(risk_limits.risk_per_trade * 100).toFixed(2), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            $value: current_status.risk_per_trade * 100,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fill\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n            children: [\"Current: \", (current_status.risk_per_trade * 100).toFixed(2), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Max Drawdown Limit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            children: [(risk_limits.max_drawdown * 100).toFixed(1), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            $value: Math.abs(portfolio_metrics.unrealized_pnl) / portfolio_metrics.total_value * 100,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fill\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n            children: [\"Current DD: \", (Math.abs(portfolio_metrics.unrealized_pnl) / portfolio_metrics.total_value * 100).toFixed(1), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Volatility Adjustment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            children: [(current_status.volatility_adjustment * 100).toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            $value: current_status.volatility_adjustment * 100,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fill\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricSubtext, {\n            children: \"Market volatility scaling factor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: '#888',\n          fontSize: '14px',\n          marginBottom: '12px'\n        },\n        children: \"\\uD83D\\uDCBC Portfolio Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(4, 1fr)',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#2a2a2a',\n            padding: '12px',\n            borderRadius: '6px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Total Value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            children: [\"$\", portfolio_metrics.total_value.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#2a2a2a',\n            padding: '12px',\n            borderRadius: '6px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Cash Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            children: [\"$\", portfolio_metrics.cash.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#2a2a2a',\n            padding: '12px',\n            borderRadius: '6px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Active Positions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            children: portfolio_metrics.active_positions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#2a2a2a',\n            padding: '12px',\n            borderRadius: '6px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(MetricLabel, {\n            children: \"Sizing Method\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n            style: {\n              fontSize: '12px'\n            },\n            children: current_status.sizing_method.replace('_', ' ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AlertSection, {\n      $level: \"normal\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n        $level: \"normal\",\n        children: \"\\uD83D\\uDCCA System Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AlertText, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '16px',\n            marginTop: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Risk Engine:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), \" \", circuitBreakerActive ? 'HALTED' : 'ACTIVE', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 89\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Recent Trades:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this), \" \", performance_metrics.recent_trades, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 82\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Max Daily Loss:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this), \" \", (risk_limits.max_daily_loss * 100).toFixed(1), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Confidence Threshold:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), \" \", (current_status.confidence_threshold * 100).toFixed(0), \"%\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 111\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Max Leverage:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), \" \", risk_limits.max_leverage, \"x\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 73\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Correlation Limit:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), \" \", (risk_limits.max_correlation * 100).toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s(RiskDashboard, \"TTOasbD5KmddyQb0UAN5cjUI8zA=\");\n_c12 = RiskDashboard;\nexport default RiskDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"DashboardContainer\");\n$RefreshReg$(_c2, \"DashboardHeader\");\n$RefreshReg$(_c3, \"RiskIcon\");\n$RefreshReg$(_c4, \"MetricGrid\");\n$RefreshReg$(_c5, \"MetricCard\");\n$RefreshReg$(_c6, \"MetricLabel\");\n$RefreshReg$(_c7, \"MetricValue\");\n$RefreshReg$(_c8, \"MetricSubtext\");\n$RefreshReg$(_c9, \"AlertSection\");\n$RefreshReg$(_c0, \"AlertTitle\");\n$RefreshReg$(_c1, \"AlertText\");\n$RefreshReg$(_c10, \"ControlButton\");\n$RefreshReg$(_c11, \"ProgressBar\");\n$RefreshReg$(_c12, \"RiskDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "jsxDEV", "_jsxDEV", "DashboardContainer", "div", "_c", "DashboardHeader", "h3", "_c2", "RiskIcon", "props", "$status", "_c3", "MetricGrid", "_c4", "MetricCard", "_c5", "MetricLabel", "_c6", "MetricValue", "$positive", "$negative", "_c7", "MetricSubtext", "_c8", "AlertSection", "$level", "_c9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c0", "AlertText", "_c1", "ControlButton", "button", "_c10", "ProgressBar", "value", "isNaN", "$value", "Math", "min", "max", "_c11", "RiskDashboard", "_s", "riskData", "setRiskData", "isLoading", "setIsLoading", "lastUpdate", "setLastUpdate", "fetchRiskData", "response", "fetch", "ok", "data", "json", "Date", "error", "console", "interval", "setInterval", "clearInterval", "resetCircuitBreaker", "method", "alert", "riskResponse", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "textAlign", "padding", "risk_limits", "current_status", "performance_metrics", "portfolio_metrics", "circuitBreakerActive", "circuit_breaker_active", "consecutiveLosses", "consecutive_losses", "sharpeRatio", "sharpe_ratio", "overallStatus", "fontSize", "marginLeft", "fontFamily", "toLocaleTimeString", "marginTop", "className", "onClick", "disabled", "max_consecutive_losses", "toFixed", "sortino_ratio", "marginBottom", "display", "gridTemplateColumns", "gap", "max_position_size", "risk_per_trade", "max_drawdown", "abs", "unrealized_pnl", "total_value", "volatility_adjustment", "background", "borderRadius", "cash", "active_positions", "sizing_method", "replace", "recent_trades", "max_daily_loss", "confidence_threshold", "max_leverage", "max_correlation", "_c12", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/src/RiskDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\nconst DashboardContainer = styled.div`\n  background: #1a1a1a;\n  border-radius: 8px;\n  padding: 20px;\n  color: #ffffff;\n  border: 1px solid #333;\n  margin-bottom: 20px;\n`;\n\nconst DashboardHeader = styled.h3`\n  margin: 0 0 20px 0;\n  color: #4bffb5;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst RiskIcon = styled.div`\n  width: 20px;\n  height: 20px;\n  background: ${props => props.$status === 'critical' ? '#ff4976' :\n                       props.$status === 'warning' ? '#ffa726' : '#4bffb5'};\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #000;\n  font-weight: bold;\n`;\n\nconst MetricGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 20px;\n`;\n\nconst MetricCard = styled.div`\n  background: #2a2a2a;\n  border-radius: 6px;\n  padding: 16px;\n  border-left: 4px solid ${props =>\n    props.$status === 'critical' ? '#ff4976' :\n    props.$status === 'warning' ? '#ffa726' : '#4bffb5'\n  };\n`;\n\nconst MetricLabel = styled.div`\n  color: #888;\n  font-size: 12px;\n  margin-bottom: 4px;\n  font-weight: 500;\n`;\n\nconst MetricValue = styled.div`\n  color: ${props =>\n    props.$status === 'critical' ? '#ff4976' :\n    props.$status === 'warning' ? '#ffa726' :\n    props.$positive ? '#4bffb5' :\n    props.$negative ? '#ff4976' : '#ffffff'\n  };\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 4px;\n`;\n\nconst MetricSubtext = styled.div`\n  color: #666;\n  font-size: 10px;\n  line-height: 1.3;\n`;\n\nconst AlertSection = styled.div`\n  background: ${props =>\n    props.$level === 'critical' ? 'rgba(255, 73, 118, 0.1)' :\n    props.$level === 'warning' ? 'rgba(255, 167, 38, 0.1)' :\n    'rgba(75, 255, 181, 0.1)'\n  };\n  border: 1px solid ${props =>\n    props.$level === 'critical' ? 'rgba(255, 73, 118, 0.3)' :\n    props.$level === 'warning' ? 'rgba(255, 167, 38, 0.3)' :\n    'rgba(75, 255, 181, 0.3)'\n  };\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 16px;\n`;\n\nconst AlertTitle = styled.div`\n  color: ${props =>\n    props.$level === 'critical' ? '#ff4976' :\n    props.$level === 'warning' ? '#ffa726' : '#4bffb5'\n  };\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 4px;\n`;\n\nconst AlertText = styled.div`\n  color: #ccc;\n  font-size: 12px;\n  line-height: 1.4;\n`;\n\nconst ControlButton = styled.button`\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-right: 8px;\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &.critical {\n    background: #ff4976;\n    color: #fff;\n    \n    &:hover:not(:disabled) {\n      background: #e63946;\n    }\n  }\n  \n  &.warning {\n    background: #ffa726;\n    color: #000;\n    \n    &:hover:not(:disabled) {\n      background: #ff9800;\n    }\n  }\n  \n  &.normal {\n    background: #2a2a2a;\n    color: #fff;\n    border: 1px solid #444;\n    \n    &:hover:not(:disabled) {\n      background: #333;\n    }\n  }\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 6px;\n  background: #333;\n  border-radius: 3px;\n  overflow: hidden;\n  margin-top: 8px;\n\n  .fill {\n    height: 100%;\n    background: ${props => {\n      const value = isNaN(props.$value) ? 0 : props.$value;\n      return value > 80 ? '#ff4976' :\n             value > 60 ? '#ffa726' : '#4bffb5';\n    }};\n    width: ${props => {\n      const value = isNaN(props.$value) ? 0 : props.$value;\n      return Math.min(Math.max(value, 0), 100);\n    }}%;\n    transition: width 0.3s ease;\n  }\n`;\n\nconst RiskDashboard = () => {\n  const [riskData, setRiskData] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  // Fetch risk data\n  useEffect(() => {\n    const fetchRiskData = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/api/institutional-risk-report');\n        if (response.ok) {\n          const data = await response.json();\n          setRiskData(data);\n          setLastUpdate(new Date());\n        }\n      } catch (error) {\n        console.error('Error fetching risk data:', error);\n      }\n    };\n\n    // Initial fetch\n    fetchRiskData();\n\n    // Update every 10 seconds\n    const interval = setInterval(fetchRiskData, 10000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const resetCircuitBreaker = async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/reset-circuit-breaker', {\n        method: 'POST',\n      });\n      \n      if (response.ok) {\n        alert('Circuit breaker reset successfully!');\n        // Refresh risk data\n        const riskResponse = await fetch('http://localhost:8000/api/institutional-risk-report');\n        if (riskResponse.ok) {\n          const data = await riskResponse.json();\n          setRiskData(data);\n        }\n      } else {\n        alert('Failed to reset circuit breaker');\n      }\n    } catch (error) {\n      console.error('Error resetting circuit breaker:', error);\n      alert('Error resetting circuit breaker');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!riskData) {\n    return (\n      <DashboardContainer>\n        <DashboardHeader>\n          <RiskIcon>🛡️</RiskIcon>\n          Risk Management Dashboard\n        </DashboardHeader>\n        <div style={{ color: '#888', textAlign: 'center', padding: '20px' }}>\n          Loading risk data...\n        </div>\n      </DashboardContainer>\n    );\n  }\n\n  const { risk_limits, current_status, performance_metrics, portfolio_metrics } = riskData;\n  \n  // Determine overall risk status\n  const circuitBreakerActive = current_status.circuit_breaker_active;\n  const consecutiveLosses = current_status.consecutive_losses;\n  const sharpeRatio = performance_metrics.sharpe_ratio;\n  \n  let overallStatus = 'normal';\n  if (circuitBreakerActive || consecutiveLosses >= 5) {\n    overallStatus = 'critical';\n  } else if (consecutiveLosses >= 3 || sharpeRatio < -0.5) {\n    overallStatus = 'warning';\n  }\n\n  return (\n    <DashboardContainer>\n      <DashboardHeader>\n        <RiskIcon $status={overallStatus}>🛡️</RiskIcon>\n        Institutional Risk Management\n        {lastUpdate && (\n          <div style={{ \n            fontSize: '10px', \n            color: '#666', \n            marginLeft: 'auto',\n            fontFamily: 'monospace'\n          }}>\n            Updated: {lastUpdate.toLocaleTimeString()}\n          </div>\n        )}\n      </DashboardHeader>\n\n      {/* Critical Alerts */}\n      {circuitBreakerActive && (\n        <AlertSection $level=\"critical\">\n          <AlertTitle $level=\"critical\">🚨 CIRCUIT BREAKER ACTIVE</AlertTitle>\n          <AlertText>\n            Trading has been automatically halted due to risk limits being exceeded. \n            Manual intervention required to resume trading.\n          </AlertText>\n          <div style={{ marginTop: '8px' }}>\n            <ControlButton \n              className=\"critical\"\n              onClick={resetCircuitBreaker}\n              disabled={isLoading}\n            >\n              {isLoading ? 'Resetting...' : '🔄 Reset Circuit Breaker'}\n            </ControlButton>\n          </div>\n        </AlertSection>\n      )}\n\n      {/* Warning Alerts */}\n      {consecutiveLosses >= 3 && !circuitBreakerActive && (\n        <AlertSection $level=\"warning\">\n          <AlertTitle $level=\"warning\">⚠️ HIGH CONSECUTIVE LOSSES</AlertTitle>\n          <AlertText>\n            {consecutiveLosses} consecutive losing trades detected. Risk parameters have been automatically adjusted.\n          </AlertText>\n        </AlertSection>\n      )}\n\n      {/* Risk Metrics Grid */}\n      <MetricGrid>\n        <MetricCard $status={circuitBreakerActive ? 'critical' : 'normal'}>\n          <MetricLabel>Circuit Breaker</MetricLabel>\n          <MetricValue $status={circuitBreakerActive ? 'critical' : 'normal'}>\n            {circuitBreakerActive ? 'ACTIVE' : 'NORMAL'}\n          </MetricValue>\n          <MetricSubtext>\n            Emergency stop mechanism for extreme risk events\n          </MetricSubtext>\n        </MetricCard>\n\n        <MetricCard $status={consecutiveLosses >= 3 ? 'warning' : 'normal'}>\n          <MetricLabel>Consecutive Losses</MetricLabel>\n          <MetricValue $status={consecutiveLosses >= 3 ? 'warning' : 'normal'}>\n            {consecutiveLosses}\n          </MetricValue>\n          <MetricSubtext>\n            Max: {performance_metrics.max_consecutive_losses} | Threshold: 5\n          </MetricSubtext>\n        </MetricCard>\n\n        <MetricCard>\n          <MetricLabel>Sharpe Ratio</MetricLabel>\n          <MetricValue\n            $positive={sharpeRatio > 1}\n            $negative={sharpeRatio < 0}\n          >\n            {sharpeRatio.toFixed(2)}\n          </MetricValue>\n          <MetricSubtext>\n            Risk-adjusted return measure (annualized)\n          </MetricSubtext>\n        </MetricCard>\n\n        <MetricCard>\n          <MetricLabel>Sortino Ratio</MetricLabel>\n          <MetricValue\n            $positive={performance_metrics.sortino_ratio > 1}\n            $negative={performance_metrics.sortino_ratio < 0}\n          >\n            {performance_metrics.sortino_ratio.toFixed(2)}\n          </MetricValue>\n          <MetricSubtext>\n            Downside risk-adjusted returns\n          </MetricSubtext>\n        </MetricCard>\n      </MetricGrid>\n\n      {/* Risk Limits Section */}\n      <div style={{ marginBottom: '20px' }}>\n        <h4 style={{ color: '#888', fontSize: '14px', marginBottom: '12px' }}>\n          🎯 Risk Limits & Current Exposure\n        </h4>\n\n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>\n          <div>\n            <MetricLabel>Max Position Size</MetricLabel>\n            <MetricValue>{(risk_limits.max_position_size * 100).toFixed(1)}%</MetricValue>\n            <ProgressBar $value={current_status.max_position_size * 100}>\n              <div className=\"fill\" />\n            </ProgressBar>\n            <MetricSubtext>Current: {(current_status.max_position_size * 100).toFixed(1)}%</MetricSubtext>\n          </div>\n\n          <div>\n            <MetricLabel>Risk Per Trade</MetricLabel>\n            <MetricValue>{(risk_limits.risk_per_trade * 100).toFixed(2)}%</MetricValue>\n            <ProgressBar $value={current_status.risk_per_trade * 100}>\n              <div className=\"fill\" />\n            </ProgressBar>\n            <MetricSubtext>Current: {(current_status.risk_per_trade * 100).toFixed(2)}%</MetricSubtext>\n          </div>\n\n          <div>\n            <MetricLabel>Max Drawdown Limit</MetricLabel>\n            <MetricValue>{(risk_limits.max_drawdown * 100).toFixed(1)}%</MetricValue>\n            <ProgressBar $value={Math.abs(portfolio_metrics.unrealized_pnl) / portfolio_metrics.total_value * 100}>\n              <div className=\"fill\" />\n            </ProgressBar>\n            <MetricSubtext>\n              Current DD: {(Math.abs(portfolio_metrics.unrealized_pnl) / portfolio_metrics.total_value * 100).toFixed(1)}%\n            </MetricSubtext>\n          </div>\n\n          <div>\n            <MetricLabel>Volatility Adjustment</MetricLabel>\n            <MetricValue>{(current_status.volatility_adjustment * 100).toFixed(0)}%</MetricValue>\n            <ProgressBar $value={current_status.volatility_adjustment * 100}>\n              <div className=\"fill\" />\n            </ProgressBar>\n            <MetricSubtext>Market volatility scaling factor</MetricSubtext>\n          </div>\n        </div>\n      </div>\n\n      {/* Portfolio Status */}\n      <div style={{ marginBottom: '20px' }}>\n        <h4 style={{ color: '#888', fontSize: '14px', marginBottom: '12px' }}>\n          💼 Portfolio Status\n        </h4>\n\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '12px' }}>\n          <div style={{ background: '#2a2a2a', padding: '12px', borderRadius: '6px' }}>\n            <MetricLabel>Total Value</MetricLabel>\n            <MetricValue>${portfolio_metrics.total_value.toFixed(2)}</MetricValue>\n          </div>\n\n          <div style={{ background: '#2a2a2a', padding: '12px', borderRadius: '6px' }}>\n            <MetricLabel>Cash Available</MetricLabel>\n            <MetricValue>${portfolio_metrics.cash.toFixed(2)}</MetricValue>\n          </div>\n\n          <div style={{ background: '#2a2a2a', padding: '12px', borderRadius: '6px' }}>\n            <MetricLabel>Active Positions</MetricLabel>\n            <MetricValue>{portfolio_metrics.active_positions}</MetricValue>\n          </div>\n\n          <div style={{ background: '#2a2a2a', padding: '12px', borderRadius: '6px' }}>\n            <MetricLabel>Sizing Method</MetricLabel>\n            <MetricValue style={{ fontSize: '12px' }}>\n              {current_status.sizing_method.replace('_', ' ')}\n            </MetricValue>\n          </div>\n        </div>\n      </div>\n\n      {/* System Status */}\n      <AlertSection $level=\"normal\">\n        <AlertTitle $level=\"normal\">📊 System Status</AlertTitle>\n        <AlertText>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginTop: '8px' }}>\n            <div>\n              <strong>Risk Engine:</strong> {circuitBreakerActive ? 'HALTED' : 'ACTIVE'}<br />\n              <strong>Recent Trades:</strong> {performance_metrics.recent_trades}<br />\n              <strong>Max Daily Loss:</strong> {(risk_limits.max_daily_loss * 100).toFixed(1)}%\n            </div>\n            <div>\n              <strong>Confidence Threshold:</strong> {(current_status.confidence_threshold * 100).toFixed(0)}%<br />\n              <strong>Max Leverage:</strong> {risk_limits.max_leverage}x<br />\n              <strong>Correlation Limit:</strong> {(risk_limits.max_correlation * 100).toFixed(0)}%\n            </div>\n          </div>\n        </AlertText>\n      </AlertSection>\n    </DashboardContainer>\n  );\n};\n\nexport default RiskDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,kBAAkB,GAAGH,MAAM,CAACI,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,kBAAkB;AASxB,MAAMG,eAAe,GAAGN,MAAM,CAACO,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,eAAe;AASrB,MAAMG,QAAQ,GAAGT,MAAM,CAACI,GAAG;AAC3B;AACA;AACA,gBAAgBM,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,UAAU,GAAG,SAAS,GAC1CD,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,QAAQ;AAcd,MAAMI,UAAU,GAAGb,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,UAAU;AAOhB,MAAME,UAAU,GAAGf,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,2BAA2BM,KAAK,IAC5BA,KAAK,CAACC,OAAO,KAAK,UAAU,GAAG,SAAS,GACxCD,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AACvD,CACC;AAACK,GAAA,GARID,UAAU;AAUhB,MAAME,WAAW,GAAGjB,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GALID,WAAW;AAOjB,MAAME,WAAW,GAAGnB,MAAM,CAACI,GAAG;AAC9B,WAAWM,KAAK,IACZA,KAAK,CAACC,OAAO,KAAK,UAAU,GAAG,SAAS,GACxCD,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GACvCD,KAAK,CAACU,SAAS,GAAG,SAAS,GAC3BV,KAAK,CAACW,SAAS,GAAG,SAAS,GAAG,SAAS;AAC3C;AACA;AACA;AACA,CACC;AAACC,GAAA,GAVIH,WAAW;AAYjB,MAAMI,aAAa,GAAGvB,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGzB,MAAM,CAACI,GAAG;AAC/B,gBAAgBM,KAAK,IACjBA,KAAK,CAACgB,MAAM,KAAK,UAAU,GAAG,yBAAyB,GACvDhB,KAAK,CAACgB,MAAM,KAAK,SAAS,GAAG,yBAAyB,GACtD,yBAAyB;AAC7B,sBACsBhB,KAAK,IACvBA,KAAK,CAACgB,MAAM,KAAK,UAAU,GAAG,yBAAyB,GACvDhB,KAAK,CAACgB,MAAM,KAAK,SAAS,GAAG,yBAAyB,GACtD,yBAAyB;AAC7B;AACA;AACA;AACA,CACC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,UAAU,GAAG5B,MAAM,CAACI,GAAG;AAC7B,WAAWM,KAAK,IACZA,KAAK,CAACgB,MAAM,KAAK,UAAU,GAAG,SAAS,GACvChB,KAAK,CAACgB,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AACtD;AACA;AACA;AACA,CACC;AAACG,GAAA,GARID,UAAU;AAUhB,MAAME,SAAS,GAAG9B,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GAJID,SAAS;AAMf,MAAME,aAAa,GAAGhC,MAAM,CAACiC,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA1CIF,aAAa;AA4CnB,MAAMG,WAAW,GAAGnC,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBM,KAAK,IAAI;EACrB,MAAM0B,KAAK,GAAGC,KAAK,CAAC3B,KAAK,CAAC4B,MAAM,CAAC,GAAG,CAAC,GAAG5B,KAAK,CAAC4B,MAAM;EACpD,OAAOF,KAAK,GAAG,EAAE,GAAG,SAAS,GACtBA,KAAK,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS;AAC3C,CAAC;AACL,aAAa1B,KAAK,IAAI;EAChB,MAAM0B,KAAK,GAAGC,KAAK,CAAC3B,KAAK,CAAC4B,MAAM,CAAC,GAAG,CAAC,GAAG5B,KAAK,CAAC4B,MAAM;EACpD,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAC1C,CAAC;AACL;AACA;AACA,CAAC;AAACM,IAAA,GArBIP,WAAW;AAuBjB,MAAMQ,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qDAAqD,CAAC;QACnF,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClCV,WAAW,CAACS,IAAI,CAAC;UACjBL,aAAa,CAAC,IAAIO,IAAI,CAAC,CAAC,CAAC;QAC3B;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;;IAED;IACAP,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMS,QAAQ,GAAGC,WAAW,CAACV,aAAa,EAAE,KAAK,CAAC;IAClD,OAAO,MAAMW,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCf,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,iDAAiD,EAAE;QAC9EW,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAIZ,QAAQ,CAACE,EAAE,EAAE;QACfW,KAAK,CAAC,qCAAqC,CAAC;QAC5C;QACA,MAAMC,YAAY,GAAG,MAAMb,KAAK,CAAC,qDAAqD,CAAC;QACvF,IAAIa,YAAY,CAACZ,EAAE,EAAE;UACnB,MAAMC,IAAI,GAAG,MAAMW,YAAY,CAACV,IAAI,CAAC,CAAC;UACtCV,WAAW,CAACS,IAAI,CAAC;QACnB;MACF,CAAC,MAAM;QACLU,KAAK,CAAC,iCAAiC,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDO,KAAK,CAAC,iCAAiC,CAAC;IAC1C,CAAC,SAAS;MACRjB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,IAAI,CAACH,QAAQ,EAAE;IACb,oBACE3C,OAAA,CAACC,kBAAkB;MAAAgE,QAAA,gBACjBjE,OAAA,CAACI,eAAe;QAAA6D,QAAA,gBACdjE,OAAA,CAACO,QAAQ;UAAA0D,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,6BAE1B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAClBrE,OAAA;QAAKsE,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAEzB;EAEA,MAAM;IAAEK,WAAW;IAAEC,cAAc;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAAGlC,QAAQ;;EAExF;EACA,MAAMmC,oBAAoB,GAAGH,cAAc,CAACI,sBAAsB;EAClE,MAAMC,iBAAiB,GAAGL,cAAc,CAACM,kBAAkB;EAC3D,MAAMC,WAAW,GAAGN,mBAAmB,CAACO,YAAY;EAEpD,IAAIC,aAAa,GAAG,QAAQ;EAC5B,IAAIN,oBAAoB,IAAIE,iBAAiB,IAAI,CAAC,EAAE;IAClDI,aAAa,GAAG,UAAU;EAC5B,CAAC,MAAM,IAAIJ,iBAAiB,IAAI,CAAC,IAAIE,WAAW,GAAG,CAAC,GAAG,EAAE;IACvDE,aAAa,GAAG,SAAS;EAC3B;EAEA,oBACEpF,OAAA,CAACC,kBAAkB;IAAAgE,QAAA,gBACjBjE,OAAA,CAACI,eAAe;MAAA6D,QAAA,gBACdjE,OAAA,CAACO,QAAQ;QAACE,OAAO,EAAE2E,aAAc;QAAAnB,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,iCAEhD,EAACtB,UAAU,iBACT/C,OAAA;QAAKsE,KAAK,EAAE;UACVe,QAAQ,EAAE,MAAM;UAChBd,KAAK,EAAE,MAAM;UACbe,UAAU,EAAE,MAAM;UAClBC,UAAU,EAAE;QACd,CAAE;QAAAtB,QAAA,GAAC,WACQ,EAAClB,UAAU,CAACyC,kBAAkB,CAAC,CAAC;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,EAGjBS,oBAAoB,iBACnB9E,OAAA,CAACuB,YAAY;MAACC,MAAM,EAAC,UAAU;MAAAyC,QAAA,gBAC7BjE,OAAA,CAAC0B,UAAU;QAACF,MAAM,EAAC,UAAU;QAAAyC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpErE,OAAA,CAAC4B,SAAS;QAAAqC,QAAA,EAAC;MAGX;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZrE,OAAA;QAAKsE,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAM,CAAE;QAAAxB,QAAA,eAC/BjE,OAAA,CAAC8B,aAAa;UACZ4D,SAAS,EAAC,UAAU;UACpBC,OAAO,EAAE9B,mBAAoB;UAC7B+B,QAAQ,EAAE/C,SAAU;UAAAoB,QAAA,EAEnBpB,SAAS,GAAG,cAAc,GAAG;QAA0B;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACf,EAGAW,iBAAiB,IAAI,CAAC,IAAI,CAACF,oBAAoB,iBAC9C9E,OAAA,CAACuB,YAAY;MAACC,MAAM,EAAC,SAAS;MAAAyC,QAAA,gBAC5BjE,OAAA,CAAC0B,UAAU;QAACF,MAAM,EAAC,SAAS;QAAAyC,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpErE,OAAA,CAAC4B,SAAS;QAAAqC,QAAA,GACPe,iBAAiB,EAAC,wFACrB;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACf,eAGDrE,OAAA,CAACW,UAAU;MAAAsD,QAAA,gBACTjE,OAAA,CAACa,UAAU;QAACJ,OAAO,EAAEqE,oBAAoB,GAAG,UAAU,GAAG,QAAS;QAAAb,QAAA,gBAChEjE,OAAA,CAACe,WAAW;UAAAkD,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1CrE,OAAA,CAACiB,WAAW;UAACR,OAAO,EAAEqE,oBAAoB,GAAG,UAAU,GAAG,QAAS;UAAAb,QAAA,EAChEa,oBAAoB,GAAG,QAAQ,GAAG;QAAQ;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACdrE,OAAA,CAACqB,aAAa;UAAA4C,QAAA,EAAC;QAEf;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEbrE,OAAA,CAACa,UAAU;QAACJ,OAAO,EAAEuE,iBAAiB,IAAI,CAAC,GAAG,SAAS,GAAG,QAAS;QAAAf,QAAA,gBACjEjE,OAAA,CAACe,WAAW;UAAAkD,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC7CrE,OAAA,CAACiB,WAAW;UAACR,OAAO,EAAEuE,iBAAiB,IAAI,CAAC,GAAG,SAAS,GAAG,QAAS;UAAAf,QAAA,EACjEe;QAAiB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACdrE,OAAA,CAACqB,aAAa;UAAA4C,QAAA,GAAC,OACR,EAACW,mBAAmB,CAACiB,sBAAsB,EAAC,iBACnD;QAAA;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEbrE,OAAA,CAACa,UAAU;QAAAoD,QAAA,gBACTjE,OAAA,CAACe,WAAW;UAAAkD,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvCrE,OAAA,CAACiB,WAAW;UACVC,SAAS,EAAEgE,WAAW,GAAG,CAAE;UAC3B/D,SAAS,EAAE+D,WAAW,GAAG,CAAE;UAAAjB,QAAA,EAE1BiB,WAAW,CAACY,OAAO,CAAC,CAAC;QAAC;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACdrE,OAAA,CAACqB,aAAa;UAAA4C,QAAA,EAAC;QAEf;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEbrE,OAAA,CAACa,UAAU;QAAAoD,QAAA,gBACTjE,OAAA,CAACe,WAAW;UAAAkD,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxCrE,OAAA,CAACiB,WAAW;UACVC,SAAS,EAAE0D,mBAAmB,CAACmB,aAAa,GAAG,CAAE;UACjD5E,SAAS,EAAEyD,mBAAmB,CAACmB,aAAa,GAAG,CAAE;UAAA9B,QAAA,EAEhDW,mBAAmB,CAACmB,aAAa,CAACD,OAAO,CAAC,CAAC;QAAC;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACdrE,OAAA,CAACqB,aAAa;UAAA4C,QAAA,EAAC;QAEf;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGbrE,OAAA;MAAKsE,KAAK,EAAE;QAAE0B,YAAY,EAAE;MAAO,CAAE;MAAA/B,QAAA,gBACnCjE,OAAA;QAAIsE,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEc,QAAQ,EAAE,MAAM;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAA/B,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELrE,OAAA;QAAKsE,KAAK,EAAE;UAAE2B,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAlC,QAAA,gBAC3EjE,OAAA;UAAAiE,QAAA,gBACEjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC5CrE,OAAA,CAACiB,WAAW;YAAAgD,QAAA,GAAE,CAACS,WAAW,CAAC0B,iBAAiB,GAAG,GAAG,EAAEN,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9ErE,OAAA,CAACiC,WAAW;YAACG,MAAM,EAAEuC,cAAc,CAACyB,iBAAiB,GAAG,GAAI;YAAAnC,QAAA,eAC1DjE,OAAA;cAAK0F,SAAS,EAAC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACdrE,OAAA,CAACqB,aAAa;YAAA4C,QAAA,GAAC,WAAS,EAAC,CAACU,cAAc,CAACyB,iBAAiB,GAAG,GAAG,EAAEN,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,eAENrE,OAAA;UAAAiE,QAAA,gBACEjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzCrE,OAAA,CAACiB,WAAW;YAAAgD,QAAA,GAAE,CAACS,WAAW,CAAC2B,cAAc,GAAG,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3ErE,OAAA,CAACiC,WAAW;YAACG,MAAM,EAAEuC,cAAc,CAAC0B,cAAc,GAAG,GAAI;YAAApC,QAAA,eACvDjE,OAAA;cAAK0F,SAAS,EAAC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACdrE,OAAA,CAACqB,aAAa;YAAA4C,QAAA,GAAC,WAAS,EAAC,CAACU,cAAc,CAAC0B,cAAc,GAAG,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eAENrE,OAAA;UAAAiE,QAAA,gBACEjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7CrE,OAAA,CAACiB,WAAW;YAAAgD,QAAA,GAAE,CAACS,WAAW,CAAC4B,YAAY,GAAG,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzErE,OAAA,CAACiC,WAAW;YAACG,MAAM,EAAEC,IAAI,CAACkE,GAAG,CAAC1B,iBAAiB,CAAC2B,cAAc,CAAC,GAAG3B,iBAAiB,CAAC4B,WAAW,GAAG,GAAI;YAAAxC,QAAA,eACpGjE,OAAA;cAAK0F,SAAS,EAAC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACdrE,OAAA,CAACqB,aAAa;YAAA4C,QAAA,GAAC,cACD,EAAC,CAAC5B,IAAI,CAACkE,GAAG,CAAC1B,iBAAiB,CAAC2B,cAAc,CAAC,GAAG3B,iBAAiB,CAAC4B,WAAW,GAAG,GAAG,EAAEX,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7G;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAENrE,OAAA;UAAAiE,QAAA,gBACEjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChDrE,OAAA,CAACiB,WAAW;YAAAgD,QAAA,GAAE,CAACU,cAAc,CAAC+B,qBAAqB,GAAG,GAAG,EAAEZ,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrFrE,OAAA,CAACiC,WAAW;YAACG,MAAM,EAAEuC,cAAc,CAAC+B,qBAAqB,GAAG,GAAI;YAAAzC,QAAA,eAC9DjE,OAAA;cAAK0F,SAAS,EAAC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACdrE,OAAA,CAACqB,aAAa;YAAA4C,QAAA,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA;MAAKsE,KAAK,EAAE;QAAE0B,YAAY,EAAE;MAAO,CAAE;MAAA/B,QAAA,gBACnCjE,OAAA;QAAIsE,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEc,QAAQ,EAAE,MAAM;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAA/B,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELrE,OAAA;QAAKsE,KAAK,EAAE;UAAE2B,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,gBAAgB;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAlC,QAAA,gBAClFjE,OAAA;UAAKsE,KAAK,EAAE;YAAEqC,UAAU,EAAE,SAAS;YAAElC,OAAO,EAAE,MAAM;YAAEmC,YAAY,EAAE;UAAM,CAAE;UAAA3C,QAAA,gBAC1EjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtCrE,OAAA,CAACiB,WAAW;YAAAgD,QAAA,GAAC,GAAC,EAACY,iBAAiB,CAAC4B,WAAW,CAACX,OAAO,CAAC,CAAC,CAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAENrE,OAAA;UAAKsE,KAAK,EAAE;YAAEqC,UAAU,EAAE,SAAS;YAAElC,OAAO,EAAE,MAAM;YAAEmC,YAAY,EAAE;UAAM,CAAE;UAAA3C,QAAA,gBAC1EjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzCrE,OAAA,CAACiB,WAAW;YAAAgD,QAAA,GAAC,GAAC,EAACY,iBAAiB,CAACgC,IAAI,CAACf,OAAO,CAAC,CAAC,CAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENrE,OAAA;UAAKsE,KAAK,EAAE;YAAEqC,UAAU,EAAE,SAAS;YAAElC,OAAO,EAAE,MAAM;YAAEmC,YAAY,EAAE;UAAM,CAAE;UAAA3C,QAAA,gBAC1EjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3CrE,OAAA,CAACiB,WAAW;YAAAgD,QAAA,EAAEY,iBAAiB,CAACiC;UAAgB;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAENrE,OAAA;UAAKsE,KAAK,EAAE;YAAEqC,UAAU,EAAE,SAAS;YAAElC,OAAO,EAAE,MAAM;YAAEmC,YAAY,EAAE;UAAM,CAAE;UAAA3C,QAAA,gBAC1EjE,OAAA,CAACe,WAAW;YAAAkD,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACxCrE,OAAA,CAACiB,WAAW;YAACqD,KAAK,EAAE;cAAEe,QAAQ,EAAE;YAAO,CAAE;YAAApB,QAAA,EACtCU,cAAc,CAACoC,aAAa,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG;UAAC;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA,CAACuB,YAAY;MAACC,MAAM,EAAC,QAAQ;MAAAyC,QAAA,gBAC3BjE,OAAA,CAAC0B,UAAU;QAACF,MAAM,EAAC,QAAQ;QAAAyC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACzDrE,OAAA,CAAC4B,SAAS;QAAAqC,QAAA,eACRjE,OAAA;UAAKsE,KAAK,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEC,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAEV,SAAS,EAAE;UAAM,CAAE;UAAAxB,QAAA,gBAC7FjE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAAiE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACS,oBAAoB,GAAG,QAAQ,GAAG,QAAQ,eAAC9E,OAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChFrE,OAAA;cAAAiE,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACO,mBAAmB,CAACqC,aAAa,eAACjH,OAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzErE,OAAA;cAAAiE,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAACK,WAAW,CAACwC,cAAc,GAAG,GAAG,EAAEpB,OAAO,CAAC,CAAC,CAAC,EAAC,GAClF;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAAiE,QAAA,EAAQ;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAACM,cAAc,CAACwC,oBAAoB,GAAG,GAAG,EAAErB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,eAAA9F,OAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtGrE,OAAA;cAAAiE,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACK,WAAW,CAAC0C,YAAY,EAAC,GAAC,eAAApH,OAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChErE,OAAA;cAAAiE,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAACK,WAAW,CAAC2C,eAAe,GAAG,GAAG,EAAEvB,OAAO,CAAC,CAAC,CAAC,EAAC,GACtF;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEzB,CAAC;AAAC3B,EAAA,CApRID,aAAa;AAAA6E,IAAA,GAAb7E,aAAa;AAsRnB,eAAeA,aAAa;AAAC,IAAAtC,EAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAQ,IAAA,EAAA8E,IAAA;AAAAC,YAAA,CAAApH,EAAA;AAAAoH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}