import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { createChart, ColorType } from 'lightweight-charts';

const ChartContainer = styled.div`
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #333;
  height: 600px;
  display: flex;
  flex-direction: column;
  position: relative;
`;

const ChartHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
`;

const Title = styled.h3`
  color: #4bffb5;
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &::before {
    content: '🧠';
    font-size: 20px;
  }
`;

const AISignalOverlay = styled.div`
  position: absolute;
  top: 60px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #333;
  min-width: 200px;
  z-index: 10;
`;

const SignalIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  
  .signal-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: ${props => {
      switch(props.signal) {
        case 'BUY': return '#4bffb5';
        case 'SELL': return '#ff4976';
        default: return '#ffa726';
      }
    }};
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const ConfidenceBar = styled.div`
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
  
  &::after {
    content: '';
    display: block;
    width: ${props => props.confidence * 100}%;
    height: 100%;
    background: ${props => props.confidence > 0.7 ? '#4bffb5' : props.confidence > 0.4 ? '#ffa726' : '#ff4976'};
    transition: width 0.3s ease;
  }
`;

const ChartWrapper = styled.div`
  flex: 1;
  position: relative;
`;

const AIEnhancedChart = ({ symbol = 'BTC/USD', timeframe = '1m' }) => {
  const chartContainerRef = useRef();
  const chart = useRef();
  const candlestickSeries = useRef();
  const volumeSeries = useRef();
  const aiSignalSeries = useRef();
  
  const [chartData, setChartData] = useState([]);
  const [aiSignals, setAiSignals] = useState([]);
  const [currentSignal, setCurrentSignal] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    chart.current = createChart(chartContainerRef.current, {
      layout: {
        background: { type: ColorType.Solid, color: 'transparent' },
        textColor: '#d1d4dc',
      },
      grid: {
        vertLines: { color: '#2B2B43' },
        horzLines: { color: '#2B2B43' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#485c7b',
      },
      timeScale: {
        borderColor: '#485c7b',
        timeVisible: true,
        secondsVisible: false,
      },
      watermark: {
        visible: true,
        fontSize: 24,
        horzAlign: 'center',
        vertAlign: 'center',
        color: 'rgba(171, 71, 188, 0.3)',
        text: 'AutoTradz AI',
      },
    });

    // Add candlestick series
    candlestickSeries.current = chart.current.addCandlestickSeries({
      upColor: '#4bffb5',
      downColor: '#ff4976',
      borderDownColor: '#ff4976',
      borderUpColor: '#4bffb5',
      wickDownColor: '#ff4976',
      wickUpColor: '#4bffb5',
    });

    // Add volume series
    volumeSeries.current = chart.current.addHistogramSeries({
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: '',
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
    });

    // Add AI signal markers series
    aiSignalSeries.current = chart.current.addLineSeries({
      color: 'transparent',
      lineWidth: 0,
      crosshairMarkerVisible: false,
      lastValueVisible: false,
      priceLineVisible: false,
    });

    return () => {
      if (chart.current) {
        chart.current.remove();
      }
    };
  }, []);

  // Fetch market data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/market-data?limit=200&timeframe=${timeframe}&pair=${encodeURIComponent(symbol)}`);
        const data = await response.json();
        
        if (data && data.length > 0) {
          const formattedData = data
            .filter(item => item && item.timestamp)
            .map(item => ({
              time: new Date(item.timestamp).getTime() / 1000,
              open: parseFloat(item.open),
              high: parseFloat(item.high),
              low: parseFloat(item.low),
              close: parseFloat(item.close),
              volume: parseFloat(item.volume || 0),
            }))
            .sort((a, b) => a.time - b.time);

          setChartData(formattedData);
          
          if (candlestickSeries.current && formattedData.length > 0) {
            candlestickSeries.current.setData(formattedData);
          }

          const volumeData = formattedData.map(item => ({
            time: item.time,
            value: item.volume,
            color: item.close >= item.open ? '#4bffb544' : '#ff497644',
          }));

          if (volumeSeries.current && volumeData.length > 0) {
            volumeSeries.current.setData(volumeData);
          }
        }
      } catch (error) {
        console.error('Failed to fetch chart data:', error);
      }
      setIsLoading(false);
    };

    fetchData();
    const interval = setInterval(fetchData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [symbol, timeframe]);

  // Fetch AI signals and current analysis
  useEffect(() => {
    const fetchAIData = async () => {
      try {
        // Fetch recent signals
        const signalsResponse = await fetch('/signals?limit=50');
        const signalsData = await signalsResponse.json();
        
        if (signalsData && signalsData.length > 0) {
          const formattedSignals = signalsData.map(signal => ({
            time: new Date(signal.timestamp).getTime() / 1000,
            signal: signal.signal,
            confidence: signal.confidence,
            price: signal.price,
            reasoning: signal.reasoning,
          }));
          setAiSignals(formattedSignals);
          
          // Add signal markers to chart
          if (aiSignalSeries.current) {
            const markers = formattedSignals.map(signal => ({
              time: signal.time,
              position: signal.signal === 'BUY' ? 'belowBar' : 'aboveBar',
              color: signal.signal === 'BUY' ? '#4bffb5' : signal.signal === 'SELL' ? '#ff4976' : '#ffa726',
              shape: signal.signal === 'BUY' ? 'arrowUp' : signal.signal === 'SELL' ? 'arrowDown' : 'circle',
              text: `${signal.signal} (${(signal.confidence * 100).toFixed(0)}%)`,
              size: 1,
            }));
            
            candlestickSeries.current.setMarkers(markers);
          }
        }

        // Fetch current AI analysis
        const analysisResponse = await fetch('/api/ai/current-analysis');
        if (analysisResponse.ok) {
          const analysisData = await analysisResponse.json();
          setCurrentSignal(analysisData.final_decision);
        } else {
          // Mock current signal for demo
          setCurrentSignal({
            signal: 'BUY',
            confidence: 0.76,
            reasoning: 'Strong bullish consensus across multiple strategies with favorable market regime detection.'
          });
        }
      } catch (error) {
        console.error('Failed to fetch AI data:', error);
        // Mock data for demo
        setCurrentSignal({
          signal: 'BUY',
          confidence: 0.76,
          reasoning: 'Strong bullish consensus across multiple strategies with favorable market regime detection.'
        });
      }
    };

    fetchAIData();
    const interval = setInterval(fetchAIData, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <ChartContainer>
      <ChartHeader>
        <Title>AI-Enhanced Trading Chart - {symbol}</Title>
        {isLoading && <div style={{ color: '#ffa726', fontSize: '12px' }}>Loading...</div>}
      </ChartHeader>
      
      <ChartWrapper>
        <div ref={chartContainerRef} style={{ width: '100%', height: '100%' }} />
        
        {currentSignal && (
          <AISignalOverlay>
            <div style={{ color: '#4bffb5', fontWeight: 'bold', marginBottom: '8px' }}>
              🧠 AI Analysis
            </div>
            
            <SignalIndicator signal={currentSignal.signal}>
              <div className="signal-dot"></div>
              <span style={{ color: '#fff', fontWeight: 'bold' }}>
                {currentSignal.signal}
              </span>
              <span style={{ color: '#888', marginLeft: 'auto' }}>
                {(currentSignal.confidence * 100).toFixed(0)}%
              </span>
            </SignalIndicator>
            
            <ConfidenceBar confidence={currentSignal.confidence} />
            
            <div style={{ 
              fontSize: '10px', 
              color: '#ccc', 
              marginTop: '8px',
              lineHeight: '1.3'
            }}>
              {currentSignal.reasoning}
            </div>
            
            <div style={{ 
              fontSize: '10px', 
              color: '#888', 
              marginTop: '8px',
              borderTop: '1px solid #333',
              paddingTop: '8px'
            }}>
              Signals: {aiSignals.length} | Last: {aiSignals.length > 0 ? new Date(aiSignals[aiSignals.length - 1].time * 1000).toLocaleTimeString() : 'N/A'}
            </div>
          </AISignalOverlay>
        )}
      </ChartWrapper>
    </ChartContainer>
  );
};

export default AIEnhancedChart;
