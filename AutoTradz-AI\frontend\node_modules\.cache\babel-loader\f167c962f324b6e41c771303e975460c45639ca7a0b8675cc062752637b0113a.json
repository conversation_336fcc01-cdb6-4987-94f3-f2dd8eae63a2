{"ast": null, "code": "import { equalSizes, size } from \"./size.mjs\";\nimport { createObservable as createDevicePixelRatioObservable } from \"./device-pixel-ratio.mjs\";\nvar DevicePixelContentBoxBinding = /** @class */function () {\n  function DevicePixelContentBoxBinding(canvasElement, transformBitmapSize, options) {\n    var _a;\n    this._canvasElement = null;\n    this._bitmapSizeChangedListeners = [];\n    this._suggestedBitmapSize = null;\n    this._suggestedBitmapSizeChangedListeners = [];\n    // devicePixelRatio approach\n    this._devicePixelRatioObservable = null;\n    // ResizeObserver approach\n    this._canvasElementResizeObserver = null;\n    this._canvasElement = canvasElement;\n    this._canvasElementClientSize = size({\n      width: this._canvasElement.clientWidth,\n      height: this._canvasElement.clientHeight\n    });\n    this._transformBitmapSize = transformBitmapSize !== null && transformBitmapSize !== void 0 ? transformBitmapSize : function (size) {\n      return size;\n    };\n    this._allowResizeObserver = (_a = options === null || options === void 0 ? void 0 : options.allowResizeObserver) !== null && _a !== void 0 ? _a : true;\n    this._chooseAndInitObserver();\n    // we MAY leave the constuctor without any bitmap size observation mechanics initialized\n  }\n  DevicePixelContentBoxBinding.prototype.dispose = function () {\n    var _a, _b;\n    if (this._canvasElement === null) {\n      throw new Error('Object is disposed');\n    }\n    (_a = this._canvasElementResizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n    this._canvasElementResizeObserver = null;\n    (_b = this._devicePixelRatioObservable) === null || _b === void 0 ? void 0 : _b.dispose();\n    this._devicePixelRatioObservable = null;\n    this._suggestedBitmapSizeChangedListeners.length = 0;\n    this._bitmapSizeChangedListeners.length = 0;\n    this._canvasElement = null;\n  };\n  Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"canvasElement\", {\n    get: function () {\n      if (this._canvasElement === null) {\n        throw new Error('Object is disposed');\n      }\n      return this._canvasElement;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"canvasElementClientSize\", {\n    get: function () {\n      return this._canvasElementClientSize;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"bitmapSize\", {\n    get: function () {\n      return size({\n        width: this.canvasElement.width,\n        height: this.canvasElement.height\n      });\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Use this function to change canvas element client size until binding is disposed\n   * @param clientSize New client size for bound HTMLCanvasElement\n   */\n  DevicePixelContentBoxBinding.prototype.resizeCanvasElement = function (clientSize) {\n    this._canvasElementClientSize = size(clientSize);\n    this.canvasElement.style.width = \"\".concat(this._canvasElementClientSize.width, \"px\");\n    this.canvasElement.style.height = \"\".concat(this._canvasElementClientSize.height, \"px\");\n    this._invalidateBitmapSize();\n  };\n  DevicePixelContentBoxBinding.prototype.subscribeBitmapSizeChanged = function (listener) {\n    this._bitmapSizeChangedListeners.push(listener);\n  };\n  DevicePixelContentBoxBinding.prototype.unsubscribeBitmapSizeChanged = function (listener) {\n    this._bitmapSizeChangedListeners = this._bitmapSizeChangedListeners.filter(function (l) {\n      return l !== listener;\n    });\n  };\n  Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"suggestedBitmapSize\", {\n    get: function () {\n      return this._suggestedBitmapSize;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  DevicePixelContentBoxBinding.prototype.subscribeSuggestedBitmapSizeChanged = function (listener) {\n    this._suggestedBitmapSizeChangedListeners.push(listener);\n  };\n  DevicePixelContentBoxBinding.prototype.unsubscribeSuggestedBitmapSizeChanged = function (listener) {\n    this._suggestedBitmapSizeChangedListeners = this._suggestedBitmapSizeChangedListeners.filter(function (l) {\n      return l !== listener;\n    });\n  };\n  DevicePixelContentBoxBinding.prototype.applySuggestedBitmapSize = function () {\n    if (this._suggestedBitmapSize === null) {\n      // nothing to apply\n      return;\n    }\n    var oldSuggestedSize = this._suggestedBitmapSize;\n    this._suggestedBitmapSize = null;\n    this._resizeBitmap(oldSuggestedSize);\n    this._emitSuggestedBitmapSizeChanged(oldSuggestedSize, this._suggestedBitmapSize);\n  };\n  DevicePixelContentBoxBinding.prototype._resizeBitmap = function (newSize) {\n    var oldSize = this.bitmapSize;\n    if (equalSizes(oldSize, newSize)) {\n      return;\n    }\n    this.canvasElement.width = newSize.width;\n    this.canvasElement.height = newSize.height;\n    this._emitBitmapSizeChanged(oldSize, newSize);\n  };\n  DevicePixelContentBoxBinding.prototype._emitBitmapSizeChanged = function (oldSize, newSize) {\n    var _this = this;\n    this._bitmapSizeChangedListeners.forEach(function (listener) {\n      return listener.call(_this, oldSize, newSize);\n    });\n  };\n  DevicePixelContentBoxBinding.prototype._suggestNewBitmapSize = function (newSize) {\n    var oldSuggestedSize = this._suggestedBitmapSize;\n    var finalNewSize = size(this._transformBitmapSize(newSize, this._canvasElementClientSize));\n    var newSuggestedSize = equalSizes(this.bitmapSize, finalNewSize) ? null : finalNewSize;\n    if (oldSuggestedSize === null && newSuggestedSize === null) {\n      return;\n    }\n    if (oldSuggestedSize !== null && newSuggestedSize !== null && equalSizes(oldSuggestedSize, newSuggestedSize)) {\n      return;\n    }\n    this._suggestedBitmapSize = newSuggestedSize;\n    this._emitSuggestedBitmapSizeChanged(oldSuggestedSize, newSuggestedSize);\n  };\n  DevicePixelContentBoxBinding.prototype._emitSuggestedBitmapSizeChanged = function (oldSize, newSize) {\n    var _this = this;\n    this._suggestedBitmapSizeChangedListeners.forEach(function (listener) {\n      return listener.call(_this, oldSize, newSize);\n    });\n  };\n  DevicePixelContentBoxBinding.prototype._chooseAndInitObserver = function () {\n    var _this = this;\n    if (!this._allowResizeObserver) {\n      this._initDevicePixelRatioObservable();\n      return;\n    }\n    isDevicePixelContentBoxSupported().then(function (isSupported) {\n      return isSupported ? _this._initResizeObserver() : _this._initDevicePixelRatioObservable();\n    });\n  };\n  // devicePixelRatio approach\n  DevicePixelContentBoxBinding.prototype._initDevicePixelRatioObservable = function () {\n    var _this = this;\n    if (this._canvasElement === null) {\n      // it looks like we are already dead\n      return;\n    }\n    var win = canvasElementWindow(this._canvasElement);\n    if (win === null) {\n      throw new Error('No window is associated with the canvas');\n    }\n    this._devicePixelRatioObservable = createDevicePixelRatioObservable(win);\n    this._devicePixelRatioObservable.subscribe(function () {\n      return _this._invalidateBitmapSize();\n    });\n    this._invalidateBitmapSize();\n  };\n  DevicePixelContentBoxBinding.prototype._invalidateBitmapSize = function () {\n    var _a, _b;\n    if (this._canvasElement === null) {\n      // it looks like we are already dead\n      return;\n    }\n    var win = canvasElementWindow(this._canvasElement);\n    if (win === null) {\n      return;\n    }\n    var ratio = (_b = (_a = this._devicePixelRatioObservable) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : win.devicePixelRatio;\n    var canvasRects = this._canvasElement.getClientRects();\n    var newSize =\n    // eslint-disable-next-line no-negated-condition\n    canvasRects[0] !== undefined ? predictedBitmapSize(canvasRects[0], ratio) : size({\n      width: this._canvasElementClientSize.width * ratio,\n      height: this._canvasElementClientSize.height * ratio\n    });\n    this._suggestNewBitmapSize(newSize);\n  };\n  // ResizeObserver approach\n  DevicePixelContentBoxBinding.prototype._initResizeObserver = function () {\n    var _this = this;\n    if (this._canvasElement === null) {\n      // it looks like we are already dead\n      return;\n    }\n    this._canvasElementResizeObserver = new ResizeObserver(function (entries) {\n      var entry = entries.find(function (entry) {\n        return entry.target === _this._canvasElement;\n      });\n      if (!entry || !entry.devicePixelContentBoxSize || !entry.devicePixelContentBoxSize[0]) {\n        return;\n      }\n      var entrySize = entry.devicePixelContentBoxSize[0];\n      var newSize = size({\n        width: entrySize.inlineSize,\n        height: entrySize.blockSize\n      });\n      _this._suggestNewBitmapSize(newSize);\n    });\n    this._canvasElementResizeObserver.observe(this._canvasElement, {\n      box: 'device-pixel-content-box'\n    });\n  };\n  return DevicePixelContentBoxBinding;\n}();\nexport function bindTo(canvasElement, target) {\n  if (target.type === 'device-pixel-content-box') {\n    return new DevicePixelContentBoxBinding(canvasElement, target.transform, target.options);\n  }\n  throw new Error('Unsupported binding target');\n}\nfunction canvasElementWindow(canvasElement) {\n  // According to DOM Level 2 Core specification, ownerDocument should never be null for HTMLCanvasElement\n  // see https://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#node-ownerDoc\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return canvasElement.ownerDocument.defaultView;\n}\nfunction isDevicePixelContentBoxSupported() {\n  return new Promise(function (resolve) {\n    var ro = new ResizeObserver(function (entries) {\n      resolve(entries.every(function (entry) {\n        return 'devicePixelContentBoxSize' in entry;\n      }));\n      ro.disconnect();\n    });\n    ro.observe(document.body, {\n      box: 'device-pixel-content-box'\n    });\n  }).catch(function () {\n    return false;\n  });\n}\nfunction predictedBitmapSize(canvasRect, ratio) {\n  return size({\n    width: Math.round(canvasRect.left * ratio + canvasRect.width * ratio) - Math.round(canvasRect.left * ratio),\n    height: Math.round(canvasRect.top * ratio + canvasRect.height * ratio) - Math.round(canvasRect.top * ratio)\n  });\n}", "map": {"version": 3, "names": ["equalSizes", "size", "createObservable", "createDevicePixelRatioObservable", "DevicePixelContentBoxBinding", "canvasElement", "transformBitmapSize", "options", "_a", "_canvasElement", "_bitmapSizeChangedListeners", "_suggestedBitmapSize", "_suggestedBitmapSizeChangedListeners", "_devicePixelRatioObservable", "_canvasElementResizeObserver", "_canvasElementClientSize", "width", "clientWidth", "height", "clientHeight", "_transformBitmapSize", "_allowResizeObserver", "allowResizeObserver", "_chooseAndInitObserver", "prototype", "dispose", "_b", "Error", "disconnect", "length", "Object", "defineProperty", "get", "enumerable", "configurable", "resizeCanvasElement", "clientSize", "style", "concat", "_invalidateBitmapSize", "subscribeBitmapSizeChanged", "listener", "push", "unsubscribeBitmapSizeChanged", "filter", "l", "subscribeSuggestedBitmapSizeChanged", "unsubscribeSuggestedBitmapSizeChanged", "applySuggestedBitmapSize", "oldSuggestedSize", "_resizeBitmap", "_emitSuggestedBitmapSizeChanged", "newSize", "oldSize", "bitmapSize", "_emitBitmapSizeChanged", "_this", "for<PERSON>ach", "call", "_suggestNewBitmapSize", "finalNewSize", "newSuggestedSize", "_initDevicePixelRatioObservable", "isDevicePixelContentBoxSupported", "then", "isSupported", "_initResizeObserver", "win", "canvasElementWindow", "subscribe", "ratio", "value", "devicePixelRatio", "canvasRects", "getClientRects", "undefined", "predictedBitmapSize", "ResizeObserver", "entries", "entry", "find", "target", "devicePixelContentBoxSize", "entrySize", "inlineSize", "blockSize", "observe", "box", "bindTo", "type", "transform", "ownerDocument", "defaultView", "Promise", "resolve", "ro", "every", "document", "body", "catch", "canvasRect", "Math", "round", "left", "top"], "sources": ["C:/Users/<USER>/Desktop/projects/AutoTradz AI/AutoTradz-AI/frontend/node_modules/fancy-canvas/canvas-element-bitmap-size.mjs"], "sourcesContent": ["import { equalSizes, size } from \"./size.mjs\";\nimport { createObservable as createDevicePixelRatioObservable } from \"./device-pixel-ratio.mjs\";\nvar DevicePixelContentBoxBinding = /** @class */ (function () {\n    function DevicePixelContentBoxBinding(canvasElement, transformBitmapSize, options) {\n        var _a;\n        this._canvasElement = null;\n        this._bitmapSizeChangedListeners = [];\n        this._suggestedBitmapSize = null;\n        this._suggestedBitmapSizeChangedListeners = [];\n        // devicePixelRatio approach\n        this._devicePixelRatioObservable = null;\n        // ResizeObserver approach\n        this._canvasElementResizeObserver = null;\n        this._canvasElement = canvasElement;\n        this._canvasElementClientSize = size({\n            width: this._canvasElement.clientWidth,\n            height: this._canvasElement.clientHeight,\n        });\n        this._transformBitmapSize = transformBitmapSize !== null && transformBitmapSize !== void 0 ? transformBitmapSize : (function (size) { return size; });\n        this._allowResizeObserver = (_a = options === null || options === void 0 ? void 0 : options.allowResizeObserver) !== null && _a !== void 0 ? _a : true;\n        this._chooseAndInitObserver();\n        // we MAY leave the constuctor without any bitmap size observation mechanics initialized\n    }\n    DevicePixelContentBoxBinding.prototype.dispose = function () {\n        var _a, _b;\n        if (this._canvasElement === null) {\n            throw new Error('Object is disposed');\n        }\n        (_a = this._canvasElementResizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n        this._canvasElementResizeObserver = null;\n        (_b = this._devicePixelRatioObservable) === null || _b === void 0 ? void 0 : _b.dispose();\n        this._devicePixelRatioObservable = null;\n        this._suggestedBitmapSizeChangedListeners.length = 0;\n        this._bitmapSizeChangedListeners.length = 0;\n        this._canvasElement = null;\n    };\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"canvasElement\", {\n        get: function () {\n            if (this._canvasElement === null) {\n                throw new Error('Object is disposed');\n            }\n            return this._canvasElement;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"canvasElementClientSize\", {\n        get: function () {\n            return this._canvasElementClientSize;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"bitmapSize\", {\n        get: function () {\n            return size({\n                width: this.canvasElement.width,\n                height: this.canvasElement.height,\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Use this function to change canvas element client size until binding is disposed\n     * @param clientSize New client size for bound HTMLCanvasElement\n     */\n    DevicePixelContentBoxBinding.prototype.resizeCanvasElement = function (clientSize) {\n        this._canvasElementClientSize = size(clientSize);\n        this.canvasElement.style.width = \"\".concat(this._canvasElementClientSize.width, \"px\");\n        this.canvasElement.style.height = \"\".concat(this._canvasElementClientSize.height, \"px\");\n        this._invalidateBitmapSize();\n    };\n    DevicePixelContentBoxBinding.prototype.subscribeBitmapSizeChanged = function (listener) {\n        this._bitmapSizeChangedListeners.push(listener);\n    };\n    DevicePixelContentBoxBinding.prototype.unsubscribeBitmapSizeChanged = function (listener) {\n        this._bitmapSizeChangedListeners = this._bitmapSizeChangedListeners.filter(function (l) { return l !== listener; });\n    };\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"suggestedBitmapSize\", {\n        get: function () {\n            return this._suggestedBitmapSize;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    DevicePixelContentBoxBinding.prototype.subscribeSuggestedBitmapSizeChanged = function (listener) {\n        this._suggestedBitmapSizeChangedListeners.push(listener);\n    };\n    DevicePixelContentBoxBinding.prototype.unsubscribeSuggestedBitmapSizeChanged = function (listener) {\n        this._suggestedBitmapSizeChangedListeners = this._suggestedBitmapSizeChangedListeners.filter(function (l) { return l !== listener; });\n    };\n    DevicePixelContentBoxBinding.prototype.applySuggestedBitmapSize = function () {\n        if (this._suggestedBitmapSize === null) {\n            // nothing to apply\n            return;\n        }\n        var oldSuggestedSize = this._suggestedBitmapSize;\n        this._suggestedBitmapSize = null;\n        this._resizeBitmap(oldSuggestedSize);\n        this._emitSuggestedBitmapSizeChanged(oldSuggestedSize, this._suggestedBitmapSize);\n    };\n    DevicePixelContentBoxBinding.prototype._resizeBitmap = function (newSize) {\n        var oldSize = this.bitmapSize;\n        if (equalSizes(oldSize, newSize)) {\n            return;\n        }\n        this.canvasElement.width = newSize.width;\n        this.canvasElement.height = newSize.height;\n        this._emitBitmapSizeChanged(oldSize, newSize);\n    };\n    DevicePixelContentBoxBinding.prototype._emitBitmapSizeChanged = function (oldSize, newSize) {\n        var _this = this;\n        this._bitmapSizeChangedListeners.forEach(function (listener) { return listener.call(_this, oldSize, newSize); });\n    };\n    DevicePixelContentBoxBinding.prototype._suggestNewBitmapSize = function (newSize) {\n        var oldSuggestedSize = this._suggestedBitmapSize;\n        var finalNewSize = size(this._transformBitmapSize(newSize, this._canvasElementClientSize));\n        var newSuggestedSize = equalSizes(this.bitmapSize, finalNewSize) ? null : finalNewSize;\n        if (oldSuggestedSize === null && newSuggestedSize === null) {\n            return;\n        }\n        if (oldSuggestedSize !== null && newSuggestedSize !== null\n            && equalSizes(oldSuggestedSize, newSuggestedSize)) {\n            return;\n        }\n        this._suggestedBitmapSize = newSuggestedSize;\n        this._emitSuggestedBitmapSizeChanged(oldSuggestedSize, newSuggestedSize);\n    };\n    DevicePixelContentBoxBinding.prototype._emitSuggestedBitmapSizeChanged = function (oldSize, newSize) {\n        var _this = this;\n        this._suggestedBitmapSizeChangedListeners.forEach(function (listener) { return listener.call(_this, oldSize, newSize); });\n    };\n    DevicePixelContentBoxBinding.prototype._chooseAndInitObserver = function () {\n        var _this = this;\n        if (!this._allowResizeObserver) {\n            this._initDevicePixelRatioObservable();\n            return;\n        }\n        isDevicePixelContentBoxSupported()\n            .then(function (isSupported) {\n            return isSupported ?\n                _this._initResizeObserver() :\n                _this._initDevicePixelRatioObservable();\n        });\n    };\n    // devicePixelRatio approach\n    DevicePixelContentBoxBinding.prototype._initDevicePixelRatioObservable = function () {\n        var _this = this;\n        if (this._canvasElement === null) {\n            // it looks like we are already dead\n            return;\n        }\n        var win = canvasElementWindow(this._canvasElement);\n        if (win === null) {\n            throw new Error('No window is associated with the canvas');\n        }\n        this._devicePixelRatioObservable = createDevicePixelRatioObservable(win);\n        this._devicePixelRatioObservable.subscribe(function () { return _this._invalidateBitmapSize(); });\n        this._invalidateBitmapSize();\n    };\n    DevicePixelContentBoxBinding.prototype._invalidateBitmapSize = function () {\n        var _a, _b;\n        if (this._canvasElement === null) {\n            // it looks like we are already dead\n            return;\n        }\n        var win = canvasElementWindow(this._canvasElement);\n        if (win === null) {\n            return;\n        }\n        var ratio = (_b = (_a = this._devicePixelRatioObservable) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : win.devicePixelRatio;\n        var canvasRects = this._canvasElement.getClientRects();\n        var newSize = \n        // eslint-disable-next-line no-negated-condition\n        canvasRects[0] !== undefined ?\n            predictedBitmapSize(canvasRects[0], ratio) :\n            size({\n                width: this._canvasElementClientSize.width * ratio,\n                height: this._canvasElementClientSize.height * ratio,\n            });\n        this._suggestNewBitmapSize(newSize);\n    };\n    // ResizeObserver approach\n    DevicePixelContentBoxBinding.prototype._initResizeObserver = function () {\n        var _this = this;\n        if (this._canvasElement === null) {\n            // it looks like we are already dead\n            return;\n        }\n        this._canvasElementResizeObserver = new ResizeObserver(function (entries) {\n            var entry = entries.find(function (entry) { return entry.target === _this._canvasElement; });\n            if (!entry || !entry.devicePixelContentBoxSize || !entry.devicePixelContentBoxSize[0]) {\n                return;\n            }\n            var entrySize = entry.devicePixelContentBoxSize[0];\n            var newSize = size({\n                width: entrySize.inlineSize,\n                height: entrySize.blockSize,\n            });\n            _this._suggestNewBitmapSize(newSize);\n        });\n        this._canvasElementResizeObserver.observe(this._canvasElement, { box: 'device-pixel-content-box' });\n    };\n    return DevicePixelContentBoxBinding;\n}());\nexport function bindTo(canvasElement, target) {\n    if (target.type === 'device-pixel-content-box') {\n        return new DevicePixelContentBoxBinding(canvasElement, target.transform, target.options);\n    }\n    throw new Error('Unsupported binding target');\n}\nfunction canvasElementWindow(canvasElement) {\n    // According to DOM Level 2 Core specification, ownerDocument should never be null for HTMLCanvasElement\n    // see https://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#node-ownerDoc\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return canvasElement.ownerDocument.defaultView;\n}\nfunction isDevicePixelContentBoxSupported() {\n    return new Promise(function (resolve) {\n        var ro = new ResizeObserver(function (entries) {\n            resolve(entries.every(function (entry) { return 'devicePixelContentBoxSize' in entry; }));\n            ro.disconnect();\n        });\n        ro.observe(document.body, { box: 'device-pixel-content-box' });\n    })\n        .catch(function () { return false; });\n}\nfunction predictedBitmapSize(canvasRect, ratio) {\n    return size({\n        width: Math.round(canvasRect.left * ratio + canvasRect.width * ratio) -\n            Math.round(canvasRect.left * ratio),\n        height: Math.round(canvasRect.top * ratio + canvasRect.height * ratio) -\n            Math.round(canvasRect.top * ratio),\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,IAAI,QAAQ,YAAY;AAC7C,SAASC,gBAAgB,IAAIC,gCAAgC,QAAQ,0BAA0B;AAC/F,IAAIC,4BAA4B,GAAG,aAAe,YAAY;EAC1D,SAASA,4BAA4BA,CAACC,aAAa,EAAEC,mBAAmB,EAAEC,OAAO,EAAE;IAC/E,IAAIC,EAAE;IACN,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,2BAA2B,GAAG,EAAE;IACrC,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,oCAAoC,GAAG,EAAE;IAC9C;IACA,IAAI,CAACC,2BAA2B,GAAG,IAAI;IACvC;IACA,IAAI,CAACC,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAACL,cAAc,GAAGJ,aAAa;IACnC,IAAI,CAACU,wBAAwB,GAAGd,IAAI,CAAC;MACjCe,KAAK,EAAE,IAAI,CAACP,cAAc,CAACQ,WAAW;MACtCC,MAAM,EAAE,IAAI,CAACT,cAAc,CAACU;IAChC,CAAC,CAAC;IACF,IAAI,CAACC,oBAAoB,GAAGd,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAI,UAAUL,IAAI,EAAE;MAAE,OAAOA,IAAI;IAAE,CAAE;IACrJ,IAAI,CAACoB,oBAAoB,GAAG,CAACb,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,mBAAmB,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;IACtJ,IAAI,CAACe,sBAAsB,CAAC,CAAC;IAC7B;EACJ;EACAnB,4BAA4B,CAACoB,SAAS,CAACC,OAAO,GAAG,YAAY;IACzD,IAAIjB,EAAE,EAAEkB,EAAE;IACV,IAAI,IAAI,CAACjB,cAAc,KAAK,IAAI,EAAE;MAC9B,MAAM,IAAIkB,KAAK,CAAC,oBAAoB,CAAC;IACzC;IACA,CAACnB,EAAE,GAAG,IAAI,CAACM,4BAA4B,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoB,UAAU,CAAC,CAAC;IAC7F,IAAI,CAACd,4BAA4B,GAAG,IAAI;IACxC,CAACY,EAAE,GAAG,IAAI,CAACb,2BAA2B,MAAM,IAAI,IAAIa,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,OAAO,CAAC,CAAC;IACzF,IAAI,CAACZ,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACD,oCAAoC,CAACiB,MAAM,GAAG,CAAC;IACpD,IAAI,CAACnB,2BAA2B,CAACmB,MAAM,GAAG,CAAC;IAC3C,IAAI,CAACpB,cAAc,GAAG,IAAI;EAC9B,CAAC;EACDqB,MAAM,CAACC,cAAc,CAAC3B,4BAA4B,CAACoB,SAAS,EAAE,eAAe,EAAE;IAC3EQ,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,IAAI,IAAI,CAACvB,cAAc,KAAK,IAAI,EAAE;QAC9B,MAAM,IAAIkB,KAAK,CAAC,oBAAoB,CAAC;MACzC;MACA,OAAO,IAAI,CAAClB,cAAc;IAC9B,CAAC;IACDwB,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFJ,MAAM,CAACC,cAAc,CAAC3B,4BAA4B,CAACoB,SAAS,EAAE,yBAAyB,EAAE;IACrFQ,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACjB,wBAAwB;IACxC,CAAC;IACDkB,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFJ,MAAM,CAACC,cAAc,CAAC3B,4BAA4B,CAACoB,SAAS,EAAE,YAAY,EAAE;IACxEQ,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO/B,IAAI,CAAC;QACRe,KAAK,EAAE,IAAI,CAACX,aAAa,CAACW,KAAK;QAC/BE,MAAM,EAAE,IAAI,CAACb,aAAa,CAACa;MAC/B,CAAC,CAAC;IACN,CAAC;IACDe,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF;AACJ;AACA;AACA;EACI9B,4BAA4B,CAACoB,SAAS,CAACW,mBAAmB,GAAG,UAAUC,UAAU,EAAE;IAC/E,IAAI,CAACrB,wBAAwB,GAAGd,IAAI,CAACmC,UAAU,CAAC;IAChD,IAAI,CAAC/B,aAAa,CAACgC,KAAK,CAACrB,KAAK,GAAG,EAAE,CAACsB,MAAM,CAAC,IAAI,CAACvB,wBAAwB,CAACC,KAAK,EAAE,IAAI,CAAC;IACrF,IAAI,CAACX,aAAa,CAACgC,KAAK,CAACnB,MAAM,GAAG,EAAE,CAACoB,MAAM,CAAC,IAAI,CAACvB,wBAAwB,CAACG,MAAM,EAAE,IAAI,CAAC;IACvF,IAAI,CAACqB,qBAAqB,CAAC,CAAC;EAChC,CAAC;EACDnC,4BAA4B,CAACoB,SAAS,CAACgB,0BAA0B,GAAG,UAAUC,QAAQ,EAAE;IACpF,IAAI,CAAC/B,2BAA2B,CAACgC,IAAI,CAACD,QAAQ,CAAC;EACnD,CAAC;EACDrC,4BAA4B,CAACoB,SAAS,CAACmB,4BAA4B,GAAG,UAAUF,QAAQ,EAAE;IACtF,IAAI,CAAC/B,2BAA2B,GAAG,IAAI,CAACA,2BAA2B,CAACkC,MAAM,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,KAAKJ,QAAQ;IAAE,CAAC,CAAC;EACvH,CAAC;EACDX,MAAM,CAACC,cAAc,CAAC3B,4BAA4B,CAACoB,SAAS,EAAE,qBAAqB,EAAE;IACjFQ,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACrB,oBAAoB;IACpC,CAAC;IACDsB,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF9B,4BAA4B,CAACoB,SAAS,CAACsB,mCAAmC,GAAG,UAAUL,QAAQ,EAAE;IAC7F,IAAI,CAAC7B,oCAAoC,CAAC8B,IAAI,CAACD,QAAQ,CAAC;EAC5D,CAAC;EACDrC,4BAA4B,CAACoB,SAAS,CAACuB,qCAAqC,GAAG,UAAUN,QAAQ,EAAE;IAC/F,IAAI,CAAC7B,oCAAoC,GAAG,IAAI,CAACA,oCAAoC,CAACgC,MAAM,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,KAAKJ,QAAQ;IAAE,CAAC,CAAC;EACzI,CAAC;EACDrC,4BAA4B,CAACoB,SAAS,CAACwB,wBAAwB,GAAG,YAAY;IAC1E,IAAI,IAAI,CAACrC,oBAAoB,KAAK,IAAI,EAAE;MACpC;MACA;IACJ;IACA,IAAIsC,gBAAgB,GAAG,IAAI,CAACtC,oBAAoB;IAChD,IAAI,CAACA,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACuC,aAAa,CAACD,gBAAgB,CAAC;IACpC,IAAI,CAACE,+BAA+B,CAACF,gBAAgB,EAAE,IAAI,CAACtC,oBAAoB,CAAC;EACrF,CAAC;EACDP,4BAA4B,CAACoB,SAAS,CAAC0B,aAAa,GAAG,UAAUE,OAAO,EAAE;IACtE,IAAIC,OAAO,GAAG,IAAI,CAACC,UAAU;IAC7B,IAAItD,UAAU,CAACqD,OAAO,EAAED,OAAO,CAAC,EAAE;MAC9B;IACJ;IACA,IAAI,CAAC/C,aAAa,CAACW,KAAK,GAAGoC,OAAO,CAACpC,KAAK;IACxC,IAAI,CAACX,aAAa,CAACa,MAAM,GAAGkC,OAAO,CAAClC,MAAM;IAC1C,IAAI,CAACqC,sBAAsB,CAACF,OAAO,EAAED,OAAO,CAAC;EACjD,CAAC;EACDhD,4BAA4B,CAACoB,SAAS,CAAC+B,sBAAsB,GAAG,UAAUF,OAAO,EAAED,OAAO,EAAE;IACxF,IAAII,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC9C,2BAA2B,CAAC+C,OAAO,CAAC,UAAUhB,QAAQ,EAAE;MAAE,OAAOA,QAAQ,CAACiB,IAAI,CAACF,KAAK,EAAEH,OAAO,EAAED,OAAO,CAAC;IAAE,CAAC,CAAC;EACpH,CAAC;EACDhD,4BAA4B,CAACoB,SAAS,CAACmC,qBAAqB,GAAG,UAAUP,OAAO,EAAE;IAC9E,IAAIH,gBAAgB,GAAG,IAAI,CAACtC,oBAAoB;IAChD,IAAIiD,YAAY,GAAG3D,IAAI,CAAC,IAAI,CAACmB,oBAAoB,CAACgC,OAAO,EAAE,IAAI,CAACrC,wBAAwB,CAAC,CAAC;IAC1F,IAAI8C,gBAAgB,GAAG7D,UAAU,CAAC,IAAI,CAACsD,UAAU,EAAEM,YAAY,CAAC,GAAG,IAAI,GAAGA,YAAY;IACtF,IAAIX,gBAAgB,KAAK,IAAI,IAAIY,gBAAgB,KAAK,IAAI,EAAE;MACxD;IACJ;IACA,IAAIZ,gBAAgB,KAAK,IAAI,IAAIY,gBAAgB,KAAK,IAAI,IACnD7D,UAAU,CAACiD,gBAAgB,EAAEY,gBAAgB,CAAC,EAAE;MACnD;IACJ;IACA,IAAI,CAAClD,oBAAoB,GAAGkD,gBAAgB;IAC5C,IAAI,CAACV,+BAA+B,CAACF,gBAAgB,EAAEY,gBAAgB,CAAC;EAC5E,CAAC;EACDzD,4BAA4B,CAACoB,SAAS,CAAC2B,+BAA+B,GAAG,UAAUE,OAAO,EAAED,OAAO,EAAE;IACjG,IAAII,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC5C,oCAAoC,CAAC6C,OAAO,CAAC,UAAUhB,QAAQ,EAAE;MAAE,OAAOA,QAAQ,CAACiB,IAAI,CAACF,KAAK,EAAEH,OAAO,EAAED,OAAO,CAAC;IAAE,CAAC,CAAC;EAC7H,CAAC;EACDhD,4BAA4B,CAACoB,SAAS,CAACD,sBAAsB,GAAG,YAAY;IACxE,IAAIiC,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAACnC,oBAAoB,EAAE;MAC5B,IAAI,CAACyC,+BAA+B,CAAC,CAAC;MACtC;IACJ;IACAC,gCAAgC,CAAC,CAAC,CAC7BC,IAAI,CAAC,UAAUC,WAAW,EAAE;MAC7B,OAAOA,WAAW,GACdT,KAAK,CAACU,mBAAmB,CAAC,CAAC,GAC3BV,KAAK,CAACM,+BAA+B,CAAC,CAAC;IAC/C,CAAC,CAAC;EACN,CAAC;EACD;EACA1D,4BAA4B,CAACoB,SAAS,CAACsC,+BAA+B,GAAG,YAAY;IACjF,IAAIN,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAAC/C,cAAc,KAAK,IAAI,EAAE;MAC9B;MACA;IACJ;IACA,IAAI0D,GAAG,GAAGC,mBAAmB,CAAC,IAAI,CAAC3D,cAAc,CAAC;IAClD,IAAI0D,GAAG,KAAK,IAAI,EAAE;MACd,MAAM,IAAIxC,KAAK,CAAC,yCAAyC,CAAC;IAC9D;IACA,IAAI,CAACd,2BAA2B,GAAGV,gCAAgC,CAACgE,GAAG,CAAC;IACxE,IAAI,CAACtD,2BAA2B,CAACwD,SAAS,CAAC,YAAY;MAAE,OAAOb,KAAK,CAACjB,qBAAqB,CAAC,CAAC;IAAE,CAAC,CAAC;IACjG,IAAI,CAACA,qBAAqB,CAAC,CAAC;EAChC,CAAC;EACDnC,4BAA4B,CAACoB,SAAS,CAACe,qBAAqB,GAAG,YAAY;IACvE,IAAI/B,EAAE,EAAEkB,EAAE;IACV,IAAI,IAAI,CAACjB,cAAc,KAAK,IAAI,EAAE;MAC9B;MACA;IACJ;IACA,IAAI0D,GAAG,GAAGC,mBAAmB,CAAC,IAAI,CAAC3D,cAAc,CAAC;IAClD,IAAI0D,GAAG,KAAK,IAAI,EAAE;MACd;IACJ;IACA,IAAIG,KAAK,GAAG,CAAC5C,EAAE,GAAG,CAAClB,EAAE,GAAG,IAAI,CAACK,2BAA2B,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+D,KAAK,MAAM,IAAI,IAAI7C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGyC,GAAG,CAACK,gBAAgB;IAC9J,IAAIC,WAAW,GAAG,IAAI,CAAChE,cAAc,CAACiE,cAAc,CAAC,CAAC;IACtD,IAAItB,OAAO;IACX;IACAqB,WAAW,CAAC,CAAC,CAAC,KAAKE,SAAS,GACxBC,mBAAmB,CAACH,WAAW,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC,GAC1CrE,IAAI,CAAC;MACDe,KAAK,EAAE,IAAI,CAACD,wBAAwB,CAACC,KAAK,GAAGsD,KAAK;MAClDpD,MAAM,EAAE,IAAI,CAACH,wBAAwB,CAACG,MAAM,GAAGoD;IACnD,CAAC,CAAC;IACN,IAAI,CAACX,qBAAqB,CAACP,OAAO,CAAC;EACvC,CAAC;EACD;EACAhD,4BAA4B,CAACoB,SAAS,CAAC0C,mBAAmB,GAAG,YAAY;IACrE,IAAIV,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAAC/C,cAAc,KAAK,IAAI,EAAE;MAC9B;MACA;IACJ;IACA,IAAI,CAACK,4BAA4B,GAAG,IAAI+D,cAAc,CAAC,UAAUC,OAAO,EAAE;MACtE,IAAIC,KAAK,GAAGD,OAAO,CAACE,IAAI,CAAC,UAAUD,KAAK,EAAE;QAAE,OAAOA,KAAK,CAACE,MAAM,KAAKzB,KAAK,CAAC/C,cAAc;MAAE,CAAC,CAAC;MAC5F,IAAI,CAACsE,KAAK,IAAI,CAACA,KAAK,CAACG,yBAAyB,IAAI,CAACH,KAAK,CAACG,yBAAyB,CAAC,CAAC,CAAC,EAAE;QACnF;MACJ;MACA,IAAIC,SAAS,GAAGJ,KAAK,CAACG,yBAAyB,CAAC,CAAC,CAAC;MAClD,IAAI9B,OAAO,GAAGnD,IAAI,CAAC;QACfe,KAAK,EAAEmE,SAAS,CAACC,UAAU;QAC3BlE,MAAM,EAAEiE,SAAS,CAACE;MACtB,CAAC,CAAC;MACF7B,KAAK,CAACG,qBAAqB,CAACP,OAAO,CAAC;IACxC,CAAC,CAAC;IACF,IAAI,CAACtC,4BAA4B,CAACwE,OAAO,CAAC,IAAI,CAAC7E,cAAc,EAAE;MAAE8E,GAAG,EAAE;IAA2B,CAAC,CAAC;EACvG,CAAC;EACD,OAAOnF,4BAA4B;AACvC,CAAC,CAAC,CAAE;AACJ,OAAO,SAASoF,MAAMA,CAACnF,aAAa,EAAE4E,MAAM,EAAE;EAC1C,IAAIA,MAAM,CAACQ,IAAI,KAAK,0BAA0B,EAAE;IAC5C,OAAO,IAAIrF,4BAA4B,CAACC,aAAa,EAAE4E,MAAM,CAACS,SAAS,EAAET,MAAM,CAAC1E,OAAO,CAAC;EAC5F;EACA,MAAM,IAAIoB,KAAK,CAAC,4BAA4B,CAAC;AACjD;AACA,SAASyC,mBAAmBA,CAAC/D,aAAa,EAAE;EACxC;EACA;EACA;EACA,OAAOA,aAAa,CAACsF,aAAa,CAACC,WAAW;AAClD;AACA,SAAS7B,gCAAgCA,CAAA,EAAG;EACxC,OAAO,IAAI8B,OAAO,CAAC,UAAUC,OAAO,EAAE;IAClC,IAAIC,EAAE,GAAG,IAAIlB,cAAc,CAAC,UAAUC,OAAO,EAAE;MAC3CgB,OAAO,CAAChB,OAAO,CAACkB,KAAK,CAAC,UAAUjB,KAAK,EAAE;QAAE,OAAO,2BAA2B,IAAIA,KAAK;MAAE,CAAC,CAAC,CAAC;MACzFgB,EAAE,CAACnE,UAAU,CAAC,CAAC;IACnB,CAAC,CAAC;IACFmE,EAAE,CAACT,OAAO,CAACW,QAAQ,CAACC,IAAI,EAAE;MAAEX,GAAG,EAAE;IAA2B,CAAC,CAAC;EAClE,CAAC,CAAC,CACGY,KAAK,CAAC,YAAY;IAAE,OAAO,KAAK;EAAE,CAAC,CAAC;AAC7C;AACA,SAASvB,mBAAmBA,CAACwB,UAAU,EAAE9B,KAAK,EAAE;EAC5C,OAAOrE,IAAI,CAAC;IACRe,KAAK,EAAEqF,IAAI,CAACC,KAAK,CAACF,UAAU,CAACG,IAAI,GAAGjC,KAAK,GAAG8B,UAAU,CAACpF,KAAK,GAAGsD,KAAK,CAAC,GACjE+B,IAAI,CAACC,KAAK,CAACF,UAAU,CAACG,IAAI,GAAGjC,KAAK,CAAC;IACvCpD,MAAM,EAAEmF,IAAI,CAACC,KAAK,CAACF,UAAU,CAACI,GAAG,GAAGlC,KAAK,GAAG8B,UAAU,CAAClF,MAAM,GAAGoD,KAAK,CAAC,GAClE+B,IAAI,CAACC,KAAK,CAACF,UAAU,CAACI,GAAG,GAAGlC,KAAK;EACzC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}