"""
AutoTradz AI Backend API
FastAPI backend with WebSocket support for real-time trading data
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
import uvicorn
import pandas as pd
import numpy as np
from pathlib import Path
from dataclasses import asdict
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks, Query, Request, Response, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import structlog

# Add the parent directory to the path to import src modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from src.trading_ai import AutoTradzAI, StrategyType, MarketData
from src.paper_trading import PaperTradingEngine
from src.alpaca_api import AlpacaCryptoAPI
from src.auth import auth_manager, LoginRequest, RegisterRequest, require_auth, optional_auth, create_session_cookie, clear_session_cookie
from src.database import user_db
from src.crypto_pairs import crypto_pair_manager
from src.historical_data_manager import historical_data_manager
from config import settings

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="AutoTradz AI API",
    description="Institutional-grade AI trading system API",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001", "http://127.0.0.1:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global instances
trading_ai: Optional[AutoTradzAI] = None
paper_engine: Optional[PaperTradingEngine] = None
websocket_connections: List[WebSocket] = []


class ConnectionManager:
    """WebSocket connection manager."""

    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info("WebSocket connected", total_connections=len(self.active_connections))

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info("WebSocket disconnected", total_connections=len(self.active_connections))

    async def broadcast(self, message: dict):
        """Broadcast message to all connected clients."""
        if not self.active_connections:
            return

        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error("Failed to send message", error=str(e))
                disconnected.append(connection)

        # Remove disconnected clients
        for conn in disconnected:
            self.disconnect(conn)


manager = ConnectionManager()


# Pydantic models for API requests
class ApiConfig(BaseModel):
    apiKey: str  # PKTEST... (paper) or PK... (live)
    apiSecret: str  # Alpaca API secret
    paper: bool = True  # Paper trading mode

class TradingConfig(BaseModel):
    risk_per_trade: float = 0.02  # 2%
    max_position_size: float = 0.10  # 10%
    max_drawdown: float = 0.15  # 15%
    initial_balance: float = 10000.0
    stop_loss: float = 0.05  # 5%
    take_profit: float = 0.10  # 10%
    strategy: str = "TECHNICAL"

class ClosePositionRequest(BaseModel):
    symbol: str
    size: Optional[float] = None  # None for full close, float for partial close
    reason: str = "manual_close"


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    global trading_ai, paper_engine

    logger.info("Starting AutoTradz AI backend")

    # Initialize trading AI
    trading_ai = AutoTradzAI()
    paper_engine = PaperTradingEngine()

    # Start background tasks
    asyncio.create_task(market_data_broadcaster())
    asyncio.create_task(historical_data_preloader())

    logger.info("AutoTradz AI backend started successfully")


# Authentication Endpoints
@app.post("/api/auth/register")
async def register_user(request: RegisterRequest):
    """Register a new user account."""
    try:
        result = await auth_manager.register_user(request)

        if result.success:
            logger.info("User registration successful", username=request.username)
            return {"success": True, "message": result.message}
        else:
            logger.warning("User registration failed",
                         username=request.username,
                         reason=result.message)
            return {"success": False, "message": result.message}

    except Exception as e:
        logger.error("Registration endpoint error", error=str(e))
        raise HTTPException(status_code=500, detail="Registration failed")

@app.post("/api/auth/login")
async def login_user(request: LoginRequest, response: Response):
    """Authenticate user and create session."""
    try:
        result = await auth_manager.login_user(request)

        if result.success:
            # Set session cookie
            cookie_config = create_session_cookie(result.session_token)
            response.set_cookie(**cookie_config)

            logger.info("User login successful", username=request.username)
            return {
                "success": True,
                "message": result.message,
                "user": result.user
            }
        else:
            logger.warning("User login failed",
                         username=request.username,
                         reason=result.message)
            return {"success": False, "message": result.message}

    except Exception as e:
        logger.error("Login endpoint error", error=str(e))
        raise HTTPException(status_code=500, detail="Login failed")

@app.post("/api/auth/logout")
async def logout_user(request: Request, response: Response):
    """Logout user and clear session."""
    try:
        session_token = request.cookies.get("session_token")

        if session_token:
            result = await auth_manager.logout_user(session_token)

            # Clear session cookie
            cookie_config = clear_session_cookie()
            response.set_cookie(**cookie_config)

            logger.info("User logout successful")
            return {"success": True, "message": "Logged out successfully"}
        else:
            return {"success": False, "message": "No active session"}

    except Exception as e:
        logger.error("Logout endpoint error", error=str(e))
        raise HTTPException(status_code=500, detail="Logout failed")

@app.get("/api/auth/me")
async def get_current_user_info(user: dict = Depends(optional_auth)):
    """Get current user information."""
    if user:
        return {
            "authenticated": True,
            "user": {
                "id": user["user_id"],
                "username": user["username"],
                "email": user["email"]
            }
        }
    else:
        return {"authenticated": False, "user": None}

@app.get("/api/auth/status")
async def get_auth_status():
    """Get authentication system status."""
    try:
        user_count = user_db.get_user_count()
        return {
            "auth_enabled": True,
            "total_users": user_count,
            "database_status": "connected"
        }
    except Exception as e:
        logger.error("Auth status check failed", error=str(e))
        return {
            "auth_enabled": False,
            "total_users": 0,
            "database_status": "error"
        }


# Crypto Pair Management Endpoints
@app.get("/api/crypto-pairs")
async def get_crypto_pairs():
    """Get all available crypto trading pairs."""
    try:
        pairs = crypto_pair_manager.get_pairs_for_dropdown()
        current_pair = crypto_pair_manager.get_current_pair()

        return {
            "success": True,
            "pairs": pairs,
            "current_pair": current_pair.symbol,
            "total_pairs": len(pairs)
        }
    except Exception as e:
        logger.error("Failed to get crypto pairs", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to fetch crypto pairs")

@app.post("/api/crypto-pairs/switch")
async def switch_crypto_pair(request: dict):
    """Switch to a different crypto trading pair."""
    try:
        new_symbol = request.get("symbol")
        if not new_symbol:
            raise HTTPException(status_code=400, detail="Symbol is required")

        # Validate and switch pair
        if not crypto_pair_manager.set_current_pair(new_symbol):
            raise HTTPException(status_code=400, detail="Invalid crypto pair symbol")

        # Get the new pair info
        new_pair = crypto_pair_manager.get_current_pair()

        # 🧹 CRITICAL: Clear all cached market data to prevent mixing
        if trading_ai:
            old_symbol = trading_ai.current_pair.symbol if hasattr(trading_ai, 'current_pair') else None

            logger.info("🧹 Clearing all market data to prevent price mixing",
                       old_pair=old_symbol,
                       new_pair=new_symbol)

            # Clear all data arrays
            trading_ai.market_data.clear()
            trading_ai.recent_signals.clear()
            trading_ai.signals_history.clear()

            # Clear any cached WebSocket data
            if hasattr(trading_ai, 'latest_quotes'):
                trading_ai.latest_quotes.clear()
            if hasattr(trading_ai, 'latest_trades'):
                trading_ai.latest_trades.clear()

            # Switch the trading pair with clean data
            await trading_ai.switch_trading_pair(new_pair)

            # Force immediate fresh data fetch for new pair
            logger.info("🚀 Forcing immediate fresh data fetch for new pair")
            await trading_ai._fetch_live_data()

        logger.info("✅ Crypto pair switched successfully with clean data isolation",
                   old_pair=crypto_pair_manager.current_pair,
                   new_pair=new_symbol,
                   market_data_points=len(trading_ai.market_data) if trading_ai else 0)

        return {
            "success": True,
            "message": f"Switched to {new_symbol}",
            "pair": {
                "symbol": new_pair.symbol,
                "display_name": new_pair.display_name,
                "base_asset": new_pair.base_asset,
                "category": new_pair.category.value,
                "volatility": new_pair.volatility_level
            },
            "data_isolation": "complete",
            "old_data_cleared": True,
            "market_data_points": len(trading_ai.market_data) if trading_ai else 0
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to switch crypto pair", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to switch crypto pair")

@app.get("/api/crypto-pairs/{symbol}/historical")
async def get_historical_data(symbol: str, timeframe: str = "1Day", limit: int = 1000):
    """Get historical data for a specific crypto pair with enhanced caching."""
    try:
        # Handle different symbol formats (XRPUSD -> XRP/USD)
        if '/' not in symbol and len(symbol) >= 6:
            # Convert XRPUSD to XRP/USD format
            base = symbol[:-3]  # Remove last 3 chars (USD)
            quote = symbol[-3:]  # Get last 3 chars (USD)
            formatted_symbol = f"{base}/{quote}"
        else:
            formatted_symbol = symbol

        # Validate symbol
        pair = crypto_pair_manager.get_pair(formatted_symbol)
        if not pair:
            # Try with original symbol format
            pair = crypto_pair_manager.get_pair(symbol)
            if not pair:
                logger.warning(f"Crypto pair not found: {symbol} (tried {formatted_symbol})")
                raise HTTPException(status_code=404, detail=f"Crypto pair not found: {symbol}")

        # Use the formatted symbol for consistency
        symbol = formatted_symbol if pair else symbol

        # Map frontend timeframes to backend timeframes
        timeframe_map = {
            "1m": "1Min",
            "5m": "5Min",
            "15m": "15Min",
            "1h": "1Hour",
            "4h": "4Hour",
            "1d": "1Day"
        }

        backend_timeframe = timeframe_map.get(timeframe, timeframe)
        logger.info(f"📊 Historical data request: {symbol} - {timeframe} -> {backend_timeframe} - {limit} points")

        # Get historical data with caching
        data = historical_data_manager.get_historical_data(symbol, backend_timeframe, limit)

        if data.empty:
            # If no data exists, try to fetch it
            logger.info(f"📥 No cached data found, fetching historical data for {symbol}")
            success = await historical_data_manager.fetch_historical_data_for_pair(pair, backend_timeframe)
            if success:
                data = historical_data_manager.get_historical_data(symbol, backend_timeframe, limit)

        # Convert to JSON format with optimized processing
        historical_data = []
        if not data.empty:
            # Use vectorized operations for better performance
            data_dict = data.to_dict('records')
            historical_data = [
                {
                    "timestamp": row['timestamp'].isoformat(),
                    "open": float(row['open']),
                    "high": float(row['high']),
                    "low": float(row['low']),
                    "close": float(row['close']),
                    "volume": float(row['volume']),
                    "symbol": symbol,
                    "timeframe": timeframe
                }
                for row in data_dict
            ]

        logger.info(f"📈 Returning {len(historical_data)} historical data points for {symbol} ({timeframe})")

        return {
            "success": True,
            "symbol": symbol,
            "timeframe": timeframe,
            "backend_timeframe": backend_timeframe,
            "data": historical_data,
            "count": len(historical_data),
            "cached": not data.empty
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get historical data", symbol=symbol, timeframe=timeframe, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to fetch historical data")

@app.post("/api/crypto-pairs/fetch-historical")
async def fetch_historical_data_bulk():
    """Fetch historical data for all crypto pairs."""
    try:
        logger.info("Starting bulk historical data fetch")

        # Fetch data for all pairs with multiple timeframes for better chart support
        successful, failed = await historical_data_manager.fetch_all_pairs_data(
            timeframes=["1Day", "1Hour", "15Min", "5Min", "1Min"],
            days_back=365
        )

        # Get status
        status = historical_data_manager.get_data_status()

        return {
            "success": True,
            "message": f"Fetched data for {successful} pairs, {failed} failed",
            "successful": successful,
            "failed": failed,
            "status": status
        }

    except Exception as e:
        logger.error("Failed to fetch bulk historical data", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to fetch historical data")

@app.get("/api/crypto-pairs/data-status")
async def get_data_status():
    """Get status of historical data for all pairs."""
    try:
        status = historical_data_manager.get_data_status()

        return {
            "success": True,
            "status": status,
            "total_pairs": len(status)
        }

    except Exception as e:
        logger.error("Failed to get data status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get data status")


@app.get("/api/crypto-pairs/{symbol}/chart-data")
async def get_chart_data(
    symbol: str,
    timeframe: str = "1Day",
    limit: int = 500,
    force_refresh: bool = False
):
    """Get enhanced chart data for TradingView with proper timeframe support."""
    try:
        # Handle different symbol formats
        if '/' not in symbol and len(symbol) >= 6:
            base = symbol[:-3]
            quote = symbol[-3:]
            formatted_symbol = f"{base}/{quote}"
        else:
            formatted_symbol = symbol

        # Map frontend timeframes to backend timeframes
        timeframe_map = {
            "1m": "1Min",
            "5m": "5Min",
            "15m": "15Min",
            "1h": "1Hour",
            "1d": "1Day"
        }

        backend_timeframe = timeframe_map.get(timeframe, timeframe)

        logger.info(f"📊 Chart data request: {symbol} - {timeframe} -> {backend_timeframe} - {limit} points")

        # Get historical data
        data = historical_data_manager.get_historical_data(formatted_symbol, backend_timeframe, limit)

        if data.empty and not force_refresh:
            # Try to fetch fresh data
            pair = crypto_pair_manager.get_pair_by_symbol(formatted_symbol)
            if pair:
                logger.info(f"📥 No chart data found, fetching for {formatted_symbol}")
                success = await historical_data_manager.fetch_historical_data_for_pair(pair, backend_timeframe)
                if success:
                    data = historical_data_manager.get_historical_data(formatted_symbol, backend_timeframe, limit)

        # Convert to TradingView format
        chart_data = []
        for _, row in data.iterrows():
            chart_data.append({
                "time": int(row['timestamp'].timestamp()),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": float(row['volume'])
            })

        # Sort by time to ensure proper ordering
        chart_data.sort(key=lambda x: x['time'])

        logger.info(f"📊 Returning {len(chart_data)} chart data points for {symbol} ({timeframe})")

        return {
            "success": True,
            "symbol": formatted_symbol,
            "timeframe": timeframe,
            "data": chart_data,
            "count": len(chart_data)
        }

    except Exception as e:
        logger.error("Failed to get chart data", symbol=symbol, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get chart data: {str(e)}")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global trading_ai

    if trading_ai and trading_ai.is_running:
        await trading_ai.stop()

    logger.info("AutoTradz AI backend stopped")


async def historical_data_preloader():
    """Background task to preload historical data for faster chart loading."""
    try:
        logger.info("🚀 Starting historical data preloader...")

        # Wait a bit for the system to initialize
        await asyncio.sleep(10)

        if not historical_data_manager:
            logger.warning("Historical data manager not available")
            return

        # Get all available crypto pairs
        pairs = crypto_pair_manager.get_all_pairs()
        logger.info(f"📊 Preloading historical data for {len(pairs)} crypto pairs...")

        # Preload data for common timeframes
        timeframes = ["1Min", "5Min", "15Min", "1Hour", "4Hour", "1Day"]

        for pair in pairs[:10]:  # Limit to first 10 pairs to avoid overwhelming the system
            for timeframe in timeframes:
                try:
                    # Check if data already exists
                    existing_data = historical_data_manager.get_historical_data(pair.symbol, timeframe, 1)
                    if existing_data.empty:
                        logger.info(f"📥 Fetching {timeframe} data for {pair.symbol}...")
                        await historical_data_manager.fetch_historical_data_for_pair(pair, timeframe)
                        # Small delay to avoid overwhelming the API
                        await asyncio.sleep(1)
                    else:
                        logger.info(f"✅ {pair.symbol} {timeframe} data already cached")

                except Exception as e:
                    logger.warning(f"Failed to preload {pair.symbol} {timeframe}: {str(e)}")
                    continue

        logger.info("🎯 Historical data preloading completed")

    except Exception as e:
        logger.error(f"Historical data preloader error: {str(e)}")


async def market_data_broadcaster():
    """Background task to broadcast real-time market data updates to WebSocket clients."""
    import random
    import time

    logger.info("🚀 Starting real-time market data broadcaster")

    # Initialize with some mock data for demonstration
    base_price = 0.50  # XRP starting price

    while True:
        try:
            # Use only real Alpaca data - no mock data generation
            if trading_ai and trading_ai.api:
                # Fetch real-time data from Alpaca
                live_data = trading_ai.api.get_live_data_for_symbol("XRP/USD")

                if live_data:
                    from src.trading_ai import MarketData
                    real_data = MarketData(
                        timestamp=live_data['timestamp'],
                        open=live_data['open'],
                        high=live_data['high'],
                        low=live_data['low'],
                        close=live_data['close'],
                        volume=live_data['volume'],
                        product_id="XRP/USD"
                    )
                    trading_ai.market_data.append(real_data)
                    print(f"🚀 Real Alpaca data: {live_data['symbol']} @ ${live_data['close']:.4f}")
                else:
                    print("⚠️ No real Alpaca data available - check API connection")

            # 🔄 SMART DATA GENERATION - Live vs Mock
            if trading_ai:
                current_time = datetime.now()

                # 🌐 CHECK DATA SOURCE AND GENERATE ACCORDINGLY
                # EMERGENCY FIX: Temporarily disable live data to stop spam
                if trading_ai.data_source == "live":
                    # 📡 FETCH LIVE DATA FROM ALPACA (with fallback to prevent spam)
                    try:
                        result = await trading_ai._fetch_live_data()
                        if len(trading_ai.market_data) > 0:
                            new_data = trading_ai.market_data[-1]
                            # Only log every 50th update to reduce spam
                            if len(trading_ai.market_data) % 50 == 0:
                                print(f"📡 Live data: ${new_data.close:.4f} at {current_time.strftime('%H:%M:%S')}")
                        else:
                            # EMERGENCY: Add mock data to prevent infinite retry loop
                            print("⚠️ No real Alpaca data available - using temporary mock data to prevent spam")
                            await trading_ai._add_mock_data_emergency()
                            break  # Exit the retry loop
                    except Exception as e:
                        # EMERGENCY: Add mock data to prevent infinite retry loop
                        print(f"❌ Live data error: {e} - using temporary mock data")
                        await trading_ai._add_mock_data_emergency()
                        break  # Exit the retry loop

                # Only proceed if we have real data - NO MOCK DATA GENERATION

                # Keep only last 1000 data points
                if len(trading_ai.market_data) > 1000:
                    trading_ai.market_data = trading_ai.market_data[-1000:]

                # Get the latest data point for broadcasting
                if len(trading_ai.market_data) > 0:
                    new_data = trading_ai.market_data[-1]
                else:
                    continue  # Skip this iteration if no data

                # Update portfolio with current price
                await trading_ai._update_portfolio_value(new_data)

                # Get updated data
                portfolio_summary = trading_ai.get_portfolio_summary()
                recent_signals = trading_ai.get_recent_signals(5)

                # recent_signals is already in serializable format (list of dicts)
                serializable_signals = recent_signals

                # Send candlestick data for the chart
                candlestick_data = {
                    "timestamp": new_data.timestamp.isoformat(),
                    "open": new_data.open,
                    "high": new_data.high,
                    "low": new_data.low,
                    "close": new_data.close,
                    "volume": new_data.volume
                }

                # 📡 ENHANCED REAL-TIME MESSAGE WITH POSITIONS
                positions_data = []
                for symbol, position in trading_ai.portfolio.positions.items():
                    position.current_price = new_data.close  # Update with latest price
                    unrealized_pnl = (new_data.close - position.entry_price) * position.size
                    positions_data.append({
                        'symbol': symbol,
                        'size': round(position.size, 6),
                        'entry_price': round(position.entry_price, 4),
                        'current_price': round(new_data.close, 4),
                        'unrealized_pnl': round(unrealized_pnl, 2),
                        'unrealized_pnl_pct': round((unrealized_pnl / (position.entry_price * position.size)) * 100, 2) if position.entry_price * position.size > 0 else 0
                    })

                message = {
                    "type": "market_update",
                    "data": {
                        "candlestick": candlestick_data,
                        "price": round(new_data.close, 4),
                        "price_change": round(new_data.close - (trading_ai.market_data[-2].close if len(trading_ai.market_data) > 1 else new_data.open), 4),
                        "price_change_pct": round(((new_data.close - (trading_ai.market_data[-2].close if len(trading_ai.market_data) > 1 else new_data.open)) / (trading_ai.market_data[-2].close if len(trading_ai.market_data) > 1 else new_data.open)) * 100, 2) if (trading_ai.market_data[-2].close if len(trading_ai.market_data) > 1 else new_data.open) > 0 else 0,
                        "volume": new_data.volume,
                        "portfolio": portfolio_summary,
                        "positions": positions_data,
                        "recent_signals": serializable_signals,
                        "risk_status": {
                            "circuit_breaker": trading_ai.risk_manager.circuit_breaker_triggered,
                            "daily_loss": round(trading_ai.risk_manager.daily_loss, 2),
                            "consecutive_losses": trading_ai.risk_manager.current_consecutive_losses,
                            "risk_per_trade": trading_ai.risk_manager.risk_per_trade,
                            "max_position_size": trading_ai.risk_manager.max_position_size
                        },
                        "timestamp": datetime.now().isoformat()
                    }
                }

                try:
                    await manager.broadcast(message)
                except Exception as e:
                    logger.error("Failed to send message", error=str(e))
                    # Try sending without signals to isolate the issue
                    try:
                        simple_message = {
                            "type": "market_update",
                            "data": {
                                "candlestick": candlestick_data,
                                "price": new_data.close,
                                "volume": new_data.volume,
                                "portfolio": portfolio_summary
                            }
                        }
                        await manager.broadcast(simple_message)
                    except Exception as e2:
                        logger.error("Failed to send even simple message", error=str(e2))

            await asyncio.sleep(5.0)  # Update every 5 seconds to reduce terminal spam

        except Exception as e:
            logger.error("Market data broadcaster error", error=str(e))
            await asyncio.sleep(5)


# API Routes

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "AutoTradz AI API", "version": "1.0.0", "status": "running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "trading_ai_running": trading_ai.is_running if trading_ai else False
    }


@app.post("/trading/start")
async def start_trading():
    """Start the trading AI."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    if trading_ai.is_running:
        return {"message": "Trading AI is already running"}

    # Start trading in background
    asyncio.create_task(trading_ai.start())

    return {"message": "Trading AI started successfully"}


@app.post("/trading/stop")
async def stop_trading():
    """Stop the trading AI."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    if not trading_ai.is_running:
        return {"message": "Trading AI is not running"}

    await trading_ai.stop()

    return {"message": "Trading AI stopped successfully"}


@app.get("/portfolio")
async def get_portfolio():
    """Get current portfolio summary."""
    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    return trading_ai.get_portfolio_summary()


@app.get("/signals")
async def get_signals(limit: int = 20, pair: str = None):
    """Get recent trading signals with optional pair filtering."""
    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    signals = trading_ai.get_recent_signals(limit)

    # Filter by pair if specified
    if pair and signals:
        # Convert pair format (e.g., "BTC/USD" to "BTC-USD")
        product_id = pair.replace('/', '-')
        filtered_signals = [signal for signal in signals if getattr(signal, 'product_id', None) == product_id]
        logger.info(f"Filtered signals for {pair}: {len(filtered_signals)} signals")
        return filtered_signals

    return signals


@app.get("/performance")
async def get_performance():
    """Get performance metrics."""
    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    return trading_ai.get_performance_metrics()


@app.get("/market-data")
async def get_market_data(limit: int = 100, timeframe: str = "1m", pair: str = None):
    """Get recent market data with REAL-TIME Alpaca data integration."""
    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    # Default to XRP/USD if no pair specified
    if not pair:
        pair = "XRP/USD"

    # Convert pair format for consistency (e.g., "BTC/USD" to "BTC-USD")
    product_id = pair.replace('/', '-')
    symbol = pair.replace('/', '')  # For historical data lookup

    logger.info(f"📊 Fetching REAL-TIME market data: {pair} ({product_id}) - {timeframe} - {limit} points")

    # 🚀 PRIORITY 1: Get real-time Alpaca data first
    real_time_data = []
    try:
        if trading_ai.api:
            # Map timeframes for Alpaca API
            alpaca_timeframe_map = {
                "1m": "1Min",
                "5m": "5Min",
                "15m": "15Min",
                "1h": "1Hour",
                "4h": "4Hour",
                "1d": "1Day"
            }

            alpaca_timeframe = alpaca_timeframe_map.get(timeframe, "1Min")

            # Get real-time bars from Alpaca
            from datetime import datetime, timedelta
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)  # Get last 24 hours

            bars_data = trading_ai.api.get_crypto_bars_sdk(
                symbols=[pair],
                timeframe=alpaca_timeframe,
                start=start_time,
                end=end_time,
                limit=limit
            )

            if pair in bars_data and bars_data[pair]:
                real_time_data = [
                    MarketData(
                        timestamp=bar['timestamp'],
                        open=bar['open'],
                        high=bar['high'],
                        low=bar['low'],
                        close=bar['close'],
                        volume=bar['volume'],
                        product_id=product_id,
                        symbol=pair
                    )
                    for bar in bars_data[pair]
                ]
                logger.info(f"🚀 Retrieved {len(real_time_data)} REAL-TIME Alpaca bars for {pair}")

    except Exception as e:
        logger.error(f"Failed to get real-time Alpaca data: {str(e)}")

    # If we have real-time data, use it primarily
    if real_time_data:
        final_data = real_time_data[-limit:] if real_time_data else []
        logger.info(f"📊 Returning {len(final_data)} REAL-TIME Alpaca data points for {pair}")

        return [
            {
                "timestamp": data.timestamp.isoformat(),
                "open": data.open,
                "high": data.high,
                "low": data.low,
                "close": data.close,
                "volume": data.volume,
                "product_id": data.product_id,
                "symbol": pair,
                "source": "alpaca_realtime"
            }
            for data in final_data
        ]

    # 📈 FALLBACK: If no real-time data, try historical + live data
    logger.warning(f"No real-time Alpaca data available for {pair}, using fallback methods")

    # Try to get historical data first for better chart initialization
    historical_data = []
    try:
        if historical_data_manager:
            # Map timeframes to historical data timeframes
            hist_timeframe_map = {
                "1m": "1Min",
                "5m": "5Min",
                "15m": "15Min",
                "1h": "1Hour",
                "4h": "4Hour",
                "1d": "1Day"
            }
            hist_timeframe = hist_timeframe_map.get(timeframe, "1Hour")

            # Get historical data from database
            hist_df = historical_data_manager.get_historical_data(symbol, hist_timeframe, limit * 2)
            if not hist_df.empty:
                historical_data = [
                    MarketData(
                        timestamp=row['timestamp'],
                        open=row['open'],
                        high=row['high'],
                        low=row['low'],
                        close=row['close'],
                        volume=row['volume'],
                        product_id=product_id,
                        symbol=pair
                    )
                    for _, row in hist_df.iterrows()
                ]
                logger.info(f"📈 Retrieved {len(historical_data)} historical data points for {pair}")
    except Exception as e:
        logger.warning(f"Failed to get historical data for {pair}: {str(e)}")

    # Get live market data
    live_data = trading_ai.market_data if trading_ai.market_data else []

    # Filter live data by pair (handle both XRP/USD and XRP-USD formats)
    filtered_live_data = [
        data for data in live_data
        if data.product_id == product_id or data.product_id == pair
    ]

    # Combine historical and live data
    combined_data = historical_data + filtered_live_data

    # Remove duplicates by timestamp (keep latest)
    seen_timestamps = set()
    unique_data = []

    # Sort by timestamp first
    def safe_timestamp_key(data_point):
        timestamp = data_point.timestamp
        if timestamp.tzinfo is not None:
            return timestamp.timestamp()
        else:
            return timestamp.replace(tzinfo=timezone.utc).timestamp()

    sorted_data = sorted(combined_data, key=safe_timestamp_key)

    # Remove duplicates (keep latest entry for each timestamp)
    for data_point in reversed(sorted_data):
        timestamp_key = int(safe_timestamp_key(data_point))
        if timestamp_key not in seen_timestamps:
            seen_timestamps.add(timestamp_key)
            unique_data.insert(0, data_point)

    # Apply timeframe aggregation if needed
    if timeframe != "1m" and len(unique_data) > 0:
        aggregated_data = _aggregate_market_data(unique_data, timeframe)
        final_data = aggregated_data[-limit:] if aggregated_data else []
    else:
        final_data = unique_data[-limit:] if unique_data else []

    logger.info(f"📊 Returning {len(final_data)} market data points for {pair} ({timeframe})")

    return [
        {
            "timestamp": data.timestamp.isoformat(),
            "open": data.open,
            "high": data.high,
            "low": data.low,
            "close": data.close,
            "volume": data.volume,
            "product_id": data.product_id,
            "symbol": pair
        }
        for data in final_data
    ]


@app.get("/api/historical-data")
async def get_historical_data(
    product_id: str = "XRP-USD",
    timeframe: str = "1h",
    hours: int = 24,
    days: int = 0
):
    """🕰️ FETCH HISTORICAL DATA from Coinbase API for backtesting and chart initialization."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Calculate time range
        end_time = datetime.now()
        if days > 0:
            start_time = end_time - timedelta(days=days)
        else:
            start_time = end_time - timedelta(hours=hours)

        # Map timeframe to Coinbase granularity
        granularity_map = {
            "1m": "ONE_MINUTE",
            "5m": "FIVE_MINUTE",
            "15m": "FIFTEEN_MINUTE",
            "30m": "THIRTY_MINUTE",
            "1h": "ONE_HOUR",
            "2h": "TWO_HOUR",
            "6h": "SIX_HOUR",
            "1d": "ONE_DAY"
        }

        granularity = granularity_map.get(timeframe, "ONE_HOUR")

        # Fetch historical data
        if trading_ai.data_source == "live" and trading_ai.api:
            print(f"📊 Fetching historical data: {product_id} {timeframe} from {start_time} to {end_time}")
            historical_data = trading_ai.api.get_historical_data(
                product_id, start_time, end_time, granularity
            )

            if historical_data:
                print(f"✅ Retrieved {len(historical_data)} historical candles")
                return [
                    {
                        "timestamp": candle["timestamp"].isoformat(),
                        "open": candle["open"],
                        "high": candle["high"],
                        "low": candle["low"],
                        "close": candle["close"],
                        "volume": candle["volume"],
                        "product_id": product_id
                    }
                    for candle in historical_data
                ]
            else:
                print("⚠️ No historical data returned from API")

        # NO MOCK DATA FALLBACK - Only use real Alpaca data
        logger.error("❌ No historical data available from Alpaca API",
                    timeframe=timeframe,
                    start=start_time.isoformat(),
                    end=end_time.isoformat())

        # Return empty list instead of mock data
        return []

    except Exception as e:
        print(f"❌ Error fetching historical data: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch historical data: {str(e)}")


# MOCK DATA GENERATION REMOVED - ALPACA ONLY IMPLEMENTATION
# All data now comes from real Alpaca API endpoints

def _aggregate_market_data(data, timeframe):
    """Aggregate market data by timeframe."""
    if not data:
        return []

    # Define timeframe intervals in seconds
    timeframe_seconds = {
        "1m": 60,
        "5m": 300,
        "15m": 900,
        "1h": 3600,
        "4h": 14400,
        "1d": 86400
    }

    interval = timeframe_seconds.get(timeframe, 60)
    buckets = {}

    for item in data:
        # Create bucket key based on timeframe
        timestamp = item.timestamp.timestamp()
        bucket_key = int(timestamp // interval) * interval

        if bucket_key not in buckets:
            buckets[bucket_key] = {
                'timestamp': item.timestamp.replace(second=0, microsecond=0),
                'open': item.open,
                'high': item.high,
                'low': item.low,
                'close': item.close,
                'volume': item.volume,
                'product_id': item.product_id,
                'count': 1
            }
        else:
            bucket = buckets[bucket_key]
            bucket['high'] = max(bucket['high'], item.high)
            bucket['low'] = min(bucket['low'], item.low)
            bucket['close'] = item.close  # Last close in the bucket
            bucket['volume'] += item.volume
            bucket['count'] += 1

    # Convert back to MarketData objects
    from src.trading_ai import MarketData
    aggregated = []
    for bucket_data in sorted(buckets.values(), key=lambda x: x['timestamp']):
        aggregated.append(MarketData(
            timestamp=bucket_data['timestamp'],
            open=bucket_data['open'],
            high=bucket_data['high'],
            low=bucket_data['low'],
            close=bucket_data['close'],
            volume=bucket_data['volume'],
            product_id=bucket_data['product_id']
        ))

    return aggregated


@app.get("/paper-trading/summary")
async def get_paper_trading_summary():
    """Get paper trading portfolio summary."""
    if not paper_engine:
        raise HTTPException(status_code=500, detail="Paper trading engine not initialized")

    return paper_engine.get_portfolio_summary()


@app.get("/paper-trading/analytics")
async def get_paper_trading_analytics():
    """Get paper trading performance analytics."""
    if not paper_engine:
        raise HTTPException(status_code=500, detail="Paper trading engine not initialized")

    return paper_engine.get_performance_analytics()


@app.get("/paper-trading/trades")
async def get_paper_trades(limit: int = 50):
    """Get paper trading history."""
    if not paper_engine:
        raise HTTPException(status_code=500, detail="Paper trading engine not initialized")

    return paper_engine.get_trade_history(limit)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time market data updates."""
    await manager.connect(websocket)

    try:
        logger.info("🚀 WebSocket client connected for real-time market data")

        # Send initial connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "message": "Connected to real-time Alpaca data stream",
            "timestamp": datetime.now().isoformat()
        }))

        while True:
            # Keep connection alive and handle client messages
            data = await websocket.receive_text()
            message = json.loads(data)

            # Handle different message types
            if message.get("type") == "ping":
                await websocket.send_text(json.dumps({"type": "pong"}))
            elif message.get("type") == "subscribe":
                # Handle subscription requests
                channels = message.get("channels", [])
                await websocket.send_text(json.dumps({
                    "type": "subscribed",
                    "channels": channels,
                    "message": f"Subscribed to {len(channels)} channels",
                    "timestamp": datetime.now().isoformat()
                }))
                logger.info("Client subscribed to channels", channels=channels)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error("WebSocket error", error=str(e))
        manager.disconnect(websocket)


@app.post("/api/configure")
async def configure_api(config: ApiConfig):
    """Configure Alpaca API credentials."""
    print("🔧 DEBUG: /api/configure endpoint called!")
    print(f"🔧 DEBUG: Received config - apiKey: {config.apiKey[:20]}..., paper: {config.paper}")
    logger.info("API configure endpoint called", api_key_preview=config.apiKey[:20], paper=config.paper)

    try:
        # Validate Alpaca API key format
        if not config.apiKey or not config.apiSecret:
            raise ValueError("API key and secret are required")

        # Check if it's a valid Alpaca API key format
        if not (config.apiKey.startswith('PK') or config.apiKey.startswith('PKTEST')):
            raise ValueError("Invalid Alpaca API key format. Should start with 'PK' (live) or 'PKTEST' (paper)")

        # Update environment variables
        import os
        os.environ['ALPACA_API_KEY'] = config.apiKey
        os.environ['ALPACA_API_SECRET'] = config.apiSecret
        os.environ['ALPACA_PAPER_TRADING'] = str(config.paper).lower()

        # Update .env file to persist configuration
        env_file_path = ".env"

        # Use python-dotenv to properly handle .env files
        from dotenv import load_dotenv, set_key

        # Load existing .env file
        load_dotenv(env_file_path)

        # Update specific keys
        set_key(env_file_path, 'ALPACA_API_KEY', config.apiKey)
        set_key(env_file_path, 'ALPACA_API_SECRET', config.apiSecret)
        set_key(env_file_path, 'ALPACA_PAPER_TRADING', str(config.paper).lower())

        # Reload settings
        global settings
        from config import Settings
        settings = Settings()

        # Test the connection first before reinitializing
        test_api = AlpacaCryptoAPI(
            api_key=config.apiKey,
            api_secret=config.apiSecret,
            paper=config.paper
        )

        connection_successful = test_api.test_connection()

        # Update API credentials in existing trading AI instance
        global trading_ai
        was_running = trading_ai and trading_ai.is_running

        print(f"🔍 DEBUG: trading_ai exists: {trading_ai is not None}")
        print(f"🔍 DEBUG: was_running: {was_running}")

        # If trading AI exists, update its credentials
        if trading_ai:
            print("🔄 Updating API credentials in existing trading AI...")
            # Update the API credentials
            if trading_ai.update_api_credentials():
                print("✅ API credentials updated successfully")
                logger.info("API credentials updated successfully")

                # Switch to live data if connection test passed
                if connection_successful and config.apiKey and config.apiSecret:
                    if trading_ai.switch_to_live_data():
                        print("✅ Switched to live Alpaca data")
                        logger.info("Switched to live Alpaca data")
                    else:
                        print("❌ Failed to switch to live data, using mock data")
                        logger.warning("Failed to switch to live data, using mock data")
                else:
                    print("❌ API connection test failed, using mock data")
                    logger.warning("API connection test failed, using mock data")
            else:
                print("❌ Failed to update API credentials")
                logger.error("Failed to update API credentials")
        else:
            # Create new trading AI if it doesn't exist
            trading_ai = AutoTradzAI()

            # Switch to live data if connection test passed
            if connection_successful and config.apiKey and config.apiSecret:
                if trading_ai.switch_to_live_data():
                    logger.info("Switched to live Alpaca data")

                    # Start trading to begin fetching live data
                    import asyncio
                    asyncio.create_task(trading_ai.start())
                    logger.info("Started trading AI with live data")
                else:
                    logger.warning("Failed to switch to live data, using mock data")
            else:
                logger.warning("API connection test failed, using mock data")
                # Start with mock data if connection failed
                if was_running:
                    import asyncio
                    asyncio.create_task(trading_ai.start())
                    logger.info("Started trading AI with mock data")

        logger.info("API configuration updated", paper=config.paper)

        return {"message": "API configuration updated successfully", "paper": config.paper}

    except Exception as e:
        logger.error("Failed to configure API", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to configure API: {str(e)}")


@app.post("/api/test-connection")
async def test_api_connection(config: ApiConfig):
    """Test Alpaca API connection."""
    try:
        # Validate Alpaca API credentials
        if not config.apiKey or not config.apiSecret:
            raise ValueError("API key and secret are required")

        # Check if it's a valid Alpaca API key format
        if not (config.apiKey.startswith('PK') or config.apiKey.startswith('PKTEST')):
            raise ValueError("Invalid Alpaca API key format. Should start with 'PK' (live) or 'PKTEST' (paper)")

        # Create a temporary API client with provided credentials
        api = AlpacaCryptoAPI(
            api_key=config.apiKey,
            api_secret=config.apiSecret,
            paper=config.paper
        )

        if api.test_connection():
            return {
                "success": True,
                "message": "Connection successful",
                "paper_trading": config.paper
            }
        else:
            raise Exception("Connection test failed")

    except Exception as e:
        logger.error("API connection test failed", error=str(e))
        return {
            "success": False,
            "error": f"Connection failed: {str(e)}"
        }


@app.post("/api/test-jwt-creation")
async def test_jwt_creation(request: dict):
    """Test JWT creation with specific key."""
    try:
        from src.coinbase_api import CoinbaseCDPAPI

        api_key = request.get("test_key", "")
        api_secret = request.get("test_secret", "")

        # Clean and format the private key
        cleaned_private_key = api_secret.strip()
        if '\\n' in cleaned_private_key:
            cleaned_private_key = cleaned_private_key.replace('\\n', '\n')

        # Create API instance
        test_api = CoinbaseCDPAPI(
            api_key=api_key,
            api_secret=cleaned_private_key,
            sandbox=False
        )

        # Try to create a JWT token
        jwt_token = test_api._create_jwt_token("GET", "/api/v3/brokerage/accounts")

        return {
            "message": "JWT creation successful",
            "token_length": len(jwt_token),
            "token_preview": jwt_token[:50] + "..." if len(jwt_token) > 50 else jwt_token
        }

    except Exception as e:
        logger.error("JWT creation test failed", error=str(e))
        return {
            "message": f"JWT creation failed: {str(e)}",
            "error_type": type(e).__name__
        }


@app.get("/api/json-format-example")
async def get_json_format_example():
    """Get example JSON format for API configuration."""
    return {
        "example_formats": [
            {
                "name": "Standard CDP Format",
                "description": "Coinbase Developer Platform API format",
                "example": {
                    "apiKey": "organizations/your_org_id/apiKeys/your_key_id",
                    "apiSecret": "-----BEGIN EC PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END EC PRIVATE KEY-----\n",
                    "sandbox": True
                }
            },
            {
                "name": "Alternative Format",
                "description": "Alternative field names",
                "example": {
                    "name": "organizations/your_org_id/apiKeys/your_key_id",
                    "privateKey": "-----BEGIN EC PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END EC PRIVATE KEY-----\n",
                    "sandbox": True
                }
            },
            {
                "name": "Nested Format",
                "description": "Nested configuration format",
                "example": {
                    "coinbase": {
                        "apiKey": "organizations/your_org_id/apiKeys/your_key_id",
                        "apiSecret": "-----BEGIN EC PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END EC PRIVATE KEY-----\n",
                        "sandbox": True
                    }
                }
            }
        ],
        "instructions": [
            "1. Get your API credentials from https://portal.cdp.coinbase.com/",
            "2. Create a new text file with .json extension",
            "3. Copy one of the example formats above",
            "4. Replace 'your_org_id' and 'your_key_id' with actual values from your API key",
            "5. Replace 'YOUR_PRIVATE_KEY_HERE' with your actual EC private key",
            "6. Save the file and upload it to AutoTradz AI",
            "7. The system will automatically detect the format and load your credentials"
        ]
    }


# MOCK DATA CLEARING REMOVED - NO MOCK DATA IN ALPACA-ONLY IMPLEMENTATION


@app.post("/api/switch-data-source")
async def switch_data_source(
    data_source: str = Query(..., description="Data source to switch to: 'live', 'websocket', or 'mock'"),
    use_simulation: bool = Query(False, description="Use simulated WebSocket for testing (only for websocket data source)")
):
    """🔄 ENHANCED DATA SOURCE SWITCHING with detailed feedback and simulation support."""
    global trading_ai

    print(f"🔍 DEBUG: switch_data_source endpoint called with data_source='{data_source}', use_simulation={use_simulation}")

    if not trading_ai:
        print("❌ DEBUG: Trading AI not initialized")
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    print(f"🔍 DEBUG: Current trading_ai.data_source before switch: '{trading_ai.data_source}'")
    print(f"🔄 Switching data source to: {data_source}")

    if data_source == "live":
        print("🌐 Attempting to switch to live Alpaca data...")

        # Force live data mode regardless of account API status
        trading_ai.data_source = "live"

        # Clear old mock data
        if trading_ai.market_data:
            old_count = len(trading_ai.market_data)
            live_data = [data for data in trading_ai.market_data if data.close > 1.50]
            trading_ai.market_data = live_data
            new_count = len(trading_ai.market_data)
            print(f"🧹 Cleared mock data: {old_count} -> {new_count} data points")

        print("✅ Forced switch to live data (market data API is working)")
        return {
            "message": "Switched to live Alpaca data (market data only)",
            "data_source": "live",
            "status": "success",
            "note": "Account API unavailable but market data is working",
            "timestamp": datetime.now().isoformat()
        }
    elif data_source == "websocket":
        mode = "simulated" if use_simulation else "real"
        print(f"🔌 Switching to WebSocket {mode} data...")
        success = trading_ai.enable_websocket(use_simulation=use_simulation)
        print(f"🔍 DEBUG: enable_websocket(use_simulation={use_simulation}) returned: {success}")

        if success:
            print(f"✅ Successfully switched to WebSocket {mode} data")
            return {
                "message": f"Switched to WebSocket {mode} data",
                "data_source": "websocket",
                "simulation": use_simulation,
                "status": "success",
                "timestamp": datetime.now().isoformat()
            }
        else:
            print("❌ Failed to switch to WebSocket data")
            return {"message": "Failed to enable WebSocket", "data_source": trading_ai.data_source, "status": "failed", "timestamp": datetime.now().isoformat()}
    else:
        print(f"❌ DEBUG: Invalid data source: '{data_source}'")
        raise HTTPException(status_code=400, detail="Invalid data source. Use 'live' or 'websocket' only. Mock data has been removed.")


@app.get("/api/data-source-status")
async def get_data_source_status():
    """🔍 ENHANCED DATA SOURCE STATUS with detailed info."""
    global trading_ai

    if not trading_ai:
        return {"data_source": "unknown", "api_configured": False}

    # Check if API is configured (Alpaca format)
    api_configured = bool(
        settings.alpaca_api_key and
        settings.alpaca_api_secret
    )

    # Test API connection if configured
    api_connection_status = "unknown"
    if api_configured and trading_ai.api:
        try:
            api_connection_status = "connected" if trading_ai.api.test_connection() else "failed"
        except:
            api_connection_status = "error"

    return {
        "data_source": trading_ai.data_source,
        "api_configured": api_configured,
        "api_connection_status": api_connection_status,
        "trading_active": trading_ai.is_running,
        "market_data_count": len(trading_ai.market_data),
        "last_update": trading_ai.market_data[-1].timestamp.isoformat() if trading_ai.market_data else None,
        "api_key_preview": settings.alpaca_api_key[:20] + "..." if settings.alpaca_api_key else None,
        "paper_trading": settings.alpaca_paper_trading,
        "websocket_enabled": getattr(trading_ai, 'websocket_enabled', False),
        "websocket_connected": getattr(trading_ai, 'websocket_manager', None) is not None and getattr(trading_ai.websocket_manager, 'websocket', None) is not None if hasattr(trading_ai, 'websocket_manager') else False
    }


@app.get("/api/current-config")
async def get_current_config():
    """Get current API configuration (without sensitive data)."""
    return {
        "api_key_configured": bool(settings.alpaca_api_key),
        "api_secret_configured": bool(settings.alpaca_api_secret),
        "paper_trading": settings.alpaca_paper_trading,
        "api_key_preview": settings.alpaca_api_key[:20] + "..." if settings.alpaca_api_key else None
    }


@app.get("/api/alpaca/live-market-data")
async def get_alpaca_live_market_data():
    """Get live market data from Alpaca for all major crypto pairs."""
    try:
        # Create temporary API client if trading_ai doesn't have one
        if trading_ai and trading_ai.api:
            api_client = trading_ai.api
        else:
            # Create temporary client with current settings
            api_client = AlpacaCryptoAPI()

        # Get live data for XRP/USD (working approach)
        live_data = api_client.get_live_data_for_symbol("XRP/USD")

        if live_data and 'close' in live_data:
            # Format the data for frontend consumption
            formatted_data = {
                "XRP/USD": {
                    "symbol": "XRP/USD",
                    "timestamp": live_data['timestamp'].isoformat() if hasattr(live_data['timestamp'], 'isoformat') else str(live_data['timestamp']),
                    "open": float(live_data['open']),
                    "high": float(live_data['high']),
                    "low": float(live_data['low']),
                    "close": float(live_data['close']),
                    "volume": float(live_data['volume']),
                    "price": float(live_data['close']),
                    "change": 0,  # Calculate if needed
                    "change_percent": 0,  # Calculate if needed
                    "source": "alpaca_real_time"
                }
            }

            logger.info("🚀 REAL Alpaca data fetched successfully",
                       symbol="XRP/USD",
                       price=live_data['close'])

            return {
                "success": True,
                "data": formatted_data,
                "timestamp": datetime.now().isoformat(),
                "source": "alpaca_real_time",
                "pairs_count": 1,
                "message": "✅ REAL ALPACA DATA - NO MOCK DATA"
            }
        else:
            logger.error("❌ No live data available from Alpaca API")
            return {
                "success": False,
                "error": "No live data available from Alpaca API - Check API credentials",
                "timestamp": datetime.now().isoformat(),
                "source": "alpaca_error"
            }

    except Exception as e:
        logger.error("❌ Failed to get Alpaca live market data", error=str(e))
        return {
            "success": False,
            "error": f"Alpaca API Error: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "source": "alpaca_error"
        }


@app.get("/api/alpaca/historical-data")
async def get_alpaca_historical_data(timeframe: str = "1Hour", hours: int = 24):
    """Get historical data for all crypto pairs from Alpaca API."""
    try:
        # Create API client
        if trading_ai and trading_ai.api:
            api_client = trading_ai.api
        else:
            api_client = AlpacaCryptoAPI()

        # Get historical data for all pairs
        historical_data = api_client.get_historical_data_for_all_pairs(timeframe=timeframe, hours=hours)

        if historical_data:
            # Format the data for frontend consumption
            formatted_data = {}
            total_candles = 0

            for symbol, candles in historical_data.items():
                formatted_candles = []
                for candle in candles:
                    formatted_candles.append({
                        "timestamp": candle['timestamp'].isoformat() if hasattr(candle['timestamp'], 'isoformat') else str(candle['timestamp']),
                        "open": float(candle['open']),
                        "high": float(candle['high']),
                        "low": float(candle['low']),
                        "close": float(candle['close']),
                        "volume": float(candle['volume']),
                        "symbol": symbol
                    })

                formatted_data[symbol] = formatted_candles
                total_candles += len(formatted_candles)

            logger.info("📈 Historical data fetched successfully",
                       pairs_count=len(formatted_data),
                       total_candles=total_candles,
                       timeframe=timeframe,
                       hours=hours)

            return {
                "success": True,
                "data": formatted_data,
                "timestamp": datetime.now().isoformat(),
                "source": "alpaca_historical",
                "pairs_count": len(formatted_data),
                "total_candles": total_candles,
                "timeframe": timeframe,
                "hours": hours,
                "message": "✅ REAL ALPACA HISTORICAL DATA"
            }
        else:
            logger.error("❌ No historical data available from Alpaca API")
            return {
                "success": False,
                "error": "No historical data available from Alpaca API",
                "timestamp": datetime.now().isoformat(),
                "source": "alpaca_error"
            }

    except Exception as e:
        logger.error("❌ Failed to get Alpaca historical data", error=str(e))
        return {
            "success": False,
            "error": f"Alpaca API Error: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "source": "alpaca_error"
        }


@app.get("/api/alpaca/account")
async def get_alpaca_account():
    """Get Alpaca account information."""
    try:
        # Create API client
        if trading_ai and trading_ai.api:
            api_client = trading_ai.api
        else:
            api_client = AlpacaCryptoAPI()

        # Get account info using direct API call
        account_info = api_client.get_account_info_direct()

        if account_info:
            return {
                "success": True,
                "account": {
                    "id": account_info.get('id'),
                    "account_number": account_info.get('account_number'),
                    "status": account_info.get('status'),
                    "currency": account_info.get('currency'),
                    "buying_power": float(account_info.get('buying_power', 0)),
                    "cash": float(account_info.get('cash', 0)),
                    "portfolio_value": float(account_info.get('portfolio_value', 0)),
                    "equity": float(account_info.get('equity', 0)),
                    "day_trade_count": account_info.get('day_trade_count', 0),
                    "crypto_status": account_info.get('crypto_status'),
                    "created_at": account_info.get('created_at'),
                    "trading_blocked": account_info.get('trading_blocked', False),
                    "transfers_blocked": account_info.get('transfers_blocked', False),
                    "account_blocked": account_info.get('account_blocked', False),
                    "pattern_day_trader": account_info.get('pattern_day_trader', False)
                },
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": "Failed to fetch account information",
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error("Failed to get Alpaca account info", error=str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/api/alpaca/available-symbols")
async def get_available_symbols():
    """Get list of available crypto symbols from Alpaca and check our configured pairs."""
    try:
        # Create API client
        if trading_ai and trading_ai.api:
            api_client = trading_ai.api
        else:
            api_client = AlpacaCryptoAPI()

        # Get available crypto pairs
        available_pairs = api_client.get_crypto_pairs()

        # Get our configured pairs
        from src.crypto_pairs import crypto_pair_manager
        configured_pairs = crypto_pair_manager.get_all_pairs()

        # Check which of our pairs are available
        available_configured = []
        unavailable_configured = []

        for pair in configured_pairs:
            if pair.alpaca_symbol in available_pairs:
                available_configured.append({
                    "symbol": pair.symbol,
                    "alpaca_symbol": pair.alpaca_symbol,
                    "display_name": pair.display_name,
                    "available": True
                })
            else:
                unavailable_configured.append({
                    "symbol": pair.symbol,
                    "alpaca_symbol": pair.alpaca_symbol,
                    "display_name": pair.display_name,
                    "available": False
                })

        logger.info("Symbol availability check completed",
                   total_alpaca=len(available_pairs),
                   configured=len(configured_pairs),
                   available=len(available_configured),
                   unavailable=len(unavailable_configured))

        return {
            "success": True,
            "total_available": len(available_pairs),
            "configured_pairs": len(configured_pairs),
            "available_configured": len(available_configured),
            "unavailable_configured": len(unavailable_configured),
            "available_pairs": available_configured,
            "unavailable_pairs": unavailable_configured,
            "sample_alpaca_pairs": available_pairs[:20],  # First 20 for reference
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error("Failed to get available symbols", error=str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/api/alpaca/pair-analytics/{symbol}")
async def get_alpaca_pair_analytics(symbol: str):
    """Get detailed analytics for a specific crypto pair from Alpaca."""
    try:
        # Create API client
        if trading_ai and trading_ai.api:
            api_client = trading_ai.api
        else:
            api_client = AlpacaCryptoAPI()

        # Get live data for the specific symbol
        live_data = api_client.get_live_data_for_symbol(symbol)

        if live_data:
            # Calculate additional analytics
            current_price = live_data['close']
            daily_change = current_price - live_data['open']
            daily_change_pct = (daily_change / live_data['open']) * 100 if live_data['open'] > 0 else 0

            # Get historical data for trend analysis
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)  # 7 days of data

            historical_df = api_client.get_historical_data(
                symbol=symbol,
                start=start_time,
                end=end_time,
                timeframe="1Hour"
            )

            # Convert DataFrame to list of dictionaries for processing
            historical_data = []
            if historical_df is not None and not historical_df.empty:
                for _, row in historical_df.iterrows():
                    historical_data.append({
                        'timestamp': row['timestamp'],
                        'high': row['high'],
                        'low': row['low'],
                        'volume': row['volume']
                    })

            # Calculate 24h high/low from historical data
            try:
                if historical_data and len(historical_data) > 0:
                    recent_24h = [candle for candle in historical_data if candle['timestamp'] >= end_time - timedelta(hours=24)]
                    high_24h = max([candle['high'] for candle in recent_24h]) if recent_24h else live_data['high']
                    low_24h = min([candle['low'] for candle in recent_24h]) if recent_24h else live_data['low']
                    volume_24h = sum([candle['volume'] for candle in recent_24h]) if recent_24h else live_data['volume']
                else:
                    high_24h = live_data['high']
                    low_24h = live_data['low']
                    volume_24h = live_data['volume']
            except Exception as hist_error:
                logger.warning(f"Error processing historical data: {hist_error}")
                high_24h = live_data['high']
                low_24h = live_data['low']
                volume_24h = live_data['volume']

            return {
                "success": True,
                "symbol": symbol,
                "analytics": {
                    "current_price": current_price,
                    "daily_change": daily_change,
                    "daily_change_pct": daily_change_pct,
                    "high_24h": high_24h,
                    "low_24h": low_24h,
                    "volume_24h": volume_24h,
                    "last_updated": live_data['timestamp'].isoformat(),
                    "market_cap_rank": None,  # Not available from Alpaca
                    "volatility": abs(daily_change_pct),
                    "trend": "bullish" if daily_change > 0 else "bearish" if daily_change < 0 else "neutral"
                },
                "raw_data": live_data,
                "historical_points": len(historical_data) if historical_data else 0,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": f"No live data available for {symbol}",
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error("Failed to get pair analytics", symbol=symbol, error=str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/api/alpaca/sdk-demo")
async def get_alpaca_sdk_demo():
    """Demonstrate the new Alpaca SDK request methods."""
    try:
        # Create API client
        if trading_ai and trading_ai.api:
            api_client = trading_ai.api
        else:
            api_client = AlpacaCryptoAPI()

        symbols = ["XRP/USD", "BTC/USD", "ETH/USD"]

        # Test the new SDK methods
        result = {
            "bars_1hour": api_client.get_crypto_bars_sdk(symbols, timeframe="1Hour", limit=5),
            "latest_quotes": api_client.get_crypto_latest_quotes_sdk(symbols),
            "latest_trades": api_client.get_crypto_latest_trades_sdk(symbols),
            "snapshots": api_client.get_crypto_snapshots_sdk(symbols)
        }

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat(),
            "symbols_tested": symbols
        }

    except Exception as e:
        logger.error("Failed to get Alpaca SDK demo data", error=str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.post("/api/load-json-config")
async def load_json_config(file_content: dict):
    """Load API configuration from Coinbase JSON file."""
    try:
        # Extract API key and private key from JSON (handle both formats)
        api_key = file_content.get("name") or file_content.get("apiKey")
        private_key = file_content.get("privateKey") or file_content.get("apiSecret")

        if not api_key or not private_key:
            raise HTTPException(status_code=400, detail="Invalid JSON format. Missing 'name'/'apiKey' or 'privateKey'/'apiSecret'")

        # Format the private key properly by adding line breaks
        formatted_private_key = private_key.replace("-----BEGIN EC PRIVATE KEY-----", "-----BEGIN EC PRIVATE KEY-----\n")
        formatted_private_key = formatted_private_key.replace("-----END EC PRIVATE KEY-----", "\n-----END EC PRIVATE KEY-----")

        # Split the key content into 64-character lines (standard PEM format)
        lines = formatted_private_key.split('\n')
        if len(lines) >= 2:
            # Get the content between BEGIN and END
            key_content = lines[1].replace("-----END EC PRIVATE KEY-----", "")

            # Split into 64-character lines
            formatted_lines = [lines[0]]  # BEGIN line
            for i in range(0, len(key_content), 64):
                formatted_lines.append(key_content[i:i+64])
            formatted_lines.append("-----END EC PRIVATE KEY-----")  # END line

            formatted_private_key = '\n'.join(formatted_lines)

        # Create configuration object
        from pydantic import BaseModel

        class TempAPIConfig(BaseModel):
            apiKey: str
            apiSecret: str
            sandbox: bool = True

        config = TempAPIConfig(
            apiKey=api_key,
            apiSecret=formatted_private_key,
            sandbox=True  # Default to sandbox for safety
        )

        # Test the connection first
        from src.coinbase_api import CoinbaseCDPAPI
        test_api = CoinbaseCDPAPI(
            api_key=config.apiKey,
            api_secret=formatted_private_key,
            sandbox=config.sandbox
        )

        connection_successful = test_api.test_connection()

        if connection_successful:
            # Save to .env file
            env_path = Path(".env")
            env_content = f"""COINBASE_API_KEY={config.apiKey}
COINBASE_API_SECRET={formatted_private_key}
COINBASE_SANDBOX={str(config.sandbox).lower()}
"""
            env_path.write_text(env_content)

            # Update settings
            settings.coinbase_api_key = config.apiKey
            settings.coinbase_api_secret = formatted_private_key
            settings.coinbase_sandbox = config.sandbox

            # Reinitialize trading AI with new credentials
            global trading_ai
            was_running = trading_ai and trading_ai.is_running

            if trading_ai and trading_ai.is_running:
                await trading_ai.stop()

            trading_ai = AutoTradzAI()

            # Switch to live data
            if trading_ai.switch_to_live_data():
                logger.info("Switched to live Coinbase data")

                # Start trading to begin fetching live data
                import asyncio
                asyncio.create_task(trading_ai.start())
                logger.info("Started trading AI with live data")

                return {"message": "JSON configuration loaded successfully and switched to live data", "sandbox": config.sandbox}
            else:
                logger.warning("Failed to switch to live data, using mock data")
                return {"message": "JSON configuration loaded but failed to connect to live data", "sandbox": config.sandbox}
        else:
            return {"message": "JSON configuration loaded but API connection test failed", "sandbox": config.sandbox}

    except Exception as e:
        logger.error(f"Failed to load JSON configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to load JSON configuration: {str(e)}")


@app.post("/api/simulate-volatility")
async def simulate_volatility():
    """Simulate market volatility for testing purposes."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    # Add some artificial price movements to trigger signals
    import random
    from src.trading_ai import MarketData
    from datetime import datetime

    if len(trading_ai.market_data) > 0:
        last_price = trading_ai.market_data[-1].close

        # Create 5 volatile price movements
        for i in range(5):
            # Generate significant price movement (±2-5%)
            price_change = random.uniform(-0.05, 0.05)  # ±5% change
            new_price = last_price * (1 + price_change)

            volatile_data = MarketData(
                timestamp=datetime.now(),
                open=last_price,
                high=max(last_price, new_price) * 1.01,
                low=min(last_price, new_price) * 0.99,
                close=new_price,
                volume=random.uniform(500000, 2000000),  # High volume
                product_id="XRP-USD"
            )

            trading_ai.market_data.append(volatile_data)
            last_price = new_price

        # Keep only last 1000 data points
        if len(trading_ai.market_data) > 1000:
            trading_ai.market_data = trading_ai.market_data[-1000:]

    return {"message": "Volatility simulation added", "data_points": 5}


@app.post("/api/force-ml-retrain")
async def force_ml_retrain():
    """Force ML model retraining for faster learning."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Get the ML strategy
        ml_strategy = trading_ai.strategies.get(StrategyType.ML_ENSEMBLE)
        if ml_strategy and len(trading_ai.market_data) >= 20:
            # Convert market data to DataFrame
            df = trading_ai._market_data_to_dataframe()

            # Force retrain
            success = ml_strategy._train_model(df)

            if success:
                return {"message": "ML model retrained successfully", "data_points": len(df)}
            else:
                return {"message": "ML model retraining failed", "data_points": len(df)}
        else:
            return {"message": "Insufficient data for ML retraining", "data_points": len(trading_ai.market_data)}

    except Exception as e:
        logger.error(f"Failed to retrain ML model: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrain ML model: {str(e)}")


@app.get("/api/strategy-performance")
async def get_strategy_performance():
    """Get performance metrics for all strategies."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        performance = trading_ai.performance_tracker.get_performance_summary()
        weights = trading_ai.strategy_weights

        return {
            "performance": performance,
            "current_weights": {k.value: v for k, v in weights.items()},
            "learning_enabled": trading_ai.learning_enabled
        }

    except Exception as e:
        logger.error(f"Failed to get strategy performance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get strategy performance: {str(e)}")


@app.get("/api/positions")
async def get_positions():
    """🔍 ENHANCED POSITION TRACKING - Get current positions with detailed info"""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        print(f"🔍 Getting positions... Portfolio positions: {len(trading_ai.portfolio.positions)}")

        positions = []
        total_value = 0
        total_pnl = 0

        # Get positions from portfolio
        for symbol, position in trading_ai.portfolio.positions.items():
            # Get current market price for accurate PnL calculation
            current_price = trading_ai.market_data[-1].close if trading_ai.market_data else position.entry_price

            # Update position current price
            position.current_price = current_price

            # Calculate unrealized PnL
            unrealized_pnl = (current_price - position.entry_price) * position.size
            position.unrealized_pnl = unrealized_pnl

            position_value = position.size * current_price

            position_data = {
                'id': getattr(position, 'id', symbol),
                'symbol': symbol,
                'side': 'long',  # Assuming long positions for now
                'size': round(position.size, 6),
                'entry_price': round(position.entry_price, 4),
                'current_price': round(current_price, 4),
                'unrealized_pnl': round(unrealized_pnl, 2),
                'unrealized_pnl_pct': round((unrealized_pnl / (position.entry_price * position.size)) * 100, 2) if position.entry_price * position.size > 0 else 0,
                'position_value': round(position_value, 2),
                'timestamp': getattr(position, 'timestamp', datetime.now()).isoformat(),
                'duration': str(datetime.now() - getattr(position, 'timestamp', datetime.now())).split('.')[0],
                'strategy': getattr(position, 'strategy', 'Unknown'),
                'confidence': getattr(position, 'confidence', 0.0)
            }

            positions.append(position_data)
            total_value += position_value
            total_pnl += unrealized_pnl

            print(f"📊 Position {symbol}: {position.size:.4f} @ ${position.entry_price:.4f} | PnL: ${unrealized_pnl:.2f}")

        response_data = {
            'positions': positions,
            'total_positions': len(positions),
            'total_value': round(total_value, 2),
            'total_pnl': round(total_pnl, 2),
            'total_pnl_pct': round((total_pnl / total_value) * 100, 2) if total_value > 0 else 0,
            'timestamp': datetime.now().isoformat(),
            'status': 'active' if len(positions) > 0 else 'no_positions'
        }

        print(f"🔍 Returning {len(positions)} positions with total PnL: ${total_pnl:.2f}")
        return response_data

    except Exception as e:
        print(f"❌ Error getting positions: {e}")
        import traceback
        traceback.print_exc()
        return {'error': str(e), 'positions': [], 'total_positions': 0}


@app.get("/api/ai-status")
async def get_ai_status():
    """Get comprehensive AI status for dashboard."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Get recent signals
        recent_signals = trading_ai.get_recent_signals(5)

        # Get market data count
        market_data_count = len(trading_ai.market_data)

        # Get current market regime
        if market_data_count > 0:
            df = trading_ai._market_data_to_dataframe()
            regime, regime_confidence = trading_ai.regime_detector.detect_regime(df)
        else:
            regime, regime_confidence = "UNKNOWN", 0.0

        # Get strategy weights
        weights = trading_ai.strategy_weights

        # Get performance
        performance = trading_ai.performance_tracker.get_performance_summary()

        # Get latest price
        latest_price = trading_ai.market_data[-1].close if trading_ai.market_data else 0.0

        return {
            "trading_active": trading_ai.is_running,
            "data_source": trading_ai.data_source,
            "learning_enabled": trading_ai.learning_enabled,
            "market_data_points": market_data_count,
            "latest_price": latest_price,
            "market_regime": regime.value if hasattr(regime, 'value') else str(regime),
            "regime_confidence": regime_confidence,
            "strategy_weights": {k.value: v for k, v in weights.items()},
            "recent_signals_count": len(recent_signals),
            "performance": performance,
            "portfolio_value": trading_ai.portfolio.total_value,
            "unrealized_pnl": trading_ai.portfolio.unrealized_pnl
        }

    except Exception as e:
        logger.error(f"Failed to get AI status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get AI status: {str(e)}")


@app.post("/api/test-websocket")
async def test_websocket():
    """Test WebSocket by sending a test message."""
    test_message = {
        "type": "test_update",
        "data": {
            "message": "WebSocket test message",
            "timestamp": datetime.now().isoformat(),
            "test": True
        }
    }

    try:
        await manager.broadcast(test_message)
        return {"message": "Test message sent to all WebSocket connections", "connections": len(manager.active_connections)}
    except Exception as e:
        logger.error(f"Failed to send test message: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send test message: {str(e)}")


@app.post("/api/configure-trading")
async def configure_trading(config: TradingConfig):
    """Configure trading parameters and risk management."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # 🔧 FORCE UPDATE RISK MANAGER CONFIGURATION
        risk_manager = trading_ai.risk_manager
        risk_manager.base_risk_per_trade = config.risk_per_trade
        risk_manager.risk_per_trade = config.risk_per_trade
        risk_manager.base_max_position_size = config.max_position_size
        risk_manager.max_position_size = config.max_position_size
        risk_manager.max_drawdown = config.max_drawdown
        risk_manager.max_daily_loss = config.max_drawdown  # Use same limit for daily loss

        # Store config on trading AI for reference
        trading_ai.risk_per_trade = config.risk_per_trade
        trading_ai.max_position_size = config.max_position_size
        trading_ai.max_drawdown = config.max_drawdown
        trading_ai.stop_loss = config.stop_loss
        trading_ai.take_profit = config.take_profit

        # Update portfolio initial balance
        if hasattr(trading_ai.portfolio, 'initial_cash'):
            trading_ai.portfolio.initial_cash = config.initial_balance

        print(f"🔧 Applied trading config: Risk={config.risk_per_trade*100:.1f}%, MaxPos={config.max_position_size*100:.1f}%, MaxDD={config.max_drawdown*100:.1f}%")

        # Update strategy weights based on selected strategy
        if config.strategy == "TECHNICAL":
            trading_ai.strategy_weights = {
                StrategyType.TECHNICAL: 0.8,
                StrategyType.ML_ENSEMBLE: 0.2
            }
        elif config.strategy == "ML_ENSEMBLE":
            trading_ai.strategy_weights = {
                StrategyType.TECHNICAL: 0.3,
                StrategyType.ML_ENSEMBLE: 0.7
            }
        elif config.strategy == "MOMENTUM":
            trading_ai.strategy_weights = {
                StrategyType.TECHNICAL: 0.4,
                StrategyType.ML_ENSEMBLE: 0.3,
                StrategyType.MOMENTUM: 0.3
            }

        logger.info("Trading configuration updated", config=config.dict())

        return {
            "message": "Trading configuration updated successfully",
            "config": config.dict(),
            "strategy_weights": {k.value: v for k, v in trading_ai.strategy_weights.items()}
        }

    except Exception as e:
        logger.error(f"Failed to configure trading: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to configure trading: {str(e)}")


@app.post("/api/close-position")
async def close_position(request: ClosePositionRequest):
    """Manually close a position (full or partial)."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Check if position exists
        if request.symbol not in trading_ai.portfolio.positions:
            raise HTTPException(status_code=404, detail=f"Position {request.symbol} not found")

        position = trading_ai.portfolio.positions[request.symbol]

        # Determine size to close
        if request.size is None:
            # Full close
            size_to_close = position.size
        else:
            # Partial close
            size_to_close = min(request.size, position.size)
            if size_to_close <= 0:
                raise HTTPException(status_code=400, detail="Invalid size to close")

        # Get current market price
        current_price = position.current_price
        if trading_ai.market_data:
            current_price = trading_ai.market_data[-1].close

        # Calculate P&L for the closed portion
        pnl = (current_price - position.entry_price) * size_to_close

        # Execute the close
        if request.size is None:
            # Full close - remove position
            del trading_ai.portfolio.positions[request.symbol]
            trading_ai.portfolio.cash += size_to_close * current_price
            trading_ai.portfolio.realized_pnl += pnl
        else:
            # Partial close - reduce position size
            position.size -= size_to_close
            trading_ai.portfolio.cash += size_to_close * current_price
            trading_ai.portfolio.realized_pnl += pnl

            # Update position current price
            position.current_price = current_price
            position.unrealized_pnl = (current_price - position.entry_price) * position.size

        # Update portfolio total value
        trading_ai.portfolio.total_value = trading_ai.portfolio.cash + sum(
            pos.size * pos.current_price for pos in trading_ai.portfolio.positions.values()
        )

        # Log the manual close
        logger.info("Manual position close",
                   symbol=request.symbol,
                   size_closed=size_to_close,
                   pnl=pnl,
                   reason=request.reason)

        return {
            "message": f"Position {request.symbol} {'fully' if request.size is None else 'partially'} closed",
            "symbol": request.symbol,
            "size_closed": size_to_close,
            "pnl": pnl,
            "remaining_size": position.size if request.size is not None else 0,
            "close_price": current_price,
            "reason": request.reason
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to close position: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to close position: {str(e)}")


@app.get("/api/institutional-risk-report")
async def get_institutional_risk_report():
    """🏛️ Get comprehensive institutional risk management report."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        risk_report = trading_ai.get_institutional_risk_report()
        return risk_report

    except Exception as e:
        logger.error(f"Failed to get institutional risk report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get institutional risk report: {str(e)}")


@app.post("/api/reset-circuit-breaker")
async def reset_circuit_breaker():
    """🚨 Manually reset the circuit breaker (requires manual intervention)."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        if trading_ai.risk_manager.circuit_breaker_triggered:
            trading_ai.risk_manager.circuit_breaker_triggered = False
            trading_ai.risk_manager.current_consecutive_losses = 0
            logger.info("Circuit breaker manually reset")

            return {
                "message": "Circuit breaker reset successfully",
                "status": "active",
                "reset_time": datetime.now().isoformat()
            }
        else:
            return {
                "message": "Circuit breaker was not active",
                "status": "active"
            }

    except Exception as e:
        logger.error(f"Failed to reset circuit breaker: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to reset circuit breaker: {str(e)}")


@app.post("/api/update-risk-settings")
async def update_risk_settings(settings: dict):
    """🏛️ Update institutional risk management settings."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        risk_manager = trading_ai.risk_manager

        # Update risk limits if provided
        if 'max_drawdown' in settings:
            risk_manager.max_drawdown = float(settings['max_drawdown'])
        if 'max_daily_loss' in settings:
            risk_manager.max_daily_loss = float(settings['max_daily_loss'])
        if 'confidence_threshold' in settings:
            risk_manager.confidence_threshold = float(settings['confidence_threshold'])
        if 'sizing_method' in settings:
            risk_manager.sizing_method = settings['sizing_method']

        logger.info("Risk settings updated", settings=settings)

        return {
            "message": "Risk settings updated successfully",
            "current_settings": {
                "max_drawdown": risk_manager.max_drawdown,
                "max_daily_loss": risk_manager.max_daily_loss,
                "confidence_threshold": risk_manager.confidence_threshold,
                "sizing_method": risk_manager.sizing_method,
                "circuit_breaker_active": risk_manager.circuit_breaker_triggered
            }
        }

    except Exception as e:
        logger.error(f"Failed to update risk settings: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update risk settings: {str(e)}")


@app.get("/api/trading-signals")
async def get_trading_signals(limit: int = 10):
    """🎯 GET CURRENT TRADING SIGNALS and recent signal history."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Get recent signals
        recent_signals = trading_ai.signals_history[-limit:] if trading_ai.signals_history else []

        # Generate current signal if we have enough data
        current_signal = None
        if len(trading_ai.market_data) >= 5:  # Minimum data for signal generation
            df = trading_ai._market_data_to_dataframe()
            current_data = trading_ai.market_data[-1]

            # Get signals from all active strategies
            signals = []
            for strategy_type, strategy in trading_ai.strategies.items():
                signal = await strategy.generate_signal(df, current_data)
                signals.append(signal)

            # Combine signals using the AI's logic
            if signals:
                market_regime, regime_confidence = trading_ai.regime_detector.detect_regime(df)
                current_signal = trading_ai._combine_signals_enhanced(signals, market_regime, regime_confidence)

        return {
            "current_signal": {
                "signal": current_signal.signal.value if current_signal else "HOLD",
                "confidence": current_signal.confidence if current_signal else 0.0,
                "price": current_signal.price if current_signal else 0.0,
                "timestamp": current_signal.timestamp.isoformat() if current_signal else None,
                "strategy": current_signal.strategy.value if current_signal else None,
                "reasoning": current_signal.reasoning if current_signal else "No signal generated",
                "risk_score": current_signal.risk_score if current_signal else 0.0,
                "position_size": current_signal.position_size if current_signal else 0.0,
                "metadata": current_signal.metadata if current_signal else {}
            } if current_signal else None,
            "recent_signals": [
                {
                    "signal": signal.signal.value,
                    "confidence": signal.confidence,
                    "price": signal.price,
                    "timestamp": signal.timestamp.isoformat(),
                    "strategy": signal.strategy.value,
                    "reasoning": signal.reasoning,
                    "risk_score": signal.risk_score,
                    "position_size": signal.position_size,
                    "metadata": signal.metadata
                }
                for signal in recent_signals
            ],
            "trading_active": trading_ai.is_running,
            "data_source": trading_ai.data_source,
            "total_signals": len(trading_ai.signals_history)
        }

    except Exception as e:
        print(f"❌ Error getting trading signals: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get trading signals: {str(e)}")


@app.post("/api/execute-signal")
async def execute_trading_signal():
    """⚡ MANUALLY EXECUTE CURRENT TRADING SIGNAL for testing."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        if len(trading_ai.market_data) < 5:
            raise HTTPException(status_code=400, detail="Insufficient market data for signal generation")

        # Generate and execute current signal
        df = trading_ai._market_data_to_dataframe()
        current_data = trading_ai.market_data[-1]

        # Get signals from all active strategies
        signals = []
        for strategy_type, strategy in trading_ai.strategies.items():
            signal = await strategy.generate_signal(df, current_data)
            signals.append(signal)

        if not signals:
            raise HTTPException(status_code=400, detail="No signals generated")

        # Combine signals
        market_regime, regime_confidence = trading_ai.regime_detector.detect_regime(df)
        final_signal = trading_ai._combine_signals_enhanced(signals, market_regime, regime_confidence)

        # Execute if not HOLD
        from src.trading_ai import SignalType
        if final_signal.signal != SignalType.HOLD:
            # Validate with risk manager
            if trading_ai.risk_manager.validate_trade(trading_ai.portfolio, final_signal):
                # Calculate position size
                position_size = trading_ai.risk_manager.calculate_position_size(
                    trading_ai.portfolio, final_signal, current_data.close
                )

                # Update signal with position size
                final_signal.position_size = position_size

                # Execute trade
                await trading_ai._execute_trade(final_signal)

                return {
                    "success": True,
                    "message": "Signal executed successfully",
                    "signal": {
                        "signal": final_signal.signal.value,
                        "confidence": final_signal.confidence,
                        "price": final_signal.price,
                        "timestamp": final_signal.timestamp.isoformat(),
                        "strategy": final_signal.strategy.value,
                        "reasoning": final_signal.reasoning,
                        "risk_score": final_signal.risk_score,
                        "position_size": final_signal.position_size,
                        "metadata": final_signal.metadata
                    }
                }
            else:
                return {
                    "success": False,
                    "message": "Signal rejected by risk manager",
                    "signal": {
                        "signal": final_signal.signal.value,
                        "confidence": final_signal.confidence,
                        "reasoning": final_signal.reasoning,
                        "risk_score": final_signal.risk_score
                    }
                }
        else:
            return {
                "success": False,
                "message": "Current signal is HOLD - no action taken",
                "signal": {
                    "signal": final_signal.signal.value,
                    "confidence": final_signal.confidence,
                    "reasoning": final_signal.reasoning
                }
            }

    except Exception as e:
        print(f"❌ Error executing trading signal: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to execute trading signal: {str(e)}")


@app.get("/api/risk-metrics")
async def get_risk_metrics():
    """🏛️ GET COMPREHENSIVE INSTITUTIONAL RISK METRICS."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        risk_manager = trading_ai.risk_manager
        portfolio = trading_ai.portfolio

        # Calculate current drawdown
        current_drawdown = 0.0
        if hasattr(portfolio, 'peak_value') and portfolio.peak_value > 0:
            current_drawdown = (portfolio.peak_value - portfolio.total_value) / portfolio.peak_value

        # Calculate daily P&L
        daily_pnl_pct = (risk_manager.daily_pnl / portfolio.total_value * 100) if portfolio.total_value > 0 else 0.0

        # Calculate position concentration
        total_position_value = sum(pos.size * pos.current_price for pos in portfolio.positions.values())
        concentration_ratio = (total_position_value / portfolio.total_value) if portfolio.total_value > 0 else 0.0

        return {
            "risk_limits": {
                "max_position_size": risk_manager.max_position_size * 100,  # Convert to percentage
                "risk_per_trade": risk_manager.risk_per_trade * 100,
                "max_drawdown": risk_manager.max_drawdown * 100,
                "max_daily_loss": risk_manager.max_daily_loss * 100,
                "max_leverage": risk_manager.max_leverage,
                "max_concurrent_positions": risk_manager.max_concurrent_positions
            },
            "current_metrics": {
                "current_drawdown": current_drawdown * 100,
                "daily_pnl": risk_manager.daily_pnl,
                "daily_pnl_pct": daily_pnl_pct,
                "daily_loss": risk_manager.daily_loss,
                "concentration_ratio": concentration_ratio * 100,
                "active_positions": len(portfolio.positions),
                "total_exposure": total_position_value,
                "available_capital": portfolio.cash,
                "total_portfolio_value": portfolio.total_value
            },
            "performance_metrics": {
                "sharpe_ratio": risk_manager.sharpe_ratio,
                "sortino_ratio": risk_manager.sortino_ratio,
                "consecutive_losses": risk_manager.current_consecutive_losses,
                "max_consecutive_losses": risk_manager.max_consecutive_losses,
                "volatility_adjustment": risk_manager.volatility_adjustment,
                "confidence_threshold": risk_manager.confidence_threshold
            },
            "risk_status": {
                "circuit_breaker_active": risk_manager.circuit_breaker_triggered,
                "risk_level": "HIGH" if current_drawdown > 0.1 or risk_manager.current_consecutive_losses > 3 else
                           "MEDIUM" if current_drawdown > 0.05 or risk_manager.current_consecutive_losses > 1 else "LOW",
                "trading_allowed": not risk_manager.circuit_breaker_triggered,
                "last_risk_update": datetime.now().isoformat()
            },
            "position_sizing": {
                "sizing_method": risk_manager.sizing_method,
                "base_position_size": risk_manager.base_max_position_size * 100,
                "adjusted_position_size": risk_manager.max_position_size * 100,
                "base_risk_per_trade": risk_manager.base_risk_per_trade * 100,
                "adjusted_risk_per_trade": risk_manager.risk_per_trade * 100
            }
        }

    except Exception as e:
        print(f"❌ Error getting risk metrics: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get risk metrics: {str(e)}")


@app.post("/api/emergency-stop")
async def emergency_stop():
    """🚨 EMERGENCY STOP - Trigger circuit breaker and halt all trading."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Trigger circuit breaker
        trading_ai.risk_manager._trigger_circuit_breaker("MANUAL_EMERGENCY_STOP")

        # Stop trading loop if running
        if trading_ai.is_running:
            trading_ai.is_running = False

        return {
            "success": True,
            "message": "Emergency stop activated - all trading halted",
            "circuit_breaker_active": True,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        print(f"❌ Error triggering emergency stop: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to trigger emergency stop: {str(e)}")


@app.post("/api/reset-circuit-breaker")
async def reset_circuit_breaker():
    """🔄 RESET CIRCUIT BREAKER - Manually reset after emergency stop."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Reset circuit breaker
        trading_ai.risk_manager.circuit_breaker_triggered = False
        trading_ai.risk_manager.current_consecutive_losses = 0

        return {
            "success": True,
            "message": "Circuit breaker reset - trading can resume",
            "circuit_breaker_active": False,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        print(f"❌ Error resetting circuit breaker: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to reset circuit breaker: {str(e)}")


@app.get("/api/portfolio-analytics")
async def get_portfolio_analytics():
    """📊 GET ADVANCED PORTFOLIO ANALYTICS with correlation analysis and optimization."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        portfolio = trading_ai.portfolio
        market_data = trading_ai.market_data

        if len(market_data) < 30:
            raise HTTPException(status_code=400, detail="Insufficient data for advanced analytics")

        # Convert market data to DataFrame for analysis
        df = trading_ai._market_data_to_dataframe()

        # Calculate advanced metrics
        analytics = await _calculate_advanced_analytics(df, portfolio, trading_ai.signals_history)

        return analytics

    except Exception as e:
        print(f"❌ Error calculating portfolio analytics: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to calculate portfolio analytics: {str(e)}")


@app.post("/api/monte-carlo-simulation")
async def run_monte_carlo_simulation(
    num_simulations: int = 1000,
    time_horizon_days: int = 30,
    confidence_level: float = 0.95
):
    """🎲 RUN MONTE CARLO SIMULATION for portfolio risk assessment."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        if len(trading_ai.market_data) < 50:
            raise HTTPException(status_code=400, detail="Insufficient historical data for Monte Carlo simulation")

        # Run Monte Carlo simulation
        simulation_results = await _run_monte_carlo_simulation(
            trading_ai, num_simulations, time_horizon_days, confidence_level
        )

        return simulation_results

    except Exception as e:
        print(f"❌ Error running Monte Carlo simulation: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to run Monte Carlo simulation: {str(e)}")


@app.get("/api/strategy-performance")
async def get_strategy_performance():
    """📈 GET DETAILED STRATEGY PERFORMANCE ANALYSIS."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        performance_data = trading_ai.performance_tracker.get_performance_summary()

        # Add advanced performance metrics
        enhanced_performance = {}
        for strategy_name, perf in performance_data.items():
            enhanced_performance[strategy_name] = {
                **perf,
                'sharpe_ratio': _calculate_strategy_sharpe_ratio(strategy_name, trading_ai.signals_history),
                'max_drawdown': _calculate_strategy_max_drawdown(strategy_name, trading_ai.signals_history),
                'profit_factor': _calculate_profit_factor(perf),
                'win_streak': _calculate_win_streak(strategy_name, trading_ai.signals_history),
                'loss_streak': _calculate_loss_streak(strategy_name, trading_ai.signals_history)
            }

        return {
            "strategy_performance": enhanced_performance,
            "overall_metrics": {
                "total_strategies": len(enhanced_performance),
                "best_strategy": max(enhanced_performance.keys(),
                                   key=lambda x: enhanced_performance[x]['win_rate']) if enhanced_performance else None,
                "worst_strategy": min(enhanced_performance.keys(),
                                    key=lambda x: enhanced_performance[x]['win_rate']) if enhanced_performance else None,
                "average_win_rate": sum(p['win_rate'] for p in enhanced_performance.values()) / len(enhanced_performance) if enhanced_performance else 0
            }
        }

    except Exception as e:
        print(f"❌ Error getting strategy performance: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get strategy performance: {str(e)}")


async def _calculate_advanced_analytics(df: pd.DataFrame, portfolio, signals_history):
    """Calculate advanced portfolio analytics."""
    import numpy as np
    from scipy import stats

    # Basic price statistics
    returns = df['close'].pct_change().dropna()

    # Volatility metrics
    daily_volatility = returns.std()
    annualized_volatility = daily_volatility * np.sqrt(252)

    # Risk metrics
    var_95 = np.percentile(returns, 5)  # 95% Value at Risk
    cvar_95 = returns[returns <= var_95].mean()  # Conditional VaR

    # Performance metrics
    total_return = (df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 100
    sharpe_ratio = (returns.mean() / daily_volatility * np.sqrt(252)) if daily_volatility > 0 else 0

    # Drawdown analysis
    cumulative_returns = (1 + returns).cumprod()
    running_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdown.min() * 100

    # Correlation analysis (with market proxy - using price momentum as proxy)
    price_momentum = df['close'].rolling(10).mean().pct_change()
    correlation_with_momentum = returns.corr(price_momentum.dropna())

    # Advanced statistics
    skewness = stats.skew(returns)
    kurtosis = stats.kurtosis(returns)

    # Portfolio composition
    total_value = portfolio.total_value
    cash_ratio = portfolio.cash / total_value if total_value > 0 else 1.0
    position_count = len(portfolio.positions)

    # Signal analysis
    signal_stats = _analyze_signals(signals_history)

    return {
        "risk_metrics": {
            "daily_volatility": daily_volatility * 100,
            "annualized_volatility": annualized_volatility * 100,
            "value_at_risk_95": var_95 * 100,
            "conditional_var_95": cvar_95 * 100,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "skewness": skewness,
            "kurtosis": kurtosis
        },
        "performance_metrics": {
            "total_return": total_return,
            "annualized_return": total_return * (252 / len(returns)) if len(returns) > 0 else 0,
            "win_rate": signal_stats['win_rate'],
            "profit_factor": signal_stats['profit_factor'],
            "average_trade_return": signal_stats['avg_return']
        },
        "portfolio_composition": {
            "total_value": total_value,
            "cash_ratio": cash_ratio * 100,
            "invested_ratio": (1 - cash_ratio) * 100,
            "position_count": position_count,
            "diversification_ratio": min(position_count / 5, 1.0) * 100  # Assuming max 5 positions for full diversification
        },
        "correlation_analysis": {
            "momentum_correlation": correlation_with_momentum,
            "market_beta": correlation_with_momentum * (returns.std() / price_momentum.std()) if price_momentum.std() > 0 else 0
        },
        "signal_analysis": signal_stats
    }


async def _run_monte_carlo_simulation(trading_ai, num_simulations, time_horizon_days, confidence_level):
    """Run Monte Carlo simulation for portfolio projections."""
    import numpy as np

    # Get historical returns
    df = trading_ai._market_data_to_dataframe()
    returns = df['close'].pct_change().dropna()

    if len(returns) < 30:
        raise ValueError("Insufficient historical data for simulation")

    # Calculate return statistics
    mean_return = returns.mean()
    std_return = returns.std()

    # Current portfolio value
    current_value = trading_ai.portfolio.total_value

    # Run simulations
    simulation_results = []
    final_values = []

    for _ in range(num_simulations):
        # Generate random returns for the time horizon
        random_returns = np.random.normal(mean_return, std_return, time_horizon_days)

        # Calculate portfolio value path
        portfolio_values = [current_value]
        for daily_return in random_returns:
            new_value = portfolio_values[-1] * (1 + daily_return)
            portfolio_values.append(new_value)

        simulation_results.append(portfolio_values)
        final_values.append(portfolio_values[-1])

    # Calculate statistics
    final_values = np.array(final_values)

    # Confidence intervals
    lower_percentile = (1 - confidence_level) / 2 * 100
    upper_percentile = (1 + confidence_level) / 2 * 100

    confidence_interval_lower = np.percentile(final_values, lower_percentile)
    confidence_interval_upper = np.percentile(final_values, upper_percentile)

    # Risk metrics
    probability_of_loss = (final_values < current_value).mean() * 100
    expected_return = (final_values.mean() / current_value - 1) * 100

    # Value at Risk
    var_level = (1 - confidence_level) * 100
    var_value = np.percentile(final_values, var_level)
    var_loss = (current_value - var_value) / current_value * 100

    return {
        "simulation_parameters": {
            "num_simulations": num_simulations,
            "time_horizon_days": time_horizon_days,
            "confidence_level": confidence_level,
            "current_portfolio_value": current_value
        },
        "results": {
            "expected_final_value": final_values.mean(),
            "expected_return_pct": expected_return,
            "confidence_interval": {
                "lower": confidence_interval_lower,
                "upper": confidence_interval_upper,
                "lower_return_pct": (confidence_interval_lower / current_value - 1) * 100,
                "upper_return_pct": (confidence_interval_upper / current_value - 1) * 100
            },
            "risk_metrics": {
                "probability_of_loss": probability_of_loss,
                "value_at_risk": var_value,
                "var_loss_pct": var_loss,
                "standard_deviation": final_values.std()
            }
        },
        "percentiles": {
            "p5": np.percentile(final_values, 5),
            "p10": np.percentile(final_values, 10),
            "p25": np.percentile(final_values, 25),
            "p50": np.percentile(final_values, 50),
            "p75": np.percentile(final_values, 75),
            "p90": np.percentile(final_values, 90),
            "p95": np.percentile(final_values, 95)
        }
    }


def _analyze_signals(signals_history):
    """Analyze trading signals for performance metrics."""
    if not signals_history:
        return {
            "total_signals": 0,
            "win_rate": 0.0,
            "avg_return": 0.0,
            "profit_factor": 0.0,
            "signal_distribution": {"BUY": 0, "SELL": 0, "HOLD": 0}
        }

    # Count signal types
    signal_counts = {"BUY": 0, "SELL": 0, "HOLD": 0}
    for signal in signals_history:
        signal_counts[signal.signal.value] += 1

    # Simulate returns (in real implementation, use actual trade outcomes)
    wins = 0
    total_return = 0.0
    positive_returns = []
    negative_returns = []

    for signal in signals_history:
        # Simulate return based on confidence (placeholder logic)
        simulated_return = (signal.confidence - 0.5) * 0.02  # ±2% max return
        if simulated_return > 0:
            wins += 1
            positive_returns.append(simulated_return)
        else:
            negative_returns.append(abs(simulated_return))
        total_return += simulated_return

    win_rate = wins / len(signals_history) if signals_history else 0
    avg_return = total_return / len(signals_history) if signals_history else 0

    # Profit factor
    gross_profit = sum(positive_returns) if positive_returns else 0
    gross_loss = sum(negative_returns) if negative_returns else 0
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0

    return {
        "total_signals": len(signals_history),
        "win_rate": win_rate,
        "avg_return": avg_return,
        "profit_factor": profit_factor,
        "signal_distribution": signal_counts,
        "gross_profit": gross_profit,
        "gross_loss": gross_loss
    }


def _calculate_strategy_sharpe_ratio(strategy_name, signals_history):
    """Calculate Sharpe ratio for a specific strategy."""
    strategy_signals = [s for s in signals_history if s.strategy.value == strategy_name]
    if len(strategy_signals) < 2:
        return 0.0

    # Simulate returns for the strategy
    returns = [(s.confidence - 0.5) * 0.02 for s in strategy_signals]
    mean_return = sum(returns) / len(returns)
    std_return = (sum((r - mean_return) ** 2 for r in returns) / len(returns)) ** 0.5

    return (mean_return / std_return * (252 ** 0.5)) if std_return > 0 else 0.0


def _calculate_strategy_max_drawdown(strategy_name, signals_history):
    """Calculate maximum drawdown for a specific strategy."""
    strategy_signals = [s for s in signals_history if s.strategy.value == strategy_name]
    if len(strategy_signals) < 2:
        return 0.0

    # Simulate cumulative returns
    cumulative_return = 1.0
    peak = 1.0
    max_drawdown = 0.0

    for signal in strategy_signals:
        return_pct = (signal.confidence - 0.5) * 0.02
        cumulative_return *= (1 + return_pct)

        if cumulative_return > peak:
            peak = cumulative_return

        drawdown = (peak - cumulative_return) / peak
        max_drawdown = max(max_drawdown, drawdown)

    return max_drawdown * 100


def _calculate_profit_factor(performance_data):
    """Calculate profit factor from performance data."""
    if performance_data['losses'] == 0:
        return float('inf') if performance_data['wins'] > 0 else 0.0

    # Estimate gross profit and loss
    avg_return = performance_data.get('avg_return', 0)
    total_trades = performance_data['wins'] + performance_data['losses']

    if total_trades == 0:
        return 0.0

    # Simple estimation
    gross_profit = performance_data['wins'] * abs(avg_return) if avg_return > 0 else performance_data['wins'] * 0.02
    gross_loss = performance_data['losses'] * abs(avg_return) if avg_return < 0 else performance_data['losses'] * 0.02

    return gross_profit / gross_loss if gross_loss > 0 else float('inf')


def _calculate_win_streak(strategy_name, signals_history):
    """Calculate maximum winning streak for a strategy."""
    strategy_signals = [s for s in signals_history if s.strategy.value == strategy_name]
    if not strategy_signals:
        return 0

    max_streak = 0
    current_streak = 0

    for signal in strategy_signals:
        # Simulate win/loss based on confidence
        is_win = signal.confidence > 0.5

        if is_win:
            current_streak += 1
            max_streak = max(max_streak, current_streak)
        else:
            current_streak = 0

    return max_streak


def _calculate_loss_streak(strategy_name, signals_history):
    """Calculate maximum losing streak for a strategy."""
    strategy_signals = [s for s in signals_history if s.strategy.value == strategy_name]
    if not strategy_signals:
        return 0

    max_streak = 0
    current_streak = 0

    for signal in strategy_signals:
        # Simulate win/loss based on confidence
        is_loss = signal.confidence <= 0.5

        if is_loss:
            current_streak += 1
            max_streak = max(max_streak, current_streak)
        else:
            current_streak = 0

    return max_streak


@app.get("/api/notifications")
async def get_notifications(limit: int = 50):
    """📢 GET NOTIFICATION HISTORY."""
    global trading_ai

    if not trading_ai or not trading_ai.notification_manager:
        return {"notifications": [], "stats": {}}

    try:
        history = await trading_ai.notification_manager.get_notification_history(limit)
        stats = await trading_ai.notification_manager.get_notification_stats()

        return {
            "notifications": history,
            "stats": stats
        }

    except Exception as e:
        print(f"❌ Error getting notifications: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get notifications: {str(e)}")


@app.post("/api/notifications/config")
async def update_notification_config(config_data: dict):
    """⚙️ UPDATE NOTIFICATION CONFIGURATION."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        from src.notification_service import NotificationConfig

        # Create new config from provided data
        new_config = NotificationConfig(
            email_enabled=config_data.get('email_enabled', False),
            sms_enabled=config_data.get('sms_enabled', False),
            push_enabled=config_data.get('push_enabled', False),
            in_app_enabled=config_data.get('in_app_enabled', True),
            webhook_enabled=config_data.get('webhook_enabled', False),
            email_address=config_data.get('email_address'),
            smtp_server=config_data.get('smtp_server', 'smtp.gmail.com'),
            smtp_port=config_data.get('smtp_port', 587),
            smtp_username=config_data.get('smtp_username'),
            smtp_password=config_data.get('smtp_password'),
            phone_number=config_data.get('phone_number'),
            sms_provider=config_data.get('sms_provider', 'twilio'),
            twilio_account_sid=config_data.get('twilio_account_sid'),
            twilio_auth_token=config_data.get('twilio_auth_token'),
            twilio_phone_number=config_data.get('twilio_phone_number'),
            push_service=config_data.get('push_service', 'firebase'),
            firebase_server_key=config_data.get('firebase_server_key'),
            webhook_url=config_data.get('webhook_url'),
            webhook_secret=config_data.get('webhook_secret'),
            min_signal_confidence=config_data.get('min_signal_confidence', 0.7),
            min_risk_score=config_data.get('min_risk_score', 0.8),
            min_trade_size=config_data.get('min_trade_size', 100.0),
            performance_milestone_threshold=config_data.get('performance_milestone_threshold', 5.0)
        )

        # Update notification manager config
        if trading_ai.notification_manager:
            trading_ai.notification_manager.update_config(new_config)
        else:
            # Initialize notification manager if not exists
            from src.notification_manager import NotificationManager
            trading_ai.notification_manager = NotificationManager(new_config)

        return {"message": "Notification configuration updated successfully"}

    except Exception as e:
        print(f"❌ Error updating notification config: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to update notification config: {str(e)}")


@app.post("/api/notifications/test")
async def test_notification(notification_data: dict):
    """🧪 SEND TEST NOTIFICATION."""
    global trading_ai

    if not trading_ai or not trading_ai.notification_manager:
        raise HTTPException(status_code=500, detail="Notification system not initialized")

    try:
        from src.notification_service import (
            Notification, NotificationType, NotificationPriority, NotificationChannel
        )
        from datetime import datetime
        import uuid

        # Create test notification
        test_notification = Notification(
            id=str(uuid.uuid4()),
            type=NotificationType.SYSTEM_ERROR,  # Use system error for test
            priority=NotificationPriority.MEDIUM,
            title=notification_data.get('title', 'Test Notification'),
            message=notification_data.get('message', 'This is a test notification from AutoTradz AI'),
            data=notification_data.get('data', {'test': True}),
            timestamp=datetime.now(),
            channels=[NotificationChannel.IN_APP, NotificationChannel.EMAIL]
        )

        success = await trading_ai.notification_manager.service.send_notification(test_notification)

        return {
            "success": success,
            "message": "Test notification sent" if success else "Test notification failed"
        }

    except Exception as e:
        print(f"❌ Error sending test notification: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to send test notification: {str(e)}")


@app.get("/api/trading-pairs")
async def get_trading_pairs():
    """📊 GET AVAILABLE TRADING PAIRS AND THEIR CONFIGURATION."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        pairs_config = trading_ai.multi_asset_manager.get_trading_pairs_config()

        # Add current market data for each pair
        enhanced_pairs = []
        for pair_config in pairs_config:
            symbol = pair_config['symbol']

            # Get current price if available
            current_price = None
            if symbol in trading_ai.multi_asset_data and trading_ai.multi_asset_data[symbol]:
                current_price = trading_ai.multi_asset_data[symbol][-1].close

            enhanced_pair = {
                **pair_config,
                'current_price': current_price,
                'is_active': symbol == trading_ai.current_trading_pair
            }
            enhanced_pairs.append(enhanced_pair)

        return {
            "trading_pairs": enhanced_pairs,
            "current_pair": trading_ai.current_trading_pair,
            "total_pairs": len(enhanced_pairs),
            "enabled_pairs": len([p for p in enhanced_pairs if p['enabled']])
        }

    except Exception as e:
        print(f"❌ Error getting trading pairs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get trading pairs: {str(e)}")


@app.post("/api/trading-pairs/{symbol}/switch")
async def switch_trading_pair(symbol: str):
    """🔄 SWITCH ACTIVE TRADING PAIR."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Validate pair exists
        pair = trading_ai.multi_asset_manager.get_pair_by_symbol(symbol)
        if not pair:
            raise HTTPException(status_code=404, detail=f"Trading pair {symbol} not found")

        if not pair.enabled:
            raise HTTPException(status_code=400, detail=f"Trading pair {symbol} is disabled")

        # Switch the active pair
        old_pair = trading_ai.current_trading_pair
        trading_ai.current_trading_pair = symbol

        # Initialize data for new pair if needed
        if symbol not in trading_ai.multi_asset_data:
            trading_ai.multi_asset_data[symbol] = []

        return {
            "message": f"Switched trading pair from {old_pair} to {symbol}",
            "old_pair": old_pair,
            "new_pair": symbol,
            "pair_config": pair.__dict__
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error switching trading pair: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to switch trading pair: {str(e)}")


@app.get("/api/portfolio-allocation")
async def get_portfolio_allocation():
    """📈 GET CURRENT PORTFOLIO ALLOCATION ACROSS ASSETS."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Calculate current allocations based on portfolio positions
        current_allocations = {}
        total_value = trading_ai.portfolio.total_value

        if total_value > 0:
            # Cash allocation
            cash_allocation = trading_ai.portfolio.cash / total_value
            current_allocations["USD"] = cash_allocation

            # Asset allocations
            for position in trading_ai.portfolio.positions.values():
                position_value = position.size * position.current_price
                allocation = position_value / total_value

                # Extract base asset from position product_id
                base_asset = position.product_id.split('-')[0]
                current_allocations[base_asset] = allocation

        # Get optimal allocations
        market_data_df = {}
        for symbol, data in trading_ai.multi_asset_data.items():
            if data:
                df_data = []
                for md in data:
                    df_data.append({
                        'timestamp': md.timestamp,
                        'close': md.close,
                        'volume': md.volume
                    })
                market_data_df[symbol] = pd.DataFrame(df_data)

        optimal_allocations = trading_ai.multi_asset_manager.calculate_optimal_allocation(market_data_df)

        # Calculate diversification metrics
        diversification_metrics = trading_ai.multi_asset_manager.check_diversification_risk(current_allocations)

        # Get rebalancing suggestions
        rebalancing_suggestions = trading_ai.multi_asset_manager.suggest_rebalancing(
            current_allocations, optimal_allocations
        )

        return {
            "current_allocations": current_allocations,
            "optimal_allocations": optimal_allocations,
            "diversification_metrics": diversification_metrics,
            "rebalancing_suggestions": rebalancing_suggestions,
            "total_portfolio_value": total_value
        }

    except Exception as e:
        print(f"❌ Error getting portfolio allocation: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get portfolio allocation: {str(e)}")


@app.get("/api/asset-performance")
async def get_asset_performance():
    """📊 GET PERFORMANCE METRICS FOR ALL ASSETS."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Prepare market data
        market_data_df = {}
        for symbol, data in trading_ai.multi_asset_data.items():
            if data and len(data) > 1:
                df_data = []
                for md in data:
                    df_data.append({
                        'timestamp': md.timestamp,
                        'open': md.open,
                        'high': md.high,
                        'low': md.low,
                        'close': md.close,
                        'volume': md.volume
                    })
                market_data_df[symbol] = pd.DataFrame(df_data)

        # Get performance summary
        performance_summary = trading_ai.multi_asset_manager.get_asset_performance_summary(market_data_df)

        # Calculate correlation matrix
        correlation_matrix = trading_ai.multi_asset_manager.calculate_correlation_matrix(market_data_df)

        return {
            "asset_performance": performance_summary,
            "correlation_matrix": correlation_matrix.to_dict() if not correlation_matrix.empty else {},
            "analysis_timestamp": datetime.now().isoformat(),
            "assets_analyzed": len(performance_summary)
        }

    except Exception as e:
        print(f"❌ Error getting asset performance: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get asset performance: {str(e)}")


@app.post("/api/trading-pairs/{symbol}/config")
async def update_trading_pair_config(symbol: str, config: dict):
    """⚙️ UPDATE TRADING PAIR CONFIGURATION."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        success = trading_ai.multi_asset_manager.update_pair_config(symbol, config)

        if success:
            return {
                "message": f"Updated configuration for {symbol}",
                "symbol": symbol,
                "updated_config": config
            }
        else:
            raise HTTPException(status_code=404, detail=f"Trading pair {symbol} not found")

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error updating trading pair config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update trading pair config: {str(e)}")


@app.post("/api/backtest/run")
async def run_backtest(backtest_config: dict):
    """🔄 RUN COMPREHENSIVE BACKTEST ON HISTORICAL DATA."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        from src.backtesting_engine import BacktestingEngine, BacktestConfig
        from datetime import datetime, timedelta

        # Parse configuration
        config = BacktestConfig(
            initial_capital=backtest_config.get('initial_capital', 10000.0),
            commission_rate=backtest_config.get('commission_rate', 0.001),
            slippage_rate=backtest_config.get('slippage_rate', 0.0005),
            max_position_size=backtest_config.get('max_position_size', 0.25),
            stop_loss_pct=backtest_config.get('stop_loss_pct', 0.05),
            take_profit_pct=backtest_config.get('take_profit_pct', 0.10),
            max_open_positions=backtest_config.get('max_open_positions', 3),
            allow_short_selling=backtest_config.get('allow_short_selling', False),
            compound_returns=backtest_config.get('compound_returns', True),
            risk_free_rate=backtest_config.get('risk_free_rate', 0.02)
        )

        # Parse date range
        start_date = None
        end_date = None

        if 'start_date' in backtest_config:
            start_date = datetime.fromisoformat(backtest_config['start_date'].replace('Z', '+00:00'))

        if 'end_date' in backtest_config:
            end_date = datetime.fromisoformat(backtest_config['end_date'].replace('Z', '+00:00'))

        # Default to last 30 days if no dates specified
        if not start_date and not end_date:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

        # Get historical data and signals
        market_data = trading_ai.market_data
        signals = trading_ai.signals_history

        if not market_data or not signals:
            raise HTTPException(status_code=400, detail="Insufficient historical data for backtesting")

        # Run backtest
        engine = BacktestingEngine(config)
        results = await engine.run_backtest(market_data, signals, start_date, end_date)

        return {
            "status": "completed",
            "config": asdict(config),
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to run backtest: {str(e)}")


@app.get("/api/backtest/presets")
async def get_backtest_presets():
    """📋 GET PREDEFINED BACKTEST CONFIGURATIONS."""
    try:
        presets = {
            "conservative": {
                "name": "Conservative Trading",
                "description": "Low risk, steady returns",
                "config": {
                    "initial_capital": 10000.0,
                    "commission_rate": 0.001,
                    "slippage_rate": 0.0005,
                    "max_position_size": 0.15,  # 15% max position
                    "stop_loss_pct": 0.03,      # 3% stop loss
                    "take_profit_pct": 0.06,    # 6% take profit
                    "max_open_positions": 2,
                    "allow_short_selling": False,
                    "compound_returns": True,
                    "risk_free_rate": 0.02
                }
            },
            "moderate": {
                "name": "Moderate Trading",
                "description": "Balanced risk and return",
                "config": {
                    "initial_capital": 10000.0,
                    "commission_rate": 0.001,
                    "slippage_rate": 0.0005,
                    "max_position_size": 0.25,  # 25% max position
                    "stop_loss_pct": 0.05,      # 5% stop loss
                    "take_profit_pct": 0.10,    # 10% take profit
                    "max_open_positions": 3,
                    "allow_short_selling": False,
                    "compound_returns": True,
                    "risk_free_rate": 0.02
                }
            },
            "aggressive": {
                "name": "Aggressive Trading",
                "description": "High risk, high potential returns",
                "config": {
                    "initial_capital": 10000.0,
                    "commission_rate": 0.001,
                    "slippage_rate": 0.0005,
                    "max_position_size": 0.40,  # 40% max position
                    "stop_loss_pct": 0.08,      # 8% stop loss
                    "take_profit_pct": 0.15,    # 15% take profit
                    "max_open_positions": 5,
                    "allow_short_selling": True,
                    "compound_returns": True,
                    "risk_free_rate": 0.02
                }
            },
            "scalping": {
                "name": "Scalping Strategy",
                "description": "High frequency, small profits",
                "config": {
                    "initial_capital": 10000.0,
                    "commission_rate": 0.001,
                    "slippage_rate": 0.0003,    # Lower slippage for scalping
                    "max_position_size": 0.20,  # 20% max position
                    "stop_loss_pct": 0.02,      # 2% stop loss
                    "take_profit_pct": 0.03,    # 3% take profit
                    "max_open_positions": 1,    # Single position focus
                    "allow_short_selling": True,
                    "compound_returns": True,
                    "risk_free_rate": 0.02
                }
            }
        }

        return {"presets": presets}

    except Exception as e:
        print(f"❌ Error getting backtest presets: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get backtest presets: {str(e)}")


@app.get("/api/backtest/history")
async def get_backtest_history(limit: int = 10):
    """📊 GET BACKTEST HISTORY AND RESULTS."""
    try:
        # In a real implementation, this would fetch from a database
        # For now, return mock data structure

        mock_history = [
            {
                "id": "bt_001",
                "timestamp": "2024-01-15T10:30:00Z",
                "config_name": "Moderate Trading",
                "duration_days": 30,
                "total_return": 12.5,
                "max_drawdown": 3.2,
                "sharpe_ratio": 1.8,
                "total_trades": 45,
                "win_rate": 67.8,
                "status": "completed"
            },
            {
                "id": "bt_002",
                "timestamp": "2024-01-10T14:20:00Z",
                "config_name": "Conservative Trading",
                "duration_days": 30,
                "total_return": 8.3,
                "max_drawdown": 1.8,
                "sharpe_ratio": 2.1,
                "total_trades": 28,
                "win_rate": 75.0,
                "status": "completed"
            }
        ]

        return {
            "backtest_history": mock_history[:limit],
            "total_backtests": len(mock_history)
        }

    except Exception as e:
        print(f"❌ Error getting backtest history: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get backtest history: {str(e)}")


@app.post("/api/backtest/optimize")
async def optimize_strategy_parameters(optimization_config: dict):
    """🎯 OPTIMIZE STRATEGY PARAMETERS USING GRID SEARCH."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        from src.backtesting_engine import BacktestingEngine, BacktestConfig
        from datetime import datetime, timedelta
        import itertools

        # Parameter ranges for optimization
        param_ranges = optimization_config.get('parameter_ranges', {
            'max_position_size': [0.15, 0.25, 0.35],
            'stop_loss_pct': [0.03, 0.05, 0.08],
            'take_profit_pct': [0.06, 0.10, 0.15]
        })

        # Base configuration
        base_config = optimization_config.get('base_config', {
            'initial_capital': 10000.0,
            'commission_rate': 0.001,
            'slippage_rate': 0.0005,
            'max_open_positions': 3,
            'allow_short_selling': False,
            'compound_returns': True,
            'risk_free_rate': 0.02
        })

        # Generate all parameter combinations
        param_names = list(param_ranges.keys())
        param_values = list(param_ranges.values())
        combinations = list(itertools.product(*param_values))

        # Limit combinations to prevent excessive computation
        max_combinations = optimization_config.get('max_combinations', 20)
        if len(combinations) > max_combinations:
            # Sample random combinations
            import random
            combinations = random.sample(combinations, max_combinations)

        # Run backtests for each combination
        optimization_results = []

        for i, combination in enumerate(combinations):
            # Create config for this combination
            config_dict = base_config.copy()
            for param_name, param_value in zip(param_names, combination):
                config_dict[param_name] = param_value

            config = BacktestConfig(**config_dict)

            # Run backtest
            engine = BacktestingEngine(config)

            # Use last 30 days of data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

            results = await engine.run_backtest(
                trading_ai.market_data,
                trading_ai.signals_history,
                start_date,
                end_date
            )

            if 'error' not in results:
                optimization_results.append({
                    'combination_id': i + 1,
                    'parameters': dict(zip(param_names, combination)),
                    'total_return': results['summary']['total_return'],
                    'sharpe_ratio': results['summary']['sharpe_ratio'],
                    'max_drawdown': results['summary']['max_drawdown'],
                    'win_rate': results['summary']['win_rate'],
                    'total_trades': results['summary']['total_trades'],
                    'profit_factor': results['summary']['profit_factor']
                })

        # Sort by Sharpe ratio (or other metric)
        optimization_metric = optimization_config.get('optimization_metric', 'sharpe_ratio')
        optimization_results.sort(key=lambda x: x.get(optimization_metric, 0), reverse=True)

        return {
            "optimization_results": optimization_results,
            "best_parameters": optimization_results[0] if optimization_results else None,
            "total_combinations_tested": len(optimization_results),
            "optimization_metric": optimization_metric
        }

    except Exception as e:
        print(f"❌ Error optimizing strategy parameters: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to optimize strategy parameters: {str(e)}")


@app.get("/api/market-intelligence/{symbol}")
async def get_market_intelligence(symbol: str):
    """🧠 GET COMPREHENSIVE MARKET INTELLIGENCE FOR A SYMBOL."""
    try:
        from src.enhanced_data_feeds import EnhancedDataFeedsManager

        # Initialize data feeds manager
        data_manager = EnhancedDataFeedsManager()
        await data_manager.initialize()

        try:
            # Get comprehensive market intelligence
            intelligence = await data_manager.get_market_intelligence(symbol)

            return {
                "symbol": intelligence.symbol,
                "timestamp": intelligence.timestamp.isoformat(),
                "price_data": intelligence.price_data,
                "volume_data": intelligence.volume_data,
                "sentiment_data": asdict(intelligence.sentiment_data),
                "technical_signals": intelligence.technical_signals,
                "market_metrics": {
                    "market_cap_rank": intelligence.market_cap_rank,
                    "social_dominance": intelligence.social_dominance
                }
            }

        finally:
            await data_manager.cleanup()

    except Exception as e:
        print(f"❌ Error getting market intelligence: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get market intelligence: {str(e)}")


@app.get("/api/sentiment-analysis/{symbol}")
async def get_sentiment_analysis(symbol: str):
    """📰 GET SENTIMENT ANALYSIS FROM NEWS AND SOCIAL MEDIA."""
    try:
        from src.enhanced_data_feeds import EnhancedDataFeedsManager

        data_manager = EnhancedDataFeedsManager()
        await data_manager.initialize()

        try:
            # Get market intelligence (includes sentiment)
            intelligence = await data_manager.get_market_intelligence(symbol)
            sentiment = intelligence.sentiment_data

            return {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "sentiment": {
                    "overall_sentiment": sentiment.overall_sentiment,
                    "sentiment_label": sentiment.sentiment_label,
                    "confidence": sentiment.confidence,
                    "news_count": sentiment.news_count,
                    "social_mentions": sentiment.social_mentions,
                    "fear_greed_index": sentiment.fear_greed_index,
                    "trending_keywords": sentiment.trending_keywords
                },
                "interpretation": {
                    "signal": "bullish" if sentiment.overall_sentiment > 0.1 else "bearish" if sentiment.overall_sentiment < -0.1 else "neutral",
                    "strength": abs(sentiment.overall_sentiment),
                    "reliability": sentiment.confidence
                }
            }

        finally:
            await data_manager.cleanup()

    except Exception as e:
        print(f"❌ Error getting sentiment analysis: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get sentiment analysis: {str(e)}")


@app.get("/api/data-sources/status")
async def get_data_sources_status():
    """📊 GET STATUS OF ALL DATA SOURCES."""
    try:
        from src.enhanced_data_feeds import EnhancedDataFeedsManager

        data_manager = EnhancedDataFeedsManager()
        await data_manager.initialize()

        try:
            status = await data_manager.get_data_sources_status()

            return {
                "data_sources": status,
                "summary": {
                    "total_sources": len(status),
                    "enabled_sources": len([s for s in status.values() if s['enabled']]),
                    "sources_with_api_keys": len([s for s in status.values() if s['has_api_key']]),
                    "overall_health": "good" if len([s for s in status.values() if s['enabled']]) >= 3 else "limited"
                }
            }

        finally:
            await data_manager.cleanup()

    except Exception as e:
        print(f"❌ Error getting data sources status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get data sources status: {str(e)}")


@app.post("/api/data-sources/configure")
async def configure_data_sources(config: dict):
    """⚙️ CONFIGURE DATA SOURCES AND API KEYS."""
    try:
        from src.enhanced_data_feeds import EnhancedDataFeedsManager, DataSource

        data_manager = EnhancedDataFeedsManager()

        # Set API keys if provided
        api_keys = config.get('api_keys', {})
        for source_name, api_key in api_keys.items():
            try:
                source = DataSource(source_name.upper())
                data_manager.set_api_key(source, api_key)
            except ValueError:
                print(f"Unknown data source: {source_name}")

        # Enable/disable sources
        source_settings = config.get('enabled_sources', {})
        for source_name, enabled in source_settings.items():
            try:
                source = DataSource(source_name.upper())
                data_manager.data_sources[source] = enabled
            except ValueError:
                print(f"Unknown data source: {source_name}")

        return {
            "message": "Data sources configured successfully",
            "configured_sources": list(api_keys.keys()),
            "enabled_sources": [k for k, v in source_settings.items() if v]
        }

    except Exception as e:
        print(f"❌ Error configuring data sources: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to configure data sources: {str(e)}")


@app.get("/api/fear-greed-index")
async def get_fear_greed_index():
    """😨 GET CRYPTO FEAR & GREED INDEX."""
    try:
        from src.enhanced_data_feeds import EnhancedDataFeedsManager

        data_manager = EnhancedDataFeedsManager()
        await data_manager.initialize()

        try:
            fear_greed = await data_manager._get_fear_greed_index()

            if fear_greed is not None:
                # Interpret the index
                if fear_greed <= 25:
                    interpretation = "Extreme Fear"
                    signal = "bullish"  # Contrarian indicator
                elif fear_greed <= 45:
                    interpretation = "Fear"
                    signal = "slightly_bullish"
                elif fear_greed <= 55:
                    interpretation = "Neutral"
                    signal = "neutral"
                elif fear_greed <= 75:
                    interpretation = "Greed"
                    signal = "slightly_bearish"
                else:
                    interpretation = "Extreme Greed"
                    signal = "bearish"  # Contrarian indicator

                return {
                    "index_value": fear_greed,
                    "interpretation": interpretation,
                    "contrarian_signal": signal,
                    "timestamp": datetime.now().isoformat(),
                    "description": f"Fear & Greed Index is at {fear_greed}/100, indicating {interpretation.lower()}"
                }
            else:
                raise HTTPException(status_code=503, detail="Fear & Greed Index data unavailable")

        finally:
            await data_manager.cleanup()

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error getting Fear & Greed Index: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get Fear & Greed Index: {str(e)}")


@app.get("/api/crypto-news/{symbol}")
async def get_crypto_news(symbol: str, limit: int = 10):
    """📰 GET LATEST CRYPTOCURRENCY NEWS FOR A SYMBOL."""
    try:
        from src.enhanced_data_feeds import EnhancedDataFeedsManager

        data_manager = EnhancedDataFeedsManager()
        await data_manager.initialize()

        try:
            news_items = await data_manager._get_crypto_news(symbol)

            # Limit results
            limited_news = news_items[:limit]

            # Add sentiment analysis to each news item
            enhanced_news = []
            for item in limited_news:
                try:
                    from textblob import TextBlob
                    blob = TextBlob(f"{item['title']} {item.get('description', '')}")
                    sentiment = blob.sentiment.polarity

                    enhanced_item = {
                        **item,
                        'sentiment_score': sentiment,
                        'sentiment_label': 'bullish' if sentiment > 0.1 else 'bearish' if sentiment < -0.1 else 'neutral'
                    }
                    enhanced_news.append(enhanced_item)
                except:
                    enhanced_news.append({**item, 'sentiment_score': 0.0, 'sentiment_label': 'neutral'})

            return {
                "symbol": symbol,
                "news_count": len(enhanced_news),
                "news_items": enhanced_news,
                "timestamp": datetime.now().isoformat()
            }

        finally:
            await data_manager.cleanup()

    except Exception as e:
        print(f"❌ Error getting crypto news: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get crypto news: {str(e)}")


@app.get("/api/market-overview")
async def get_market_overview():
    """🌍 GET COMPREHENSIVE MARKET OVERVIEW."""
    try:
        from src.enhanced_data_feeds import EnhancedDataFeedsManager

        data_manager = EnhancedDataFeedsManager()
        await data_manager.initialize()

        try:
            # Get data for major cryptocurrencies
            major_symbols = ['BTC-USD', 'ETH-USD', 'XRP-USD', 'ADA-USD']
            market_data = {}

            for symbol in major_symbols:
                try:
                    intelligence = await data_manager.get_market_intelligence(symbol)
                    market_data[symbol] = {
                        'price': intelligence.price_data.get('average', 0),
                        '24h_change': intelligence.price_data.get('24h_change', 0),
                        'volume': intelligence.volume_data.get('24h_volume', 0),
                        'sentiment': intelligence.sentiment_data.sentiment_label,
                        'sentiment_score': intelligence.sentiment_data.overall_sentiment
                    }
                except Exception as e:
                    print(f"Error getting data for {symbol}: {e}")
                    market_data[symbol] = {
                        'price': 0,
                        '24h_change': 0,
                        'volume': 0,
                        'sentiment': 'neutral',
                        'sentiment_score': 0.0
                    }

            # Get Fear & Greed Index
            fear_greed = await data_manager._get_fear_greed_index()

            # Calculate market sentiment
            sentiment_scores = [data['sentiment_score'] for data in market_data.values()]
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0

            return {
                "market_data": market_data,
                "market_sentiment": {
                    "average_sentiment": avg_sentiment,
                    "sentiment_label": 'bullish' if avg_sentiment > 0.1 else 'bearish' if avg_sentiment < -0.1 else 'neutral',
                    "fear_greed_index": fear_greed
                },
                "timestamp": datetime.now().isoformat()
            }

        finally:
            await data_manager.cleanup()

    except Exception as e:
        print(f"❌ Error getting market overview: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get market overview: {str(e)}")


@app.post("/api/fetch-5year-data")
async def fetch_5year_historical_data():
    """Fetch 5-year historical data for all crypto pairs from Alpaca API"""
    try:
        logger.info("🚀 Starting 5-year historical data fetch for all pairs")

        # Get timeframes from request or use defaults
        timeframes = ['1Day', '1Hour', '15Min']

        # Fetch 5 years of data (1825 days)
        successful, failed = await historical_data_manager.fetch_all_pairs_data(
            timeframes=timeframes,
            days_back=1825
        )

        return {
            "success": True,
            "message": f"5-year data fetch completed: {successful} successful, {failed} failed",
            "successful": successful,
            "failed": failed,
            "timeframes": timeframes,
            "years": 5
        }

    except Exception as e:
        logger.error(f"❌ Failed to fetch 5-year data: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch 5-year data: {str(e)}")


# 🧠 AI DECISION LOGGING ENDPOINTS
@app.get("/api/ai/decision-logs")
async def get_ai_decision_logs():
    """Get recent AI decision logs showing AI thinking process."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Get recent decision logs
        recent_logs = trading_ai.ai_logger.get_recent_logs(10)

        logs_data = []
        for log in recent_logs:
            log_data = {
                "timestamp": log.timestamp.isoformat(),
                "market_data": log.market_data,
                "thoughts": [],
                "final_decision": None,
                "execution_plan": log.execution_plan
            }

            # Convert thoughts to serializable format
            for thought in log.thoughts:
                log_data["thoughts"].append({
                    "timestamp": thought.timestamp.isoformat(),
                    "component": thought.component,
                    "thought_type": thought.thought_type,
                    "content": thought.content,
                    "confidence": thought.confidence,
                    "data": thought.data
                })

            # Convert final decision
            if log.final_decision:
                log_data["final_decision"] = {
                    "signal": log.final_decision.signal.value if hasattr(log.final_decision.signal, 'value') else str(log.final_decision.signal),
                    "confidence": log.final_decision.confidence,
                    "price": log.final_decision.price,
                    "timestamp": log.final_decision.timestamp.isoformat(),
                    "reasoning": log.final_decision.reasoning,
                    "risk_score": log.final_decision.risk_score,
                    "position_size": getattr(log.final_decision, 'position_size', 0.0)
                }

            logs_data.append(log_data)

        return {"decision_logs": logs_data}

    except Exception as e:
        logger.error(f"Failed to get AI decision logs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get AI decision logs: {str(e)}")


@app.get("/api/ai/thoughts/{thought_type}")
async def get_ai_thoughts_by_type(thought_type: str):
    """Get AI thoughts by type (e.g., 'market_analysis', 'signal_generation', 'risk_analysis')."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Get thoughts by type
        thoughts = trading_ai.ai_logger.get_thoughts_by_type(thought_type, 20)

        thoughts_data = []
        for thought in thoughts:
            thoughts_data.append({
                "timestamp": thought.timestamp.isoformat(),
                "component": thought.component,
                "thought_type": thought.thought_type,
                "content": thought.content,
                "confidence": thought.confidence,
                "data": thought.data
            })

        return {"thoughts": thoughts_data, "thought_type": thought_type}

    except Exception as e:
        logger.error(f"Failed to get AI thoughts: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get AI thoughts: {str(e)}")


@app.get("/api/ai/current-analysis")
async def get_current_ai_analysis():
    """Get current AI market analysis and thinking."""
    global trading_ai

    if not trading_ai:
        raise HTTPException(status_code=500, detail="Trading AI not initialized")

    try:
        # Get the most recent decision log
        recent_logs = trading_ai.ai_logger.get_recent_logs(1)
        if not recent_logs:
            return {"analysis": None, "message": "No recent AI analysis available"}

        current_log = recent_logs[0]

        # Organize thoughts by type for easier frontend consumption
        thoughts_by_type = {}
        for thought in current_log.thoughts:
            if thought.thought_type not in thoughts_by_type:
                thoughts_by_type[thought.thought_type] = []
            thoughts_by_type[thought.thought_type].append({
                "timestamp": thought.timestamp.isoformat(),
                "component": thought.component,
                "content": thought.content,
                "confidence": thought.confidence,
                "data": thought.data
            })

        return {
            "timestamp": current_log.timestamp.isoformat(),
            "market_data": current_log.market_data,
            "thoughts_by_type": thoughts_by_type,
            "final_decision": {
                "signal": current_log.final_decision.signal.value if current_log.final_decision and hasattr(current_log.final_decision.signal, 'value') else "HOLD",
                "confidence": current_log.final_decision.confidence if current_log.final_decision else 0.0,
                "reasoning": current_log.final_decision.reasoning if current_log.final_decision else "No decision made"
            } if current_log.final_decision else None,
            "execution_plan": current_log.execution_plan
        }

    except Exception as e:
        logger.error(f"Failed to get current AI analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get current AI analysis: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(
        "backend.app:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
