"""
Alpaca Crypto API Client
Official Alpaca API integration for institutional-grade crypto trading
"""

import os
import time
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
import structlog
from decimal import Decimal
import uuid
import pandas as pd

# Try to import Alpaca SDK, provide helpful error if not available
try:
    from alpaca.trading.client import TradingClient
    from alpaca.data.historical import CryptoHistoricalDataClient
    from alpaca.data.live import CryptoDataStream
    from alpaca.data.requests import (
        CryptoBarsRequest,
        CryptoQuoteRequest,
        CryptoTradesRequest,
        CryptoLatestQuoteRequest,
        CryptoLatestTradeRequest,
        CryptoSnapshotRequest
    )
    from alpaca.data.timeframe import TimeFrame
    from alpaca.data.enums import CryptoFeed
    from alpaca.common.enums import Sort, SupportedCurrencies
    from alpaca.trading.requests import MarketOrderRequest, LimitOrderRequest
    from alpaca.trading.enums import OrderSide, TimeInForce, OrderType
    from alpaca.common.exceptions import APIError
    ALPACA_AVAILABLE = True
except ImportError as e:
    print("❌ Alpaca SDK not installed!")
    print("💡 Install with: pip install alpaca-py")
    print("📖 Or run: python install_alpaca.py")
    print(f"🔍 Import error: {e}")
    ALPACA_AVAILABLE = False

    # Create dummy classes to prevent import errors
    class TradingClient: pass
    class CryptoHistoricalDataClient: pass
    class CryptoDataStream: pass
    class CryptoBarsRequest: pass
    class CryptoQuoteRequest: pass
    class CryptoTradesRequest: pass
    class CryptoLatestQuoteRequest: pass
    class CryptoLatestTradeRequest: pass
    class CryptoSnapshotRequest: pass
    class TimeFrame: pass
    class CryptoFeed: pass
    class Sort: pass
    class SupportedCurrencies: pass
    class MarketOrderRequest: pass
    class LimitOrderRequest: pass
    class OrderSide: pass
    class TimeInForce: pass
    class OrderType: pass
    class APIError(Exception): pass

from config import settings
from src.enhanced_alpaca_client import EnhancedAlpacaClient

logger = structlog.get_logger(__name__)


class AlpacaCryptoAPI:
    """
    Alpaca Crypto API client for trading and market data.

    Authentication format:
    - api_key: "PK12345..." (Both paper and live trading keys start with PK)
    - api_secret: "abcd1234..." (Secret key)
    - paper: True/False (Determines if using paper trading environment)
    """

    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None, paper: Optional[bool] = None):
        """
        Initialize Alpaca API client.

        Args:
            api_key: Alpaca API key
            api_secret: Alpaca API secret
            paper: Whether to use paper trading (default: True)
        """
        # Check if Alpaca SDK is available
        if not ALPACA_AVAILABLE:
            logger.error("Alpaca SDK not available")
            raise Exception(
                "Alpaca SDK not installed. Please run: pip install alpaca-py\n"
                "Or use the installation script: python install_alpaca.py"
            )

        # Allow override of credentials for testing
        self.api_key = api_key or settings.alpaca_api_key
        self.api_secret = api_secret or settings.alpaca_api_secret
        self.paper = paper if paper is not None else settings.alpaca_paper_trading

        # Initialize Alpaca clients
        try:
            # Trading client for orders and account management
            self.trading_client = TradingClient(
                api_key=self.api_key,
                secret_key=self.api_secret,
                paper=self.paper,
                url_override="https://paper-api.alpaca.markets/v2" if self.paper else None
            )

            # Market data client for historical data - crypto data doesn't require auth per Alpaca docs
            # But we'll try with auth first, fallback to no-auth if needed
            try:
                self.data_client = CryptoHistoricalDataClient(
                    api_key=self.api_key,
                    secret_key=self.api_secret,
                    url_override="https://data.alpaca.markets"
                )
            except:
                # Fallback to no-auth client for crypto data
                self.data_client = CryptoHistoricalDataClient()

            # Live data stream for real-time data - same endpoint for both paper and live
            self.data_stream = CryptoDataStream(
                api_key=self.api_key,
                secret_key=self.api_secret,
                url_override="https://stream.data.alpaca.markets"
            )

            logger.info("Alpaca API initialized",
                       paper=self.paper,
                       api_key_preview=self.api_key[:10] + "..." if self.api_key else "None",
                       has_secret=bool(self.api_secret),
                       sdk_available=ALPACA_AVAILABLE)

        except Exception as e:
            logger.error("Failed to initialize Alpaca API", error=str(e))
            raise Exception(f"Alpaca API initialization failed: {str(e)}")

        self.callbacks: Dict[str, List[Callable]] = {}
        self.websocket_active = False
        self.websocket_thread = None

        # Enhanced client temporarily disabled to fix terminal spam
        self.enhanced_client = None
        logger.info("Enhanced client disabled - using standard Alpaca API")

    # Account and Portfolio Methods
    def get_account(self) -> Dict[str, Any]:
        """Get account information."""
        try:
            account = self.trading_client.get_account()
            return {
                "id": account.id,
                "account_number": account.account_number,
                "status": account.status,
                "currency": account.currency,
                "buying_power": float(account.buying_power),
                "cash": float(account.cash),
                "portfolio_value": float(account.portfolio_value),
                "equity": float(account.equity),
                "last_equity": float(account.last_equity),
                "multiplier": float(account.multiplier),
                "day_trade_count": account.day_trade_count,
                "daytrade_buying_power": float(account.daytrade_buying_power),
                "crypto_status": account.crypto_status
            }
        except APIError as e:
            logger.error("Failed to get account", error=str(e))
            raise Exception(f"Account API error: {str(e)}")

    def get_positions(self) -> List[Dict[str, Any]]:
        """Get all positions."""
        try:
            positions = self.trading_client.get_all_positions()
            return [
                {
                    "asset_id": pos.asset_id,
                    "symbol": pos.symbol,
                    "exchange": pos.exchange,
                    "asset_class": pos.asset_class,
                    "qty": float(pos.qty),
                    "side": pos.side,
                    "market_value": float(pos.market_value) if pos.market_value else 0,
                    "cost_basis": float(pos.cost_basis) if pos.cost_basis else 0,
                    "unrealized_pl": float(pos.unrealized_pl) if pos.unrealized_pl else 0,
                    "unrealized_plpc": float(pos.unrealized_plpc) if pos.unrealized_plpc else 0,
                    "current_price": float(pos.current_price) if pos.current_price else 0,
                    "lastday_price": float(pos.lastday_price) if pos.lastday_price else 0,
                    "change_today": float(pos.change_today) if pos.change_today else 0
                }
                for pos in positions
            ]
        except APIError as e:
            logger.error("Failed to get positions", error=str(e))
            return []

    def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get specific position by symbol."""
        try:
            position = self.trading_client.get_open_position(symbol)
            return {
                "asset_id": position.asset_id,
                "symbol": position.symbol,
                "exchange": position.exchange,
                "asset_class": position.asset_class,
                "qty": float(position.qty),
                "side": position.side,
                "market_value": float(position.market_value) if position.market_value else 0,
                "cost_basis": float(position.cost_basis) if position.cost_basis else 0,
                "unrealized_pl": float(position.unrealized_pl) if position.unrealized_pl else 0,
                "unrealized_plpc": float(position.unrealized_plpc) if position.unrealized_plpc else 0,
                "current_price": float(position.current_price) if position.current_price else 0,
                "lastday_price": float(position.lastday_price) if position.lastday_price else 0,
                "change_today": float(position.change_today) if position.change_today else 0
            }
        except APIError as e:
            logger.warning("Position not found", symbol=symbol, error=str(e))
            return None

    # Trading Methods
    def create_market_order(self, symbol: str, side: str, qty: Optional[float] = None, 
                           notional: Optional[float] = None) -> Dict[str, Any]:
        """
        Create a market order.
        
        Args:
            symbol: Trading pair (e.g., "XRP/USD")
            side: "buy" or "sell"
            qty: Quantity of crypto to trade
            notional: Dollar amount to trade (for buy orders)
        """
        try:
            # Convert side to Alpaca enum
            order_side = OrderSide.BUY if side.lower() == "buy" else OrderSide.SELL
            
            # Create market order request
            if qty:
                market_order_data = MarketOrderRequest(
                    symbol=symbol,
                    qty=qty,
                    side=order_side,
                    time_in_force=TimeInForce.GTC
                )
            elif notional:
                market_order_data = MarketOrderRequest(
                    symbol=symbol,
                    notional=notional,
                    side=order_side,
                    time_in_force=TimeInForce.GTC
                )
            else:
                raise ValueError("Either qty or notional must be provided")

            order = self.trading_client.submit_order(order_data=market_order_data)
            
            return {
                "id": order.id,
                "client_order_id": order.client_order_id,
                "symbol": order.symbol,
                "side": order.side,
                "order_type": order.order_type,
                "qty": float(order.qty) if order.qty else None,
                "notional": float(order.notional) if order.notional else None,
                "status": order.status,
                "time_in_force": order.time_in_force,
                "created_at": order.created_at,
                "updated_at": order.updated_at,
                "submitted_at": order.submitted_at,
                "filled_at": order.filled_at,
                "expired_at": order.expired_at,
                "canceled_at": order.canceled_at,
                "failed_at": order.failed_at,
                "replaced_at": order.replaced_at,
                "filled_qty": float(order.filled_qty) if order.filled_qty else 0,
                "filled_avg_price": float(order.filled_avg_price) if order.filled_avg_price else None,
                "order_class": order.order_class,
                "hwm": float(order.hwm) if order.hwm else None,
                "trail_price": float(order.trail_price) if order.trail_price else None,
                "trail_percent": float(order.trail_percent) if order.trail_percent else None
            }
            
        except APIError as e:
            logger.error("Failed to create market order", error=str(e), symbol=symbol, side=side)
            raise Exception(f"Market order failed: {str(e)}")

    def create_limit_order(self, symbol: str, side: str, qty: float, 
                          limit_price: float) -> Dict[str, Any]:
        """
        Create a limit order.
        
        Args:
            symbol: Trading pair (e.g., "XRP/USD")
            side: "buy" or "sell"
            qty: Quantity of crypto to trade
            limit_price: Limit price
        """
        try:
            # Convert side to Alpaca enum
            order_side = OrderSide.BUY if side.lower() == "buy" else OrderSide.SELL
            
            # Create limit order request
            limit_order_data = LimitOrderRequest(
                symbol=symbol,
                qty=qty,
                side=order_side,
                time_in_force=TimeInForce.GTC,
                limit_price=limit_price
            )

            order = self.trading_client.submit_order(order_data=limit_order_data)
            
            return {
                "id": order.id,
                "client_order_id": order.client_order_id,
                "symbol": order.symbol,
                "side": order.side,
                "order_type": order.order_type,
                "qty": float(order.qty) if order.qty else None,
                "limit_price": float(order.limit_price) if order.limit_price else None,
                "status": order.status,
                "time_in_force": order.time_in_force,
                "created_at": order.created_at,
                "updated_at": order.updated_at,
                "filled_qty": float(order.filled_qty) if order.filled_qty else 0,
                "filled_avg_price": float(order.filled_avg_price) if order.filled_avg_price else None
            }
            
        except APIError as e:
            logger.error("Failed to create limit order", error=str(e), symbol=symbol, side=side)
            raise Exception(f"Limit order failed: {str(e)}")

    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order by ID."""
        try:
            self.trading_client.cancel_order_by_id(order_id)
            logger.info("Order canceled successfully", order_id=order_id)
            return True
        except APIError as e:
            logger.error("Failed to cancel order", error=str(e), order_id=order_id)
            return False

    def cancel_all_orders(self) -> bool:
        """Cancel all open orders."""
        try:
            self.trading_client.cancel_orders()
            logger.info("All orders canceled successfully")
            return True
        except APIError as e:
            logger.error("Failed to cancel all orders", error=str(e))
            return False

    def get_orders(self, status: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get orders with optional status filter."""
        try:
            orders = self.trading_client.get_orders(
                status=status,
                limit=limit
            )
            
            return [
                {
                    "id": order.id,
                    "client_order_id": order.client_order_id,
                    "symbol": order.symbol,
                    "side": order.side,
                    "order_type": order.order_type,
                    "qty": float(order.qty) if order.qty else None,
                    "notional": float(order.notional) if order.notional else None,
                    "status": order.status,
                    "time_in_force": order.time_in_force,
                    "limit_price": float(order.limit_price) if order.limit_price else None,
                    "stop_price": float(order.stop_price) if order.stop_price else None,
                    "created_at": order.created_at,
                    "updated_at": order.updated_at,
                    "filled_qty": float(order.filled_qty) if order.filled_qty else 0,
                    "filled_avg_price": float(order.filled_avg_price) if order.filled_avg_price else None
                }
                for order in orders
            ]
        except APIError as e:
            logger.error("Failed to get orders", error=str(e))
            return []

    def get_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get specific order by ID."""
        try:
            order = self.trading_client.get_order_by_id(order_id)
            return {
                "id": order.id,
                "client_order_id": order.client_order_id,
                "symbol": order.symbol,
                "side": order.side,
                "order_type": order.order_type,
                "qty": float(order.qty) if order.qty else None,
                "status": order.status,
                "time_in_force": order.time_in_force,
                "limit_price": float(order.limit_price) if order.limit_price else None,
                "created_at": order.created_at,
                "updated_at": order.updated_at,
                "filled_qty": float(order.filled_qty) if order.filled_qty else 0,
                "filled_avg_price": float(order.filled_avg_price) if order.filled_avg_price else None
            }
        except APIError as e:
            logger.warning("Order not found", order_id=order_id, error=str(e))
            return None

    # Market Data Methods
    def get_current_price(self, symbol: str) -> float:
        """Get current market price for a crypto pair."""
        try:
            # Get latest quote
            request = CryptoLatestQuoteRequest(symbol_or_symbols=[symbol])
            quotes = self.data_client.get_crypto_latest_quote(request)

            if symbol in quotes:
                quote = quotes[symbol]
                # Return mid price (average of bid and ask)
                bid_price = float(quote.bid_price) if quote.bid_price else 0
                ask_price = float(quote.ask_price) if quote.ask_price else 0
                return (bid_price + ask_price) / 2 if bid_price and ask_price else bid_price or ask_price

            logger.warning("No quote data available", symbol=symbol)
            return 0.0

        except APIError as e:
            logger.error("Failed to get current price", error=str(e), symbol=symbol)
            return 0.0

    def get_live_market_data(self) -> Optional[Dict]:
        """Get live market data for all major crypto pairs using enhanced Alpaca API."""
        try:
            if not self.enhanced_client:
                logger.error("Enhanced client not available")
                return None

            # All supported crypto pairs
            symbols = [
                "BTC/USD", "ETH/USD", "XRP/USD", "BCH/USD", "SOL/USD",
                "ADA/USD", "LTC/USD", "LINK/USD", "UNI/USD", "DOT/USD",
                "AVAX/USD", "MATIC/USD", "DOGE/USD"
            ]

            # Get comprehensive market data using enhanced client
            comprehensive_data = self.enhanced_client.get_comprehensive_market_data(symbols)
            bars = comprehensive_data.get('bars', {})

            # Format the data for our application
            formatted_data = {}
            for symbol in symbols:
                if symbol in bars:
                    bar = bars[symbol]
                    formatted_data[symbol] = {
                        "timestamp": bar.get('t', ''),
                        "open": float(bar.get('o', 0)),
                        "high": float(bar.get('h', 0)),
                        "low": float(bar.get('l', 0)),
                        "close": float(bar.get('c', 0)),
                        "volume": float(bar.get('v', 0)),
                        "symbol": symbol,
                        "product_id": symbol
                    }

            logger.info("🚀 Enhanced live market data fetched successfully",
                       symbols_count=len(formatted_data),
                       symbols=list(formatted_data.keys()))
            return {"bars": formatted_data}

        except Exception as e:
            logger.error("❌ Error getting enhanced live market data", error=str(e))
            return None

    def get_historical_data_for_all_pairs(self, timeframe="1Hour", hours=24):
        """Get historical data for all crypto pairs."""
        try:
            # All supported crypto pairs
            crypto_pairs = [
                "BTC/USD", "ETH/USD", "XRP/USD", "BCH/USD", "SOL/USD",
                "ADA/USD", "LTC/USD", "LINK/USD", "UNI/USD", "DOT/USD",
                "AVAX/USD", "MATIC/USD", "DOGE/USD"
            ]

            all_historical_data = {}

            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            for symbol in crypto_pairs:
                try:
                    # Get historical data for this symbol
                    historical_df = self.get_historical_data(symbol, start_time, end_time, timeframe)

                    if not historical_df.empty:
                        # Convert DataFrame to list of dictionaries
                        historical_data = []
                        for _, row in historical_df.iterrows():
                            historical_data.append({
                                "timestamp": row['timestamp'],
                                "open": row['open'],
                                "high": row['high'],
                                "low": row['low'],
                                "close": row['close'],
                                "volume": row['volume'],
                                "symbol": symbol,
                                "product_id": symbol
                            })

                        all_historical_data[symbol] = historical_data
                        logger.info(f"📈 Retrieved {len(historical_data)} historical candles for {symbol}")
                    else:
                        logger.warning(f"⚠️ No historical data for {symbol}")

                except Exception as e:
                    logger.error(f"❌ Error getting historical data for {symbol}: {e}")
                    continue

            if all_historical_data:
                logger.info(f"📊 Successfully retrieved historical data for {len(all_historical_data)} crypto pairs")
                return all_historical_data
            else:
                logger.warning("❌ No historical data retrieved for any crypto pairs")
                return None

        except Exception as e:
            logger.error(f"❌ Error in get_historical_data_for_all_pairs: {e}")
            return None

    def get_live_data_for_symbol(self, symbol: str) -> Optional[Dict]:
        """Get live OHLCV data for a specific symbol using real Alpaca API."""
        try:
            # Convert symbol format (XRP/USD -> XRPUSD)
            alpaca_symbol = symbol.replace('/', '')

            # Get latest bars (OHLCV data) using Alpaca SDK
            from alpaca.data.requests import CryptoBarsRequest
            from alpaca.data.timeframe import TimeFrame
            from datetime import datetime, timedelta

            # Get the most recent 1-minute bar
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=5)  # Get last 5 minutes to ensure we get data

            request = CryptoBarsRequest(
                symbol_or_symbols=[alpaca_symbol],
                timeframe=TimeFrame.Minute,
                start=start_time,
                end=end_time,
                limit=5
            )

            bars_response = self.data_client.get_crypto_bars(request)

            if alpaca_symbol in bars_response and len(bars_response[alpaca_symbol]) > 0:
                # Get the most recent bar
                latest_bar = bars_response[alpaca_symbol][-1]

                live_data = {
                    'timestamp': latest_bar.timestamp,
                    'open': float(latest_bar.open),
                    'high': float(latest_bar.high),
                    'low': float(latest_bar.low),
                    'close': float(latest_bar.close),
                    'volume': float(latest_bar.volume) if latest_bar.volume else 0,
                    'symbol': symbol,
                    'product_id': symbol,
                    'trade_count': getattr(latest_bar, 'trade_count', 0),
                    'vwap': float(getattr(latest_bar, 'vwap', latest_bar.close)) if hasattr(latest_bar, 'vwap') else float(latest_bar.close)
                }

                logger.info(f"📊 Real Alpaca data fetched for {symbol}: ${live_data['close']:.6f}")
                return live_data
            else:
                # Fallback to quote data if no bars available
                logger.warning(f"No bar data available for {symbol}, falling back to quote")
                return self._get_quote_fallback(symbol, alpaca_symbol)

        except Exception as e:
            logger.error(f"Error fetching live bar data for {symbol}: {str(e)}")
            # Try quote fallback
            try:
                alpaca_symbol = symbol.replace('/', '')
                return self._get_quote_fallback(symbol, alpaca_symbol)
            except:
                return None

    def _get_quote_fallback(self, symbol: str, alpaca_symbol: str) -> Optional[Dict]:
        """Fallback to quote data when bar data is not available."""
        try:
            from alpaca.data.requests import CryptoLatestQuoteRequest
            request = CryptoLatestQuoteRequest(symbol_or_symbols=[alpaca_symbol])
            quotes = self.data_client.get_crypto_latest_quote(request)

            if alpaca_symbol in quotes:
                quote = quotes[alpaca_symbol]
                current_time = datetime.now()

                # Create OHLCV data from quote (simplified)
                price = float(quote.bid_price + quote.ask_price) / 2  # Mid price

                live_data = {
                    'timestamp': current_time,
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': 0,  # Quote doesn't have volume
                    'symbol': symbol,
                    'product_id': symbol,
                    'bid_price': float(quote.bid_price),
                    'ask_price': float(quote.ask_price),
                    'spread': float(quote.ask_price - quote.bid_price)
                }

                logger.info(f"📈 Quote fallback data for {symbol}: ${price:.6f}")
                return live_data

            return None

        except Exception as e:
            logger.error(f"Quote fallback failed for {symbol}: {str(e)}")
            return None

    def get_account_info_direct(self) -> Optional[Dict]:
        """Get Alpaca account information using direct API call."""
        try:
            import requests

            # Use your provided account API code
            url = "https://paper-api.alpaca.markets/v2/account"

            headers = {
                "accept": "application/json",
                "APCA-API-KEY-ID": self.api_key,
                "APCA-API-SECRET-KEY": self.api_secret
            }

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                account_data = response.json()
                logger.info("Account info fetched successfully",
                           account_id=account_data.get('id', 'unknown'))
                return account_data
            else:
                logger.error("Failed to get account info",
                           status_code=response.status_code,
                           response=response.text)
                return None

        except Exception as e:
            logger.error("Error getting account info", error=str(e))
            return None

    def get_historical_data(self, symbol: str, start: datetime, end: datetime,
                           timeframe: str = "1Hour") -> pd.DataFrame:
        """
        Get historical candle data using authenticated Alpaca Crypto Bars API.

        Args:
            symbol: Trading pair (e.g., "XRP/USD")
            start: Start datetime
            end: End datetime
            timeframe: Timeframe (1Min, 5Min, 15Min, 30Min, 1Hour, 1Day)

        Returns:
            DataFrame with columns: timestamp, open, high, low, close, volume
        """
        try:
            import requests
            import urllib.parse
            from datetime import timezone

            # Pre-validate symbol availability to avoid 403 errors
            if not self.validate_symbol_availability(symbol):
                logger.warning("⚠️ Symbol not available for historical data, skipping",
                             symbol=symbol)
                return pd.DataFrame()

            # Format symbol for Alpaca API
            if '/' not in symbol:
                if len(symbol) >= 6:
                    base = symbol[:-3]
                    quote = symbol[-3:]
                    alpaca_symbol = f"{base}/{quote}"
                else:
                    alpaca_symbol = symbol
            else:
                alpaca_symbol = symbol

            # Convert timeframe to Alpaca format
            timeframe_map = {
                "1Min": "1Min",
                "5Min": "5Min",
                "15Min": "15Min",
                "30Min": "30Min",
                "1Hour": "1Hour",
                "1Day": "1Day"
            }
            alpaca_timeframe = timeframe_map.get(timeframe, "1Hour")

            # Format dates for API (ISO format with timezone)
            if start.tzinfo is None:
                start = start.replace(tzinfo=timezone.utc)
            if end.tzinfo is None:
                end = end.replace(tzinfo=timezone.utc)

            start_str = start.isoformat()
            end_str = end.isoformat()

            # Build API URL with authentication
            encoded_symbol = urllib.parse.quote(alpaca_symbol)
            url = f"https://data.alpaca.markets/v1beta3/crypto/us/bars"

            params = {
                "symbols": alpaca_symbol,
                "timeframe": alpaca_timeframe,
                "start": start_str,
                "end": end_str,
                "limit": 10000,
                "sort": "asc"
            }

            headers = {
                "accept": "application/json",
                "APCA-API-KEY-ID": self.api_key,
                "APCA-API-SECRET-KEY": self.api_secret
            }

            logger.info("🔄 Fetching historical data from Alpaca",
                       symbol=symbol,
                       alpaca_symbol=alpaca_symbol,
                       timeframe=alpaca_timeframe,
                       start=start_str,
                       end=end_str)

            response = requests.get(url, headers=headers, params=params, timeout=30)

            if response.status_code == 200:
                data = response.json()
                bars = data.get('bars', {})

                if alpaca_symbol not in bars:
                    logger.warning("⚠️ No bar data available",
                                 symbol=symbol,
                                 alpaca_symbol=alpaca_symbol,
                                 available_symbols=list(bars.keys()))
                    return pd.DataFrame()
            elif response.status_code == 403:
                logger.warning("❌ 403 Forbidden - Symbol not available or insufficient permissions",
                             symbol=symbol,
                             alpaca_symbol=alpaca_symbol,
                             response_text=response.text[:200])
                return pd.DataFrame()
            elif response.status_code == 422:
                logger.warning("❌ 422 Unprocessable Entity - Invalid symbol or parameters",
                             symbol=symbol,
                             alpaca_symbol=alpaca_symbol,
                             response_text=response.text[:200])
                return pd.DataFrame()
            else:
                logger.error("❌ Failed to get historical data",
                           symbol=symbol,
                           status_code=response.status_code,
                           response=response.text[:500])
                return pd.DataFrame()

            # Convert to DataFrame (only reached if status_code == 200)
            df_data = []
            for bar in bars[alpaca_symbol]:
                df_data.append({
                    "timestamp": datetime.fromisoformat(bar['t'].replace('Z', '+00:00')),
                    "open": float(bar['o']),
                    "high": float(bar['h']),
                    "low": float(bar['l']),
                    "close": float(bar['c']),
                    "volume": float(bar['v'])
                })

            df = pd.DataFrame(df_data)

            if not df.empty:
                # Sort by timestamp (oldest first)
                df = df.sort_values('timestamp').reset_index(drop=True)

                logger.info("✅ Historical data fetched successfully",
                           symbol=symbol,
                           records=len(df),
                           date_range=f"{df['timestamp'].min()} to {df['timestamp'].max()}")
            else:
                logger.warning("⚠️ Empty DataFrame after processing")

            return df

        except Exception as e:
            logger.error("❌ Unexpected error getting historical data",
                        symbol=symbol, error=str(e))
            return pd.DataFrame()

    def get_assets(self) -> List[Dict[str, Any]]:
        """Get all available crypto assets."""
        try:
            # Get all assets and filter for crypto
            all_assets = self.trading_client.get_all_assets()
            assets = [asset for asset in all_assets if asset.asset_class == "crypto"]
            return [
                {
                    "id": asset.id,
                    "class": asset.asset_class,
                    "exchange": asset.exchange,
                    "symbol": asset.symbol,
                    "name": asset.name,
                    "status": asset.status,
                    "tradable": asset.tradable,
                    "marginable": asset.marginable,
                    "shortable": asset.shortable,
                    "easy_to_borrow": asset.easy_to_borrow,
                    "fractionable": asset.fractionable,
                    "min_order_size": float(asset.min_order_size) if asset.min_order_size else None,
                    "min_trade_increment": float(asset.min_trade_increment) if asset.min_trade_increment else None,
                    "price_increment": float(asset.price_increment) if asset.price_increment else None
                }
                for asset in assets
            ]
        except APIError as e:
            logger.error("Failed to get assets", error=str(e))
            return []

    def get_crypto_pairs(self) -> List[str]:
        """Get all available crypto trading pairs."""
        try:
            assets = self.get_assets()
            # Filter for crypto assets that are tradable
            crypto_pairs = [
                asset["symbol"] for asset in assets
                if asset["class"] == "crypto" and asset["tradable"]
            ]
            logger.info("Available crypto pairs from Alpaca", pairs=crypto_pairs[:10], total=len(crypto_pairs))
            return crypto_pairs
        except Exception as e:
            logger.error("Failed to get crypto pairs", error=str(e))
            return []

    def validate_symbol_availability(self, symbol: str) -> bool:
        """Check if a symbol is available for historical data."""
        try:
            available_pairs = self.get_crypto_pairs()
            is_available = symbol in available_pairs

            if not is_available:
                logger.warning("Symbol not available in Alpaca",
                             symbol=symbol,
                             available_count=len(available_pairs))

            return is_available
        except Exception as e:
            logger.error("Failed to validate symbol availability", symbol=symbol, error=str(e))
            return False

    def test_connection(self) -> bool:
        """Test API connection by fetching account information."""
        try:
            logger.info("Testing Alpaca API connection...",
                       api_key=self.api_key[:10] + "..." if self.api_key else "None",
                       paper=self.paper)

            # Validate credentials first
            if not self.api_key or not self.api_secret:
                logger.error("Missing API credentials",
                           has_key=bool(self.api_key),
                           has_secret=bool(self.api_secret))
                return False

            # Validate API key format - Alpaca keys generally start with 'PK'
            if not self.api_key.startswith('PK'):
                logger.warning("API key doesn't start with 'PK' - this may not be a valid Alpaca API key",
                             api_key_prefix=self.api_key[:6] if len(self.api_key) >= 6 else self.api_key)

            # Log the trading mode for confirmation
            logger.info(f"Alpaca API configured for {'paper' if self.paper else 'live'} trading",
                       api_key_prefix=self.api_key[:6] if len(self.api_key) >= 6 else self.api_key)

            # Try to get account information using SDK first
            try:
                account = self.get_account()
                if account and "id" in account:
                    logger.info("API connection test successful (SDK)",
                               account_id=account["id"],
                               status=account["status"],
                               crypto_status=account.get("crypto_status"),
                               buying_power=account.get("buying_power"),
                               cash=account.get("cash"))
                    return True
            except Exception as sdk_error:
                logger.warning("SDK account call failed, trying direct API call", error=str(sdk_error))

                # Fallback to direct API call
                try:
                    account_direct = self.get_account_info_direct()
                    if account_direct and "id" in account_direct:
                        logger.info("API connection test successful (Direct API)",
                                   account_id=account_direct["id"],
                                   status=account_direct.get("status"),
                                   crypto_status=account_direct.get("crypto_status"),
                                   buying_power=account_direct.get("buying_power"),
                                   cash=account_direct.get("cash"))
                        return True
                    else:
                        logger.error("Direct API call also failed - no account data")
                        return False
                except Exception as direct_error:
                    logger.error("Both SDK and direct API calls failed",
                               sdk_error=str(sdk_error),
                               direct_error=str(direct_error))
                    return False

            logger.error("API connection test failed - no account data")
            return False

        except APIError as e:
            logger.error("Alpaca API error during connection test",
                        error=str(e),
                        error_code=getattr(e, 'code', 'unknown'),
                        status_code=getattr(e, 'status_code', 'unknown'))
            return False
        except Exception as e:
            logger.error("API connection test failed",
                        error=str(e),
                        error_type=type(e).__name__,
                        api_key_format=self.api_key[:10] + "..." if self.api_key else "None")
            return False

    # Utility methods for compatibility with existing code
    def list_products(self) -> Dict[str, Any]:
        """Get all available crypto products (compatibility method)."""
        assets = self.get_assets()
        return {
            "products": [
                {
                    "id": asset["symbol"],
                    "base_currency": asset["symbol"].split("/")[0] if "/" in asset["symbol"] else asset["symbol"],
                    "quote_currency": asset["symbol"].split("/")[1] if "/" in asset["symbol"] else "USD",
                    "status": asset["status"],
                    "tradable": asset["tradable"],
                    "min_size": asset["min_order_size"]
                }
                for asset in assets
            ]
        }

    def get_product_candles(self, product_id: str, start: int, end: int,
                           granularity: str = "ONE_HOUR") -> Dict[str, Any]:
        """
        Get historical candles (compatibility method for Coinbase-style API).

        Args:
            product_id: Trading pair (e.g., "XRP/USD")
            start: Start time as Unix timestamp
            end: End time as Unix timestamp
            granularity: Candle granularity
        """
        try:
            # Convert Unix timestamps to datetime
            start_dt = datetime.fromtimestamp(start)
            end_dt = datetime.fromtimestamp(end)

            # Map Coinbase granularity to Alpaca timeframe
            granularity_map = {
                "ONE_MINUTE": "1Min",
                "FIVE_MINUTE": "5Min",
                "FIFTEEN_MINUTE": "15Min",
                "THIRTY_MINUTE": "30Min",
                "ONE_HOUR": "1Hour",
                "TWO_HOUR": "1Hour",  # Alpaca doesn't have 2-hour, use 1-hour
                "SIX_HOUR": "1Hour",  # Alpaca doesn't have 6-hour, use 1-hour
                "ONE_DAY": "1Day"
            }

            timeframe = granularity_map.get(granularity, "1Hour")
            candles = self.get_historical_data(product_id, start_dt, end_dt, timeframe)

            # Convert to Coinbase-style format
            coinbase_candles = []
            for candle in candles:
                coinbase_candles.append({
                    "start": str(int(candle["timestamp"].timestamp())),
                    "low": str(candle["low"]),
                    "high": str(candle["high"]),
                    "open": str(candle["open"]),
                    "close": str(candle["close"]),
                    "volume": str(candle["volume"])
                })

            return {"candles": coinbase_candles}

        except Exception as e:
            logger.error("Failed to get product candles", error=str(e), product_id=product_id)
            return {"candles": []}

    # Enhanced Alpaca SDK Methods using official request structures
    def get_crypto_bars_sdk(self, symbols: List[str], timeframe: str = "1Hour",
                           start: Optional[datetime] = None, end: Optional[datetime] = None,
                           limit: Optional[int] = None, sort: str = "asc") -> Dict[str, List[Dict]]:
        """
        Get crypto bars using official Alpaca SDK CryptoBarsRequest.

        Args:
            symbols: List of trading pairs (e.g., ["XRP/USD", "BTC/USD"])
            timeframe: Data timeframe ("1Min", "5Min", "15Min", "1Hour", "1Day")
            start: Start datetime (timezone naive assumed UTC)
            end: End datetime (timezone naive assumed UTC)
            limit: Maximum number of bars to return
            sort: Sort order ("asc" or "desc")

        Returns:
            Dictionary with symbol as key and list of bar data as value
        """
        try:
            # Map timeframe strings to Alpaca TimeFrame objects
            timeframe_map = {
                "1Min": TimeFrame.Minute,
                "5Min": TimeFrame(5, "Min"),
                "15Min": TimeFrame(15, "Min"),
                "30Min": TimeFrame(30, "Min"),
                "1Hour": TimeFrame.Hour,
                "1Day": TimeFrame.Day
            }

            if timeframe not in timeframe_map:
                raise ValueError(f"Unsupported timeframe: {timeframe}")

            # Create bars request using official Alpaca SDK
            request = CryptoBarsRequest(
                symbol_or_symbols=symbols,
                timeframe=timeframe_map[timeframe],
                start=start,
                end=end,
                limit=limit,
                sort=Sort.ASC if sort.lower() == "asc" else Sort.DESC
            )

            # Get bars using the data client
            bars_response = self.data_client.get_crypto_bars(request)

            # Convert to our standard format
            result = {}
            for symbol in symbols:
                if symbol in bars_response:
                    result[symbol] = []
                    for bar in bars_response[symbol]:
                        result[symbol].append({
                            "timestamp": bar.timestamp,
                            "open": float(bar.open),
                            "high": float(bar.high),
                            "low": float(bar.low),
                            "close": float(bar.close),
                            "volume": float(bar.volume) if bar.volume else 0,
                            "trade_count": getattr(bar, 'trade_count', 0),
                            "vwap": float(getattr(bar, 'vwap', 0)) if hasattr(bar, 'vwap') else 0
                        })
                else:
                    result[symbol] = []

            logger.info("Crypto bars fetched successfully using SDK",
                       symbols=symbols,
                       timeframe=timeframe,
                       total_bars=sum(len(bars) for bars in result.values()))

            return result

        except APIError as e:
            logger.error("Failed to get crypto bars", error=str(e), symbols=symbols)
            return {symbol: [] for symbol in symbols}
        except Exception as e:
            logger.error("Unexpected error getting crypto bars", error=str(e), symbols=symbols)
            return {symbol: [] for symbol in symbols}

    def get_crypto_quotes_sdk(self, symbols: List[str],
                             start: Optional[datetime] = None, end: Optional[datetime] = None,
                             limit: Optional[int] = None, sort: str = "asc") -> Dict[str, List[Dict]]:
        """
        Get crypto quotes using official Alpaca SDK CryptoQuoteRequest.

        Args:
            symbols: List of trading pairs (e.g., ["XRP/USD", "BTC/USD"])
            start: Start datetime (timezone naive assumed UTC)
            end: End datetime (timezone naive assumed UTC)
            limit: Maximum number of quotes to return
            sort: Sort order ("asc" or "desc")

        Returns:
            Dictionary with symbol as key and list of quote data as value
        """
        try:
            # Create quotes request using official Alpaca SDK
            request = CryptoQuoteRequest(
                symbol_or_symbols=symbols,
                start=start,
                end=end,
                limit=limit,
                sort=Sort.ASC if sort.lower() == "asc" else Sort.DESC
            )

            # Get quotes using the data client
            quotes_response = self.data_client.get_crypto_quotes(request)

            # Convert to our standard format
            result = {}
            for symbol in symbols:
                if symbol in quotes_response:
                    result[symbol] = []
                    for quote in quotes_response[symbol]:
                        result[symbol].append({
                            "timestamp": quote.timestamp,
                            "bid_price": float(quote.bid_price) if quote.bid_price else 0,
                            "bid_size": float(quote.bid_size) if quote.bid_size else 0,
                            "ask_price": float(quote.ask_price) if quote.ask_price else 0,
                            "ask_size": float(quote.ask_size) if quote.ask_size else 0,
                            "exchange": getattr(quote, 'exchange', ''),
                        })
                else:
                    result[symbol] = []

            logger.info("Crypto quotes fetched successfully using SDK",
                       symbols=symbols,
                       total_quotes=sum(len(quotes) for quotes in result.values()))

            return result

        except APIError as e:
            logger.error("Failed to get crypto quotes", error=str(e), symbols=symbols)
            return {symbol: [] for symbol in symbols}
        except Exception as e:
            logger.error("Unexpected error getting crypto quotes", error=str(e), symbols=symbols)
            return {symbol: [] for symbol in symbols}

    def get_crypto_trades_sdk(self, symbols: List[str],
                             start: Optional[datetime] = None, end: Optional[datetime] = None,
                             limit: Optional[int] = None, sort: str = "asc") -> Dict[str, List[Dict]]:
        """
        Get crypto trades using official Alpaca SDK CryptoTradesRequest.

        Args:
            symbols: List of trading pairs (e.g., ["XRP/USD", "BTC/USD"])
            start: Start datetime (timezone naive assumed UTC)
            end: End datetime (timezone naive assumed UTC)
            limit: Maximum number of trades to return
            sort: Sort order ("asc" or "desc")

        Returns:
            Dictionary with symbol as key and list of trade data as value
        """
        try:
            # Create trades request using official Alpaca SDK
            request = CryptoTradesRequest(
                symbol_or_symbols=symbols,
                start=start,
                end=end,
                limit=limit,
                sort=Sort.ASC if sort.lower() == "asc" else Sort.DESC
            )

            # Get trades using the data client
            trades_response = self.data_client.get_crypto_trades(request)

            # Convert to our standard format
            result = {}
            for symbol in symbols:
                if symbol in trades_response:
                    result[symbol] = []
                    for trade in trades_response[symbol]:
                        result[symbol].append({
                            "timestamp": trade.timestamp,
                            "price": float(trade.price) if trade.price else 0,
                            "size": float(trade.size) if trade.size else 0,
                            "exchange": getattr(trade, 'exchange', ''),
                            "conditions": getattr(trade, 'conditions', []),
                            "id": getattr(trade, 'id', ''),
                            "taker_side": getattr(trade, 'taker_side', '')
                        })
                else:
                    result[symbol] = []

            logger.info("Crypto trades fetched successfully using SDK",
                       symbols=symbols,
                       total_trades=sum(len(trades) for trades in result.values()))

            return result

        except APIError as e:
            logger.error("Failed to get crypto trades", error=str(e), symbols=symbols)
            return {symbol: [] for symbol in symbols}
        except Exception as e:
            logger.error("Unexpected error getting crypto trades", error=str(e), symbols=symbols)
            return {symbol: [] for symbol in symbols}

    def get_crypto_latest_quotes_sdk(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        Get latest crypto quotes using official Alpaca SDK CryptoLatestQuoteRequest.

        Args:
            symbols: List of trading pairs (e.g., ["XRP/USD", "BTC/USD"])

        Returns:
            Dictionary with symbol as key and latest quote data as value
        """
        try:
            # Create latest quotes request using official Alpaca SDK
            request = CryptoLatestQuoteRequest(symbol_or_symbols=symbols)

            # Get latest quotes using the data client
            quotes_response = self.data_client.get_crypto_latest_quote(request)

            # Convert to our standard format
            result = {}
            for symbol in symbols:
                if symbol in quotes_response:
                    quote = quotes_response[symbol]
                    result[symbol] = {
                        "timestamp": quote.timestamp,
                        "bid_price": float(quote.bid_price) if quote.bid_price else 0,
                        "bid_size": float(quote.bid_size) if quote.bid_size else 0,
                        "ask_price": float(quote.ask_price) if quote.ask_price else 0,
                        "ask_size": float(quote.ask_size) if quote.ask_size else 0,
                        "exchange": getattr(quote, 'exchange', ''),
                        "mid_price": (float(quote.bid_price) + float(quote.ask_price)) / 2 if quote.bid_price and quote.ask_price else 0
                    }
                else:
                    result[symbol] = {}

            logger.info("Latest crypto quotes fetched successfully using SDK",
                       symbols=symbols,
                       quotes_count=len([q for q in result.values() if q]))

            return result

        except APIError as e:
            logger.error("Failed to get latest crypto quotes", error=str(e), symbols=symbols)
            return {symbol: {} for symbol in symbols}
        except Exception as e:
            logger.error("Unexpected error getting latest crypto quotes", error=str(e), symbols=symbols)
            return {symbol: {} for symbol in symbols}

    def get_crypto_latest_trades_sdk(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        Get latest crypto trades using official Alpaca SDK CryptoLatestTradeRequest.

        Args:
            symbols: List of trading pairs (e.g., ["XRP/USD", "BTC/USD"])

        Returns:
            Dictionary with symbol as key and latest trade data as value
        """
        try:
            # Create latest trades request using official Alpaca SDK
            request = CryptoLatestTradeRequest(symbol_or_symbols=symbols)

            # Get latest trades using the data client
            trades_response = self.data_client.get_crypto_latest_trade(request)

            # Convert to our standard format
            result = {}
            for symbol in symbols:
                if symbol in trades_response:
                    trade = trades_response[symbol]
                    result[symbol] = {
                        "timestamp": trade.timestamp,
                        "price": float(trade.price) if trade.price else 0,
                        "size": float(trade.size) if trade.size else 0,
                        "exchange": getattr(trade, 'exchange', ''),
                        "conditions": getattr(trade, 'conditions', []),
                        "id": getattr(trade, 'id', ''),
                        "taker_side": getattr(trade, 'taker_side', '')
                    }
                else:
                    result[symbol] = {}

            logger.info("Latest crypto trades fetched successfully using SDK",
                       symbols=symbols,
                       trades_count=len([t for t in result.values() if t]))

            return result

        except APIError as e:
            logger.error("Failed to get latest crypto trades", error=str(e), symbols=symbols)
            return {symbol: {} for symbol in symbols}
        except Exception as e:
            logger.error("Unexpected error getting latest crypto trades", error=str(e), symbols=symbols)
            return {symbol: {} for symbol in symbols}

    def get_crypto_snapshots_sdk(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        Get crypto snapshots using official Alpaca SDK CryptoSnapshotRequest.

        Args:
            symbols: List of trading pairs (e.g., ["XRP/USD", "BTC/USD"])

        Returns:
            Dictionary with symbol as key and snapshot data as value
        """
        try:
            # Create snapshots request using official Alpaca SDK
            request = CryptoSnapshotRequest(symbol_or_symbols=symbols)

            # Get snapshots using the data client
            snapshots_response = self.data_client.get_crypto_snapshot(request)

            # Convert to our standard format
            result = {}
            for symbol in symbols:
                if symbol in snapshots_response:
                    snapshot = snapshots_response[symbol]
                    result[symbol] = {
                        "symbol": symbol,
                        "latest_quote": {
                            "timestamp": snapshot.latest_quote.timestamp if snapshot.latest_quote else None,
                            "bid_price": float(snapshot.latest_quote.bid_price) if snapshot.latest_quote and snapshot.latest_quote.bid_price else 0,
                            "bid_size": float(snapshot.latest_quote.bid_size) if snapshot.latest_quote and snapshot.latest_quote.bid_size else 0,
                            "ask_price": float(snapshot.latest_quote.ask_price) if snapshot.latest_quote and snapshot.latest_quote.ask_price else 0,
                            "ask_size": float(snapshot.latest_quote.ask_size) if snapshot.latest_quote and snapshot.latest_quote.ask_size else 0,
                        } if snapshot.latest_quote else {},
                        "latest_trade": {
                            "timestamp": snapshot.latest_trade.timestamp if snapshot.latest_trade else None,
                            "price": float(snapshot.latest_trade.price) if snapshot.latest_trade and snapshot.latest_trade.price else 0,
                            "size": float(snapshot.latest_trade.size) if snapshot.latest_trade and snapshot.latest_trade.size else 0,
                        } if snapshot.latest_trade else {},
                        "minute_bar": {
                            "timestamp": snapshot.minute_bar.timestamp if snapshot.minute_bar else None,
                            "open": float(snapshot.minute_bar.open) if snapshot.minute_bar and snapshot.minute_bar.open else 0,
                            "high": float(snapshot.minute_bar.high) if snapshot.minute_bar and snapshot.minute_bar.high else 0,
                            "low": float(snapshot.minute_bar.low) if snapshot.minute_bar and snapshot.minute_bar.low else 0,
                            "close": float(snapshot.minute_bar.close) if snapshot.minute_bar and snapshot.minute_bar.close else 0,
                            "volume": float(snapshot.minute_bar.volume) if snapshot.minute_bar and snapshot.minute_bar.volume else 0,
                        } if snapshot.minute_bar else {},
                        "daily_bar": {
                            "timestamp": snapshot.daily_bar.timestamp if snapshot.daily_bar else None,
                            "open": float(snapshot.daily_bar.open) if snapshot.daily_bar and snapshot.daily_bar.open else 0,
                            "high": float(snapshot.daily_bar.high) if snapshot.daily_bar and snapshot.daily_bar.high else 0,
                            "low": float(snapshot.daily_bar.low) if snapshot.daily_bar and snapshot.daily_bar.low else 0,
                            "close": float(snapshot.daily_bar.close) if snapshot.daily_bar and snapshot.daily_bar.close else 0,
                            "volume": float(snapshot.daily_bar.volume) if snapshot.daily_bar and snapshot.daily_bar.volume else 0,
                        } if snapshot.daily_bar else {},
                        "prev_daily_bar": {
                            "timestamp": snapshot.prev_daily_bar.timestamp if snapshot.prev_daily_bar else None,
                            "open": float(snapshot.prev_daily_bar.open) if snapshot.prev_daily_bar and snapshot.prev_daily_bar.open else 0,
                            "high": float(snapshot.prev_daily_bar.high) if snapshot.prev_daily_bar and snapshot.prev_daily_bar.high else 0,
                            "low": float(snapshot.prev_daily_bar.low) if snapshot.prev_daily_bar and snapshot.prev_daily_bar.low else 0,
                            "close": float(snapshot.prev_daily_bar.close) if snapshot.prev_daily_bar and snapshot.prev_daily_bar.close else 0,
                            "volume": float(snapshot.prev_daily_bar.volume) if snapshot.prev_daily_bar and snapshot.prev_daily_bar.volume else 0,
                        } if snapshot.prev_daily_bar else {}
                    }
                else:
                    result[symbol] = {}

            logger.info("Crypto snapshots fetched successfully using SDK",
                       symbols=symbols,
                       snapshots_count=len([s for s in result.values() if s]))

            return result

        except APIError as e:
            logger.error("Failed to get crypto snapshots", error=str(e), symbols=symbols)
            return {symbol: {} for symbol in symbols}
        except Exception as e:
            logger.error("Unexpected error getting crypto snapshots", error=str(e), symbols=symbols)
            return {symbol: {} for symbol in symbols}

    def start_realtime_stream(self, symbols: List[str], callback: Callable):
        """Start real-time WebSocket streaming for live candlestick updates."""
        try:
            import threading
            import websocket
            import json

            def on_message(ws, message):
                try:
                    data = json.loads(message)
                    if data.get('T') == 'b':  # Bar/candlestick data
                        # Format the data for our callback
                        formatted_data = {
                            "symbol": data.get('S'),
                            "timestamp": datetime.fromisoformat(data.get('t', '').replace('Z', '+00:00')),
                            "open": float(data.get('o', 0)),
                            "high": float(data.get('h', 0)),
                            "low": float(data.get('l', 0)),
                            "close": float(data.get('c', 0)),
                            "volume": float(data.get('v', 0))
                        }
                        callback(formatted_data)
                        logger.info("📊 Real-time bar received",
                                   symbol=formatted_data["symbol"],
                                   price=formatted_data["close"])
                except Exception as e:
                    logger.error("Error processing WebSocket message", error=str(e))

            def on_error(ws, error):
                logger.error("WebSocket error", error=str(error))

            def on_close(ws, close_status_code, close_msg):
                logger.info("WebSocket connection closed")
                self.websocket_active = False

            def on_open(ws):
                logger.info("🚀 WebSocket connection opened")
                self.websocket_active = True

                # Authenticate first
                auth_message = {
                    "action": "auth",
                    "key": self.api_key,
                    "secret": self.api_secret
                }
                ws.send(json.dumps(auth_message))

                # Subscribe to real-time bars for all symbols
                subscribe_message = {
                    "action": "subscribe",
                    "bars": symbols
                }
                ws.send(json.dumps(subscribe_message))
                logger.info("📡 Subscribed to real-time bars", symbols=symbols)

            # Create WebSocket connection
            ws_url = "wss://stream.data.alpaca.markets/v1beta3/crypto/us"

            # Create WebSocket
            def create_websocket():
                ws = websocket.WebSocketApp(
                    ws_url,
                    on_message=on_message,
                    on_error=on_error,
                    on_close=on_close,
                    on_open=on_open
                )
                return ws

            # Start WebSocket in a separate thread
            def run_websocket():
                ws = create_websocket()
                ws.run_forever()

            self.websocket_thread = threading.Thread(target=run_websocket, daemon=True)
            self.websocket_thread.start()

            logger.info("✅ Real-time WebSocket stream started", symbols=symbols)
            return True

        except Exception as e:
            logger.error("❌ Failed to start real-time stream", error=str(e))
            return False

    def stop_realtime_stream(self):
        """Stop the real-time WebSocket stream."""
        try:
            self.websocket_active = False
            if self.websocket_thread and self.websocket_thread.is_alive():
                logger.info("🛑 Stopping real-time stream")
            return True
        except Exception as e:
            logger.error("❌ Error stopping real-time stream", error=str(e))
            return False
